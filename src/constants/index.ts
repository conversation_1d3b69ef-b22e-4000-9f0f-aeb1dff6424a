/**
 * 应用常量定义
 * <AUTHOR> CPQ Team
 */

// API相关常量
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },
  USERS: {
    LIST: '/users',
    DETAIL: '/users/:id',
    CREATE: '/users',
    UPDATE: '/users/:id',
    DELETE: '/users/:id',
    ACTIVATE: '/users/:id/activate',
    DEACTIVATE: '/users/:id/deactivate',
  },
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products/:id',
    CREATE: '/products',
    UPDATE: '/products/:id',
    DELETE: '/products/:id',
    SEARCH: '/products/search',
    CATEGORIES: '/products/categories',
  },
  QUOTES: {
    LIST: '/quotes',
    DETAIL: '/quotes/:id',
    CREATE: '/quotes',
    UPDATE: '/quotes/:id',
    DELETE: '/quotes/:id',
    APPROVE: '/quotes/:id/approve',
    REJECT: '/quotes/:id/reject',
    EXPORT: '/quotes/:id/export',
  },
  CONFIGURATIONS: {
    LIST: '/configurations',
    DETAIL: '/configurations/:id',
    CREATE: '/configurations',
    UPDATE: '/configurations/:id',
    DELETE: '/configurations/:id',
    VALIDATE: '/configurations/validate',
  },
  PRICING: {
    CALCULATE: '/pricing/calculate',
    RULES: '/pricing/rules',
    DISCOUNTS: '/pricing/discounts',
  },
  ANALYTICS: {
    DASHBOARD: '/analytics/dashboard',
    SALES: '/analytics/sales',
    PRODUCTS: '/analytics/products',
    CUSTOMERS: '/analytics/customers',
  },
} as const;

// 用户状态
export const USER_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  LOCKED: 'LOCKED',
} as const;

// 用户角色
export const USER_ROLES = {
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  SALES: 'SALES',
  CUSTOMER: 'CUSTOMER',
  VIEWER: 'VIEWER',
} as const;

// 产品状态
export const PRODUCT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DRAFT: 'DRAFT',
  DISCONTINUED: 'DISCONTINUED',
} as const;

// 报价状态
export const QUOTE_STATUS = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED',
  CONVERTED: 'CONVERTED',
} as const;

// 配置状态
export const CONFIGURATION_STATUS = {
  DRAFT: 'DRAFT',
  VALID: 'VALID',
  INVALID: 'INVALID',
  ARCHIVED: 'ARCHIVED',
} as const;

// 货币类型
export const CURRENCIES = {
  CNY: 'CNY',
  USD: 'USD',
  EUR: 'EUR',
  GBP: 'GBP',
  JPY: 'JPY',
} as const;

// 分页默认值
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 1000,
} as const;

// 文件上传限制
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: {
    IMAGES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    DOCUMENTS: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
    MODELS: ['obj', 'fbx', 'gltf', 'glb', '3ds'],
    ALL: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'obj', 'fbx', 'gltf', 'glb', '3ds'],
  },
} as const;

// 主题配置
export const THEME = {
  COLORS: {
    PRIMARY: '#1890ff',
    SUCCESS: '#52c41a',
    WARNING: '#faad14',
    ERROR: '#ff4d4f',
    INFO: '#1890ff',
  },
  BREAKPOINTS: {
    XS: 480,
    SM: 576,
    MD: 768,
    LG: 992,
    XL: 1200,
    XXL: 1600,
  },
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
  RECENT_SEARCHES: 'recent_searches',
  PREFERENCES: 'user_preferences',
} as const;

// 路由路径
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  PRODUCTS: '/products',
  PRODUCT_DETAIL: '/products/:id',
  CONFIGURATOR: '/configurator',
  BUNDLES: '/bundles',
  PRICING: '/pricing',
  QUOTES: '/quotes',
  QUOTE_DETAIL: '/quotes/:id',
  CUSTOMER_PORTAL: '/customer-portal',
  ANALYTICS: '/analytics',
  USERS: '/users',
  INTEGRATION: '/integration',
} as const;

// 权限定义
export const PERMISSIONS = {
  USER: {
    READ: 'user:read',
    CREATE: 'user:create',
    UPDATE: 'user:update',
    DELETE: 'user:delete',
  },
  PRODUCT: {
    READ: 'product:read',
    CREATE: 'product:create',
    UPDATE: 'product:update',
    DELETE: 'product:delete',
  },
  QUOTE: {
    READ: 'quote:read',
    CREATE: 'quote:create',
    UPDATE: 'quote:update',
    DELETE: 'quote:delete',
    APPROVE: 'quote:approve',
  },
  CONFIGURATION: {
    READ: 'configuration:read',
    CREATE: 'configuration:create',
    UPDATE: 'configuration:update',
    DELETE: 'configuration:delete',
  },
  PRICING: {
    READ: 'pricing:read',
    UPDATE: 'pricing:update',
    MANAGE: 'pricing:manage',
  },
  ANALYTICS: {
    READ: 'analytics:read',
    EXPORT: 'analytics:export',
  },
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接错误，请检查网络设置',
  UNAUTHORIZED: '未授权访问，请重新登录',
  FORBIDDEN: '权限不足，无法执行此操作',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  VALIDATION_ERROR: '数据验证失败，请检查输入',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请联系管理员',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  SAVE_SUCCESS: '保存成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  CREATE_SUCCESS: '创建成功',
  COPY_SUCCESS: '复制成功',
  EXPORT_SUCCESS: '导出成功',
  IMPORT_SUCCESS: '导入成功',
} as const;

// 日期格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
  DISPLAY_DATE: 'YYYY年MM月DD日',
  DISPLAY_DATETIME: 'YYYY年MM月DD日 HH:mm',
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  SKU: /^[A-Z0-9-]{3,50}$/,
  PRICE: /^\d+(\.\d{1,2})?$/,
} as const;

// 配置选项类型
export const CONFIGURATION_TYPES = {
  SELECT: 'SELECT',
  MULTI_SELECT: 'MULTI_SELECT',
  INPUT: 'INPUT',
  NUMBER: 'NUMBER',
  BOOLEAN: 'BOOLEAN',
  COLOR: 'COLOR',
  SLIDER: 'SLIDER',
  DATE: 'DATE',
} as const;

// 3D模型格式
export const MODEL_FORMATS = {
  GLTF: 'gltf',
  GLB: 'glb',
  OBJ: 'obj',
  FBX: 'fbx',
  THREE_DS: '3ds',
} as const;

// 导出格式
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  EXCEL: 'xlsx',
  CSV: 'csv',
  JSON: 'json',
  XML: 'xml',
} as const;
