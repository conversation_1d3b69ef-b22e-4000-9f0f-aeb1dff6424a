// 金蝶软件报价专用数据结构
export interface SoftwareProduct {
  id: string;
  name: string;
  code: string;
  description: string;
  category: 'financial' | 'erp' | 'crm' | 'hr' | 'oa';
  targetCustomer: string[];
  basePrice: number;
  priceUnit: 'user/year' | 'license/year' | 'enterprise/year';
  versions: SoftwareVersion[];
  modules: SoftwareModule[];
  services: SoftwareService[];
  deploymentOptions: DeploymentOption[];
  supportLevels: SupportLevel[];
}

export interface SoftwareVersion {
  id: string;
  name: string;
  description: string;
  features: string[];
  maxUsers: number;
  priceMultiplier: number; // 基于基础价格的倍数
  recommended: boolean;
}

export interface SoftwareModule {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  priceType: 'fixed' | 'per_user' | 'percentage';
  required: boolean;
  dependencies?: string[]; // 依赖的其他模块
}

export interface SoftwareService {
  id: string;
  name: string;
  description: string;
  type: 'implementation' | 'training' | 'customization' | 'maintenance';
  price: number;
  duration: string;
  deliverables: string[];
}

export interface DeploymentOption {
  id: string;
  name: string;
  description: string;
  type: 'cloud' | 'on_premise' | 'hybrid';
  priceImpact: number; // 价格影响（正数为加价，负数为减价）
  features: string[];
}

export interface SupportLevel {
  id: string;
  name: string;
  description: string;
  responseTime: string;
  channels: string[];
  price: number;
  priceType: 'fixed' | 'percentage';
}

// 金蝶云星辰产品定义
export const kisCloudProduct: SoftwareProduct = {
  id: 'kis-cloud',
  name: '金蝶云星辰',
  code: 'KIS-CLOUD',
  description: '专为小微企业设计的云端财务管理软件',
  category: 'financial',
  targetCustomer: ['小微企业', '个体工商户', '初创企业'],
  basePrice: 1980,
  priceUnit: 'user/year',
  versions: [
    {
      id: 'starter',
      name: '入门版',
      description: '基础财务功能，适合初创企业',
      features: ['基础总账', '简单报表', '凭证管理'],
      maxUsers: 1,
      priceMultiplier: 0.8,
      recommended: false,
    },
    {
      id: 'standard',
      name: '标准版',
      description: '完整财务功能，适合小微企业',
      features: ['完整总账', '应收应付', '固定资产', '财务报表', '现金流量表'],
      maxUsers: 5,
      priceMultiplier: 1.0,
      recommended: true,
    },
    {
      id: 'professional',
      name: '专业版',
      description: '高级财务功能，支持多账套',
      features: ['多账套管理', '成本核算', '预算管理', '自定义报表', '数据分析'],
      maxUsers: 20,
      priceMultiplier: 1.5,
      recommended: false,
    },
  ],
  modules: [
    {
      id: 'general_ledger',
      name: '总账管理',
      description: '完整的总账核算功能',
      category: '核心模块',
      price: 0,
      priceType: 'fixed',
      required: true,
    },
    {
      id: 'ar_ap',
      name: '应收应付',
      description: '应收账款和应付账款管理',
      category: '核心模块',
      price: 500,
      priceType: 'per_user',
      required: false,
    },
    {
      id: 'fixed_assets',
      name: '固定资产',
      description: '固定资产管理和折旧计算',
      category: '扩展模块',
      price: 800,
      priceType: 'fixed',
      required: false,
    },
    {
      id: 'cash_management',
      name: '出纳管理',
      description: '现金和银行存款管理',
      category: '扩展模块',
      price: 300,
      priceType: 'per_user',
      required: false,
    },
    {
      id: 'cost_accounting',
      name: '成本核算',
      description: '产品成本计算和分析',
      category: '高级模块',
      price: 1200,
      priceType: 'fixed',
      required: false,
      dependencies: ['general_ledger'],
    },
  ],
  services: [
    {
      id: 'basic_implementation',
      name: '基础实施服务',
      description: '软件安装配置和基础培训',
      type: 'implementation',
      price: 2000,
      duration: '5个工作日',
      deliverables: ['系统安装', '基础配置', '用户培训', '上线支持'],
    },
    {
      id: 'data_migration',
      name: '数据迁移服务',
      description: '从旧系统迁移数据',
      type: 'implementation',
      price: 3000,
      duration: '3个工作日',
      deliverables: ['数据分析', '数据清洗', '数据导入', '数据验证'],
    },
    {
      id: 'advanced_training',
      name: '高级培训服务',
      description: '深度业务培训和最佳实践',
      type: 'training',
      price: 1500,
      duration: '2天',
      deliverables: ['现场培训', '培训材料', '操作手册', '后续答疑'],
    },
  ],
  deploymentOptions: [
    {
      id: 'cloud',
      name: '云端部署',
      description: '金蝶云平台托管，免维护',
      type: 'cloud',
      priceImpact: 0,
      features: ['自动备份', '弹性扩容', '7x24监控', '自动更新'],
    },
    {
      id: 'private_cloud',
      name: '私有云部署',
      description: '独立云环境，更高安全性',
      type: 'cloud',
      priceImpact: 2000,
      features: ['独立环境', '数据隔离', '定制配置', '专属支持'],
    },
  ],
  supportLevels: [
    {
      id: 'standard',
      name: '标准支持',
      description: '工作日在线支持',
      responseTime: '4小时内响应',
      channels: ['在线客服', '电话支持', '邮件支持'],
      price: 0,
      priceType: 'fixed',
    },
    {
      id: 'premium',
      name: '高级支持',
      description: '7x12小时专业支持',
      responseTime: '2小时内响应',
      channels: ['专属客服', '电话支持', '远程协助', '现场支持'],
      price: 1000,
      priceType: 'fixed',
    },
    {
      id: 'enterprise',
      name: '企业支持',
      description: '7x24小时专属支持',
      responseTime: '1小时内响应',
      channels: ['专属经理', '技术专家', '现场支持', '定制开发'],
      price: 3000,
      priceType: 'fixed',
    },
  ],
};

// 金蝶精斗云产品定义
export const jdyProduct: SoftwareProduct = {
  id: 'jdy',
  name: '金蝶精斗云',
  code: 'JDY',
  description: '一体化云端企业管理软件，集成多业务模块',
  category: 'erp',
  targetCustomer: ['中小企业', '成长型企业', '连锁企业'],
  basePrice: 2980,
  priceUnit: 'user/year',
  versions: [
    {
      id: 'basic',
      name: '基础版',
      description: '进销存+基础财务',
      features: ['进销存管理', '基础财务', '库存管理', '销售管理'],
      maxUsers: 10,
      priceMultiplier: 0.8,
      recommended: false,
    },
    {
      id: 'professional',
      name: '专业版',
      description: '完整业务管理功能',
      features: ['进销存', '财务管理', 'CRM', '项目管理', '报表分析'],
      maxUsers: 50,
      priceMultiplier: 1.0,
      recommended: true,
    },
    {
      id: 'enterprise',
      name: '企业版',
      description: '全功能企业管理平台',
      features: ['全业务模块', '工作流', 'BI分析', 'API接口', '移动应用'],
      maxUsers: 200,
      priceMultiplier: 1.8,
      recommended: false,
    },
  ],
  modules: [
    {
      id: 'inventory',
      name: '进销存管理',
      description: '采购、销售、库存一体化管理',
      category: '核心模块',
      price: 0,
      priceType: 'fixed',
      required: true,
    },
    {
      id: 'finance',
      name: '财务管理',
      description: '完整的财务核算功能',
      category: '核心模块',
      price: 800,
      priceType: 'per_user',
      required: false,
    },
    {
      id: 'crm',
      name: 'CRM客户管理',
      description: '客户关系管理和销售跟进',
      category: '业务模块',
      price: 600,
      priceType: 'per_user',
      required: false,
    },
    {
      id: 'project',
      name: '项目管理',
      description: '项目进度和成本管理',
      category: '业务模块',
      price: 500,
      priceType: 'per_user',
      required: false,
    },
    {
      id: 'hr',
      name: '人事管理',
      description: '员工信息和考勤管理',
      category: '扩展模块',
      price: 400,
      priceType: 'per_user',
      required: false,
    },
  ],
  services: [
    {
      id: 'standard_implementation',
      name: '标准实施服务',
      description: '完整的系统实施和培训',
      type: 'implementation',
      price: 5000,
      duration: '10个工作日',
      deliverables: ['需求调研', '系统配置', '数据导入', '用户培训', '上线支持'],
    },
    {
      id: 'business_consulting',
      name: '业务咨询服务',
      description: '业务流程优化和最佳实践',
      type: 'customization',
      price: 8000,
      duration: '15个工作日',
      deliverables: ['流程梳理', '方案设计', '系统配置', '培训指导'],
    },
  ],
  deploymentOptions: [
    {
      id: 'saas',
      name: 'SaaS云服务',
      description: '标准云服务，快速上线',
      type: 'cloud',
      priceImpact: 0,
      features: ['即开即用', '弹性付费', '自动更新', '数据安全'],
    },
    {
      id: 'dedicated_cloud',
      name: '专属云服务',
      description: '独立云环境，定制配置',
      type: 'cloud',
      priceImpact: 5000,
      features: ['独立环境', '定制功能', '专属支持', '数据隔离'],
    },
  ],
  supportLevels: [
    {
      id: 'standard',
      name: '标准支持',
      description: '工作日支持服务',
      responseTime: '4小时内响应',
      channels: ['在线客服', '电话支持'],
      price: 0,
      priceType: 'fixed',
    },
    {
      id: 'premium',
      name: '高级支持',
      description: '扩展时间支持服务',
      responseTime: '2小时内响应',
      channels: ['专属客服', '电话支持', '远程协助'],
      price: 2000,
      priceType: 'fixed',
    },
  ],
};

// 所有软件产品
export const allSoftwareProducts: SoftwareProduct[] = [
  kisCloudProduct,
  jdyProduct,
];

// 软件报价计算函数
export function calculateSoftwareQuote(
  productId: string,
  versionId: string,
  userCount: number,
  selectedModules: string[],
  selectedServices: string[],
  deploymentId: string,
  supportId: string,
  servicePeriod: number = 1
): {
  baseAmount: number;
  moduleAmount: number;
  serviceAmount: number;
  deploymentAmount: number;
  supportAmount: number;
  totalAmount: number;
  breakdown: any[];
} {
  const product = allSoftwareProducts.find(p => p.id === productId);
  if (!product) {
    throw new Error('产品不存在');
  }

  const version = product.versions.find(v => v.id === versionId);
  if (!version) {
    throw new Error('版本不存在');
  }

  const breakdown: any[] = [];

  // 基础软件费用
  const baseAmount = product.basePrice * version.priceMultiplier * userCount * servicePeriod;
  breakdown.push({
    item: `${product.name} ${version.name}`,
    quantity: userCount,
    unit: '用户',
    unitPrice: product.basePrice * version.priceMultiplier,
    period: servicePeriod,
    amount: baseAmount,
  });

  // 模块费用
  let moduleAmount = 0;
  selectedModules.forEach(moduleId => {
    const module = product.modules.find(m => m.id === moduleId);
    if (module && !module.required) {
      let amount = 0;
      if (module.priceType === 'per_user') {
        amount = module.price * userCount * servicePeriod;
      } else {
        amount = module.price * servicePeriod;
      }
      moduleAmount += amount;
      breakdown.push({
        item: module.name,
        quantity: module.priceType === 'per_user' ? userCount : 1,
        unit: module.priceType === 'per_user' ? '用户' : '套',
        unitPrice: module.price,
        period: servicePeriod,
        amount: amount,
      });
    }
  });

  // 服务费用
  let serviceAmount = 0;
  selectedServices.forEach(serviceId => {
    const service = product.services.find(s => s.id === serviceId);
    if (service) {
      serviceAmount += service.price;
      breakdown.push({
        item: service.name,
        quantity: 1,
        unit: '项',
        unitPrice: service.price,
        period: 1,
        amount: service.price,
      });
    }
  });

  // 部署费用
  const deployment = product.deploymentOptions.find(d => d.id === deploymentId);
  const deploymentAmount = deployment ? deployment.priceImpact * servicePeriod : 0;
  if (deploymentAmount > 0) {
    breakdown.push({
      item: deployment!.name,
      quantity: 1,
      unit: '套',
      unitPrice: deployment!.priceImpact,
      period: servicePeriod,
      amount: deploymentAmount,
    });
  }

  // 支持费用
  const support = product.supportLevels.find(s => s.id === supportId);
  const supportAmount = support ? support.price * servicePeriod : 0;
  if (supportAmount > 0) {
    breakdown.push({
      item: support!.name,
      quantity: 1,
      unit: '年',
      unitPrice: support!.price,
      period: servicePeriod,
      amount: supportAmount,
    });
  }

  const totalAmount = baseAmount + moduleAmount + serviceAmount + deploymentAmount + supportAmount;

  return {
    baseAmount,
    moduleAmount,
    serviceAmount,
    deploymentAmount,
    supportAmount,
    totalAmount,
    breakdown,
  };
}
