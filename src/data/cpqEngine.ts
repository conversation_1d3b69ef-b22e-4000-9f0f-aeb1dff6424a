// CPQ标准的配置定价报价引擎
export interface CPQConfiguration {
  id: string;
  name: string;
  industry: string;
  role: string;
  companySize: string;
  product: CPQProduct;
  modules: CPQModule[];
  services: CPQService[];
  deployment: CPQDeployment;
  support: CPQSupport;
  userCount: number;
  servicePeriod: number;
  customizations: CPQCustomization[];
  validationResults: ValidationResult[];
  pricingResults: PricingResult;
  createdAt: Date;
  updatedAt: Date;
}

export interface CPQProduct {
  id: string;
  name: string;
  version: string;
  basePrice: number;
  priceModel: 'per_user' | 'per_license' | 'enterprise';
  minUsers: number;
  maxUsers: number;
  features: string[];
  constraints: ProductConstraint[];
}

export interface CPQModule {
  id: string;
  name: string;
  category: string;
  price: number;
  priceType: 'fixed' | 'per_user' | 'percentage';
  required: boolean;
  dependencies: string[];
  conflicts: string[];
  businessValue: string;
  implementationComplexity: 'low' | 'medium' | 'high';
}

export interface CPQService {
  id: string;
  name: string;
  type: 'implementation' | 'training' | 'customization' | 'support';
  price: number;
  duration: string;
  prerequisites: string[];
  deliverables: string[];
  skillLevel: 'basic' | 'intermediate' | 'advanced';
}

export interface CPQDeployment {
  id: string;
  name: string;
  type: 'cloud' | 'on_premise' | 'hybrid';
  setupCost: number;
  monthlyCost: number;
  features: string[];
  securityLevel: 'standard' | 'enhanced' | 'enterprise';
  compliance: string[];
}

export interface CPQSupport {
  id: string;
  name: string;
  level: 'basic' | 'standard' | 'premium' | 'enterprise';
  responseTime: string;
  channels: string[];
  price: number;
  priceType: 'fixed' | 'percentage';
  coverage: string;
}

export interface CPQCustomization {
  id: string;
  name: string;
  description: string;
  type: 'workflow' | 'report' | 'integration' | 'ui';
  complexity: 'simple' | 'medium' | 'complex';
  estimatedHours: number;
  hourlyRate: number;
  dependencies: string[];
}

export interface ProductConstraint {
  type: 'user_limit' | 'feature_dependency' | 'version_compatibility';
  condition: string;
  message: string;
}

export interface ValidationResult {
  type: 'error' | 'warning' | 'info';
  category: 'configuration' | 'pricing' | 'compatibility';
  message: string;
  suggestion?: string;
  affectedItems: string[];
}

export interface PricingResult {
  baseAmount: number;
  moduleAmount: number;
  serviceAmount: number;
  deploymentAmount: number;
  supportAmount: number;
  customizationAmount: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  recurringAmount: number;
  oneTimeAmount: number;
  breakdown: PriceBreakdownItem[];
  discounts: DiscountItem[];
}

export interface PriceBreakdownItem {
  category: string;
  item: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  period: 'one_time' | 'monthly' | 'yearly';
  description: string;
}

export interface DiscountItem {
  type: 'volume' | 'loyalty' | 'promotion' | 'negotiated';
  name: string;
  amount: number;
  percentage: number;
  conditions: string[];
}

// CPQ配置验证引擎
export class CPQValidationEngine {
  static validateConfiguration(config: Partial<CPQConfiguration>): ValidationResult[] {
    const results: ValidationResult[] = [];

    // 验证用户数量限制
    if (config.product && config.userCount) {
      if (config.userCount < config.product.minUsers) {
        results.push({
          type: 'error',
          category: 'configuration',
          message: `用户数量不能少于 ${config.product.minUsers} 人`,
          suggestion: `建议设置为 ${config.product.minUsers} 人`,
          affectedItems: ['userCount'],
        });
      }
      if (config.userCount > config.product.maxUsers) {
        results.push({
          type: 'error',
          category: 'configuration',
          message: `用户数量不能超过 ${config.product.maxUsers} 人`,
          suggestion: `建议升级到企业版或联系销售`,
          affectedItems: ['userCount'],
        });
      }
    }

    // 验证模块依赖关系
    if (config.modules) {
      config.modules.forEach(module => {
        module.dependencies.forEach(depId => {
          const hasDependent = config.modules!.some(m => m.id === depId);
          if (!hasDependent) {
            results.push({
              type: 'error',
              category: 'configuration',
              message: `模块 "${module.name}" 需要依赖模块 "${depId}"`,
              suggestion: `请添加依赖模块或移除当前模块`,
              affectedItems: [module.id],
            });
          }
        });

        module.conflicts.forEach(conflictId => {
          const hasConflict = config.modules!.some(m => m.id === conflictId);
          if (hasConflict) {
            results.push({
              type: 'error',
              category: 'configuration',
              message: `模块 "${module.name}" 与模块 "${conflictId}" 冲突`,
              suggestion: `请选择其中一个模块`,
              affectedItems: [module.id, conflictId],
            });
          }
        });
      });
    }

    // 验证服务前置条件
    if (config.services) {
      config.services.forEach(service => {
        service.prerequisites.forEach(prereq => {
          const hasPrereq = config.modules?.some(m => m.id === prereq) || 
                           config.services?.some(s => s.id === prereq);
          if (!hasPrereq) {
            results.push({
              type: 'warning',
              category: 'configuration',
              message: `服务 "${service.name}" 建议先配置 "${prereq}"`,
              suggestion: `添加前置条件以获得最佳效果`,
              affectedItems: [service.id],
            });
          }
        });
      });
    }

    return results;
  }
}

// CPQ定价引擎
export class CPQPricingEngine {
  static calculatePricing(config: Partial<CPQConfiguration>): PricingResult {
    const breakdown: PriceBreakdownItem[] = [];
    const discounts: DiscountItem[] = [];

    let baseAmount = 0;
    let moduleAmount = 0;
    let serviceAmount = 0;
    let deploymentAmount = 0;
    let supportAmount = 0;
    let customizationAmount = 0;

    // 计算基础产品价格
    if (config.product && config.userCount && config.servicePeriod) {
      const unitPrice = config.product.basePrice;
      const totalPrice = unitPrice * config.userCount * config.servicePeriod;
      baseAmount = totalPrice;

      breakdown.push({
        category: '基础软件',
        item: `${config.product.name} ${config.product.version}`,
        quantity: config.userCount,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        period: 'yearly',
        description: `${config.userCount} 用户 × ${config.servicePeriod} 年`,
      });
    }

    // 计算模块价格
    if (config.modules && config.userCount && config.servicePeriod) {
      config.modules.forEach(module => {
        if (!module.required) {
          let modulePrice = 0;
          if (module.priceType === 'per_user') {
            modulePrice = module.price * config.userCount! * config.servicePeriod!;
          } else if (module.priceType === 'fixed') {
            modulePrice = module.price * config.servicePeriod!;
          } else if (module.priceType === 'percentage') {
            modulePrice = baseAmount * (module.price / 100);
          }

          moduleAmount += modulePrice;
          breakdown.push({
            category: '功能模块',
            item: module.name,
            quantity: module.priceType === 'per_user' ? config.userCount! : 1,
            unitPrice: module.price,
            totalPrice: modulePrice,
            period: module.priceType === 'percentage' ? 'one_time' : 'yearly',
            description: module.businessValue,
          });
        }
      });
    }

    // 计算服务价格
    if (config.services) {
      config.services.forEach(service => {
        serviceAmount += service.price;
        breakdown.push({
          category: '实施服务',
          item: service.name,
          quantity: 1,
          unitPrice: service.price,
          totalPrice: service.price,
          period: 'one_time',
          description: `${service.duration} | ${service.deliverables.join(', ')}`,
        });
      });
    }

    // 计算部署费用
    if (config.deployment && config.servicePeriod) {
      deploymentAmount = config.deployment.setupCost + 
                        (config.deployment.monthlyCost * 12 * config.servicePeriod);
      
      if (config.deployment.setupCost > 0) {
        breakdown.push({
          category: '部署服务',
          item: `${config.deployment.name} - 初始化`,
          quantity: 1,
          unitPrice: config.deployment.setupCost,
          totalPrice: config.deployment.setupCost,
          period: 'one_time',
          description: '环境搭建和初始配置',
        });
      }

      if (config.deployment.monthlyCost > 0) {
        const yearlyDeploymentCost = config.deployment.monthlyCost * 12 * config.servicePeriod;
        breakdown.push({
          category: '部署服务',
          item: `${config.deployment.name} - 运维`,
          quantity: config.servicePeriod!,
          unitPrice: config.deployment.monthlyCost * 12,
          totalPrice: yearlyDeploymentCost,
          period: 'yearly',
          description: '环境维护和监控',
        });
      }
    }

    // 计算支持费用
    if (config.support && config.servicePeriod) {
      if (config.support.priceType === 'fixed') {
        supportAmount = config.support.price * config.servicePeriod;
      } else if (config.support.priceType === 'percentage') {
        supportAmount = (baseAmount + moduleAmount) * (config.support.price / 100);
      }

      breakdown.push({
        category: '技术支持',
        item: config.support.name,
        quantity: config.servicePeriod!,
        unitPrice: config.support.priceType === 'fixed' ? config.support.price : 0,
        totalPrice: supportAmount,
        period: 'yearly',
        description: `${config.support.responseTime} | ${config.support.coverage}`,
      });
    }

    // 计算定制开发费用
    if (config.customizations) {
      config.customizations.forEach(customization => {
        const customPrice = customization.estimatedHours * customization.hourlyRate;
        customizationAmount += customPrice;
        breakdown.push({
          category: '定制开发',
          item: customization.name,
          quantity: customization.estimatedHours,
          unitPrice: customization.hourlyRate,
          totalPrice: customPrice,
          period: 'one_time',
          description: customization.description,
        });
      });
    }

    // 计算折扣
    let discountAmount = 0;
    const totalBeforeDiscount = baseAmount + moduleAmount + serviceAmount + deploymentAmount + supportAmount + customizationAmount;

    // 批量折扣
    if (config.userCount && config.userCount >= 50) {
      const volumeDiscount = (baseAmount + moduleAmount) * 0.1;
      discountAmount += volumeDiscount;
      discounts.push({
        type: 'volume',
        name: '批量采购折扣',
        amount: volumeDiscount,
        percentage: 10,
        conditions: ['用户数 ≥ 50'],
      });
    }

    // 多年期折扣
    if (config.servicePeriod && config.servicePeriod >= 3) {
      const loyaltyDiscount = (baseAmount + moduleAmount) * 0.05;
      discountAmount += loyaltyDiscount;
      discounts.push({
        type: 'loyalty',
        name: '多年期优惠',
        amount: loyaltyDiscount,
        percentage: 5,
        conditions: ['服务期限 ≥ 3年'],
      });
    }

    const taxAmount = 0; // 简化处理，实际应根据地区计算税费
    const totalAmount = totalBeforeDiscount - discountAmount + taxAmount;

    const recurringAmount = baseAmount + moduleAmount + deploymentAmount - 
                           (config.deployment?.setupCost || 0) + supportAmount;
    const oneTimeAmount = serviceAmount + customizationAmount + (config.deployment?.setupCost || 0);

    return {
      baseAmount,
      moduleAmount,
      serviceAmount,
      deploymentAmount,
      supportAmount,
      customizationAmount,
      discountAmount,
      taxAmount,
      totalAmount,
      recurringAmount,
      oneTimeAmount,
      breakdown,
      discounts,
    };
  }
}

// CPQ配置生成器
export class CPQConfigurationGenerator {
  static generateConfiguration(
    industry: string,
    role: string,
    companySize: string,
    painPoints: string[],
    userCount: number,
    servicePeriod: number
  ): CPQConfiguration {
    // 这里应该根据实际的产品数据生成配置
    // 简化实现，返回示例配置
    const config: CPQConfiguration = {
      id: `config_${Date.now()}`,
      name: `${industry}_${role}_配置`,
      industry,
      role,
      companySize,
      product: {
        id: 'jdy',
        name: '金蝶精斗云',
        version: '专业版',
        basePrice: 2980,
        priceModel: 'per_user',
        minUsers: 1,
        maxUsers: 200,
        features: ['进销存', '财务管理', 'CRM'],
        constraints: [],
      },
      modules: [],
      services: [],
      deployment: {
        id: 'cloud',
        name: '云端部署',
        type: 'cloud',
        setupCost: 0,
        monthlyCost: 0,
        features: ['自动备份', '弹性扩容'],
        securityLevel: 'standard',
        compliance: ['等保二级'],
      },
      support: {
        id: 'standard',
        name: '标准支持',
        level: 'standard',
        responseTime: '4小时内',
        channels: ['在线客服', '电话支持'],
        price: 0,
        priceType: 'fixed',
        coverage: '工作日 9:00-18:00',
      },
      userCount,
      servicePeriod,
      customizations: [],
      validationResults: [],
      pricingResults: {
        baseAmount: 0,
        moduleAmount: 0,
        serviceAmount: 0,
        deploymentAmount: 0,
        supportAmount: 0,
        customizationAmount: 0,
        discountAmount: 0,
        taxAmount: 0,
        totalAmount: 0,
        recurringAmount: 0,
        oneTimeAmount: 0,
        breakdown: [],
        discounts: [],
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 验证配置
    config.validationResults = CPQValidationEngine.validateConfiguration(config);
    
    // 计算价格
    config.pricingResults = CPQPricingEngine.calculatePricing(config);

    return config;
  }
}
