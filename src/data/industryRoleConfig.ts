// 行业角色驱动的配置数据模型
export interface Industry {
  id: string;
  name: string;
  description: string;
  icon: string;
  painPoints: PainPoint[];
  commonRoles: Role[];
  recommendedProducts: string[];
  businessScenarios: BusinessScenario[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
  responsibilities: string[];
  painPoints: string[];
  preferredFeatures: string[];
  decisionFactors: string[];
}

export interface PainPoint {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  frequency: number; // 0-100 表示在该行业中的普遍程度
  solutions: Solution[];
}

export interface Solution {
  id: string;
  title: string;
  description: string;
  productModules: string[];
  estimatedROI: string;
  implementationTime: string;
}

export interface BusinessScenario {
  id: string;
  name: string;
  description: string;
  challenges: string[];
  requiredFeatures: string[];
  optionalFeatures: string[];
  estimatedUsers: number;
  complexity: 'simple' | 'medium' | 'complex';
}

// 制造业配置
export const manufacturingIndustry: Industry = {
  id: 'manufacturing',
  name: '制造业',
  description: '生产制造企业，注重成本控制、质量管理和供应链优化',
  icon: 'factory',
  painPoints: [
    {
      id: 'cost_control',
      title: '成本核算复杂',
      description: '原材料成本波动大，生产成本难以精确核算，影响定价决策',
      impact: 'high',
      frequency: 95,
      solutions: [
        {
          id: 'cost_accounting',
          title: '精细化成本核算',
          description: '实时跟踪原材料、人工、制造费用，提供多维度成本分析',
          productModules: ['cost_accounting', 'inventory_management', 'production_management'],
          estimatedROI: '降低成本15-25%',
          implementationTime: '2-3个月',
        },
      ],
    },
    {
      id: 'inventory_management',
      title: '库存管理困难',
      description: '原材料、半成品、成品库存复杂，容易出现积压或缺货',
      impact: 'high',
      frequency: 90,
      solutions: [
        {
          id: 'smart_inventory',
          title: '智能库存管理',
          description: '自动补货提醒、库存预警、ABC分类管理',
          productModules: ['inventory_management', 'purchase_management', 'sales_management'],
          estimatedROI: '减少库存20-30%',
          implementationTime: '1-2个月',
        },
      ],
    },
    {
      id: 'quality_control',
      title: '质量追溯困难',
      description: '产品质量问题难以快速定位，影响客户满意度和品牌形象',
      impact: 'high',
      frequency: 85,
      solutions: [
        {
          id: 'quality_traceability',
          title: '全程质量追溯',
          description: '从原材料到成品的全程质量跟踪和追溯',
          productModules: ['quality_management', 'production_management', 'inventory_management'],
          estimatedROI: '减少质量成本30-40%',
          implementationTime: '3-4个月',
        },
      ],
    },
  ],
  commonRoles: [
    {
      id: 'production_manager',
      name: '生产经理',
      description: '负责生产计划、进度控制和质量管理',
      responsibilities: ['生产计划制定', '生产进度监控', '质量控制', '成本管理'],
      painPoints: ['生产计划不准确', '质量问题频发', '成本超支'],
      preferredFeatures: ['生产计划', '质量管理', '成本核算', '设备管理'],
      decisionFactors: ['功能完整性', '易用性', '集成能力'],
    },
    {
      id: 'supply_chain_manager',
      name: '供应链经理',
      description: '负责采购、库存和供应商管理',
      responsibilities: ['采购管理', '库存控制', '供应商管理', '物流协调'],
      painPoints: ['库存积压', '供应商管理混乱', '采购成本高'],
      preferredFeatures: ['采购管理', '库存管理', '供应商管理', '成本分析'],
      decisionFactors: ['成本效益', '供应链可视化', '数据准确性'],
    },
  ],
  recommendedProducts: ['jdy', 'kis-cloud'],
  businessScenarios: [
    {
      id: 'small_manufacturing',
      name: '小型制造企业',
      description: '50人以下的制造企业，注重成本控制和效率提升',
      challenges: ['手工记账效率低', '库存管理混乱', '成本核算不准确'],
      requiredFeatures: ['财务管理', '库存管理', '采购管理'],
      optionalFeatures: ['生产管理', '质量管理', 'CRM'],
      estimatedUsers: 10,
      complexity: 'simple',
    },
    {
      id: 'medium_manufacturing',
      name: '中型制造企业',
      description: '50-200人的制造企业，需要完整的ERP解决方案',
      challenges: ['部门协作困难', '数据孤岛严重', '决策缺乏数据支持'],
      requiredFeatures: ['完整ERP', '生产管理', '质量管理', '成本核算'],
      optionalFeatures: ['BI分析', 'MES集成', '移动应用'],
      estimatedUsers: 50,
      complexity: 'complex',
    },
  ],
};

// 零售业配置
export const retailIndustry: Industry = {
  id: 'retail',
  name: '零售业',
  description: '零售连锁企业，注重客户体验、库存周转和销售分析',
  icon: 'shop',
  painPoints: [
    {
      id: 'customer_management',
      title: '客户管理分散',
      description: '客户信息分散在各个渠道，难以形成统一的客户画像',
      impact: 'high',
      frequency: 90,
      solutions: [
        {
          id: 'unified_crm',
          title: '统一客户管理',
          description: '整合线上线下客户数据，建立完整客户档案',
          productModules: ['crm', 'sales_management', 'marketing_automation'],
          estimatedROI: '提升客户满意度25%',
          implementationTime: '1-2个月',
        },
      ],
    },
    {
      id: 'inventory_turnover',
      title: '库存周转慢',
      description: '商品库存周转率低，资金占用大，影响现金流',
      impact: 'high',
      frequency: 85,
      solutions: [
        {
          id: 'smart_replenishment',
          title: '智能补货系统',
          description: '基于销售数据和季节性分析的智能补货建议',
          productModules: ['inventory_management', 'sales_analysis', 'purchase_management'],
          estimatedROI: '提升库存周转率30%',
          implementationTime: '2-3个月',
        },
      ],
    },
  ],
  commonRoles: [
    {
      id: 'store_manager',
      name: '门店经理',
      description: '负责门店日常运营和销售管理',
      responsibilities: ['门店运营', '销售管理', '客户服务', '库存控制'],
      painPoints: ['销售数据不及时', '库存管理困难', '客户流失'],
      preferredFeatures: ['销售管理', 'POS系统', '客户管理', '库存管理'],
      decisionFactors: ['易用性', '移动支持', '实时性'],
    },
  ],
  recommendedProducts: ['jdy'],
  businessScenarios: [
    {
      id: 'chain_retail',
      name: '连锁零售',
      description: '多门店连锁经营，需要统一管理',
      challenges: ['门店数据不统一', '库存调配困难', '促销活动难以协调'],
      requiredFeatures: ['多门店管理', '统一库存', 'POS系统', 'CRM'],
      optionalFeatures: ['会员管理', '促销管理', '移动应用'],
      estimatedUsers: 30,
      complexity: 'medium',
    },
  ],
};

// 服务业配置
export const serviceIndustry: Industry = {
  id: 'service',
  name: '服务业',
  description: '专业服务企业，注重项目管理、客户关系和服务质量',
  icon: 'customer-service',
  painPoints: [
    {
      id: 'project_management',
      title: '项目管理混乱',
      description: '项目进度难以跟踪，资源分配不合理，影响交付质量',
      impact: 'high',
      frequency: 88,
      solutions: [
        {
          id: 'project_tracking',
          title: '项目全程跟踪',
          description: '项目计划、进度、成本、质量的全方位管理',
          productModules: ['project_management', 'time_tracking', 'resource_management'],
          estimatedROI: '提升项目成功率40%',
          implementationTime: '1-2个月',
        },
      ],
    },
  ],
  commonRoles: [
    {
      id: 'project_manager',
      name: '项目经理',
      description: '负责项目规划、执行和交付',
      responsibilities: ['项目规划', '进度控制', '资源协调', '客户沟通'],
      painPoints: ['项目进度延误', '资源冲突', '客户满意度低'],
      preferredFeatures: ['项目管理', '时间跟踪', '客户管理', '文档管理'],
      decisionFactors: ['项目可视化', '协作能力', '移动支持'],
    },
  ],
  recommendedProducts: ['jdy'],
  businessScenarios: [
    {
      id: 'consulting_firm',
      name: '咨询公司',
      description: '专业咨询服务，注重知识管理和客户关系',
      challenges: ['项目经验难以沉淀', '客户关系管理不系统', '时间成本核算困难'],
      requiredFeatures: ['项目管理', 'CRM', '时间跟踪', '知识管理'],
      optionalFeatures: ['合同管理', '发票管理', '移动办公'],
      estimatedUsers: 20,
      complexity: 'medium',
    },
  ],
};

// 所有行业配置
export const allIndustries: Industry[] = [
  manufacturingIndustry,
  retailIndustry,
  serviceIndustry,
];

// 根据行业和角色推荐配置
export function getRecommendedConfiguration(
  industryId: string,
  roleId: string,
  companySize: 'small' | 'medium' | 'large',
  painPoints: string[]
): {
  recommendedProduct: string;
  recommendedModules: string[];
  recommendedServices: string[];
  estimatedUsers: number;
  estimatedPrice: number;
  roi: string;
} {
  const industry = allIndustries.find(i => i.id === industryId);
  const role = industry?.commonRoles.find(r => r.id === roleId);
  
  if (!industry || !role) {
    throw new Error('行业或角色不存在');
  }

  // 基于公司规模推荐产品
  let recommendedProduct = 'kis-cloud';
  let estimatedUsers = 5;
  
  if (companySize === 'medium') {
    recommendedProduct = 'jdy';
    estimatedUsers = 20;
  } else if (companySize === 'large') {
    recommendedProduct = 'k3-cloud';
    estimatedUsers = 50;
  }

  // 基于痛点推荐模块
  const recommendedModules: string[] = [];
  const recommendedServices: string[] = [];
  
  painPoints.forEach(painPointId => {
    const painPoint = industry.painPoints.find(p => p.id === painPointId);
    if (painPoint) {
      painPoint.solutions.forEach(solution => {
        recommendedModules.push(...solution.productModules);
      });
    }
  });

  // 去重
  const uniqueModules = Array.from(new Set(recommendedModules));
  
  // 基础实施服务
  recommendedServices.push('basic_implementation');
  
  // 估算价格（简化计算）
  const basePrice = recommendedProduct === 'kis-cloud' ? 1980 : 
                   recommendedProduct === 'jdy' ? 2980 : 5000;
  const estimatedPrice = basePrice * estimatedUsers + uniqueModules.length * 500;
  
  return {
    recommendedProduct,
    recommendedModules: uniqueModules,
    recommendedServices,
    estimatedUsers,
    estimatedPrice,
    roi: '预计12-18个月回收投资',
  };
}
