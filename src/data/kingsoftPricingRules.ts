// 金蝶产品定价规则配置
export interface KingsoftPricingRule {
  id: string;
  name: string;
  description: string;
  productSku: string;
  ruleType: 'volume_discount' | 'customer_tier' | 'renewal' | 'channel_partner' | 'industry_solution';
  conditions: {
    minQuantity?: number;
    maxQuantity?: number;
    customerTier?: string[];
    isRenewal?: boolean;
    partnerLevel?: string[];
    industry?: string[];
  };
  discount: {
    type: 'percentage' | 'fixed_amount';
    value: number;
  };
  priority: number;
  isActive: boolean;
  validFrom: string;
  validTo?: string;
}

// 金蝶云星辰定价规则
export const kisCloudPricingRules: KingsoftPricingRule[] = [
  {
    id: 'kis-volume-1',
    name: '云星辰批量采购折扣',
    description: '购买5套以上享受8.5折优惠',
    productSku: 'KIS-CLOUD-STD',
    ruleType: 'volume_discount',
    conditions: {
      minQuantity: 5,
      maxQuantity: 9,
    },
    discount: {
      type: 'percentage',
      value: 15, // 8.5折 = 15%折扣
    },
    priority: 1,
    isActive: true,
    validFrom: '2024-01-01',
  },
  {
    id: 'kis-volume-2',
    name: '云星辰大批量采购折扣',
    description: '购买10套以上享受8折优惠',
    productSku: 'KIS-CLOUD-STD',
    ruleType: 'volume_discount',
    conditions: {
      minQuantity: 10,
    },
    discount: {
      type: 'percentage',
      value: 20, // 8折 = 20%折扣
    },
    priority: 2,
    isActive: true,
    validFrom: '2024-01-01',
  },
  {
    id: 'kis-renewal-1',
    name: '云星辰续费优惠',
    description: '老客户续费享受9折优惠',
    productSku: 'KIS-CLOUD-STD',
    ruleType: 'renewal',
    conditions: {
      isRenewal: true,
    },
    discount: {
      type: 'percentage',
      value: 10, // 9折 = 10%折扣
    },
    priority: 3,
    isActive: true,
    validFrom: '2024-01-01',
  },
  {
    id: 'kis-partner-1',
    name: '渠道伙伴价格',
    description: '认证渠道伙伴享受7折价格',
    productSku: 'KIS-CLOUD-STD',
    ruleType: 'channel_partner',
    conditions: {
      partnerLevel: ['certified_partner', 'gold_partner'],
    },
    discount: {
      type: 'percentage',
      value: 30, // 7折 = 30%折扣
    },
    priority: 4,
    isActive: true,
    validFrom: '2024-01-01',
  },
];

// 金蝶精斗云定价规则
export const jdyPricingRules: KingsoftPricingRule[] = [
  {
    id: 'jdy-volume-1',
    name: '精斗云批量采购折扣',
    description: '购买3套以上享受9折优惠',
    productSku: 'JDY-PRO-001',
    ruleType: 'volume_discount',
    conditions: {
      minQuantity: 3,
      maxQuantity: 9,
    },
    discount: {
      type: 'percentage',
      value: 10, // 9折 = 10%折扣
    },
    priority: 1,
    isActive: true,
    validFrom: '2024-01-01',
  },
  {
    id: 'jdy-industry-1',
    name: '制造业解决方案加价',
    description: '制造业定制解决方案加价20%',
    productSku: 'JDY-PRO-001',
    ruleType: 'industry_solution',
    conditions: {
      industry: ['manufacturing'],
    },
    discount: {
      type: 'percentage',
      value: -20, // 加价20%
    },
    priority: 5,
    isActive: true,
    validFrom: '2024-01-01',
  },
  {
    id: 'jdy-industry-2',
    name: '零售业解决方案加价',
    description: '零售业定制解决方案加价15%',
    productSku: 'JDY-PRO-001',
    ruleType: 'industry_solution',
    conditions: {
      industry: ['retail'],
    },
    discount: {
      type: 'percentage',
      value: -15, // 加价15%
    },
    priority: 6,
    isActive: true,
    validFrom: '2024-01-01',
  },
];

// K/3 Cloud定价规则
export const k3CloudPricingRules: KingsoftPricingRule[] = [
  {
    id: 'k3-enterprise-1',
    name: 'K/3 Cloud企业级折扣',
    description: '大型企业客户享受特殊折扣',
    productSku: 'K3-CLOUD-001',
    ruleType: 'customer_tier',
    conditions: {
      customerTier: ['platinum', 'enterprise'],
    },
    discount: {
      type: 'percentage',
      value: 15, // 8.5折
    },
    priority: 1,
    isActive: true,
    validFrom: '2024-01-01',
  },
];

// 合并所有定价规则
export const allKingsoftPricingRules: KingsoftPricingRule[] = [
  ...kisCloudPricingRules,
  ...jdyPricingRules,
  ...k3CloudPricingRules,
];

// 定价计算函数
export function calculateKingsoftPrice(
  basePrice: number,
  productSku: string,
  quantity: number = 1,
  customerTier: string = 'standard',
  isRenewal: boolean = false,
  partnerLevel?: string,
  industry?: string
): {
  originalPrice: number;
  finalPrice: number;
  totalDiscount: number;
  appliedRules: KingsoftPricingRule[];
} {
  const applicableRules = allKingsoftPricingRules
    .filter(rule => rule.productSku === productSku && rule.isActive)
    .filter(rule => {
      // 检查数量条件
      if (rule.conditions.minQuantity && quantity < rule.conditions.minQuantity) return false;
      if (rule.conditions.maxQuantity && quantity > rule.conditions.maxQuantity) return false;
      
      // 检查客户等级条件
      if (rule.conditions.customerTier && !rule.conditions.customerTier.includes(customerTier)) return false;
      
      // 检查续费条件
      if (rule.conditions.isRenewal !== undefined && rule.conditions.isRenewal !== isRenewal) return false;
      
      // 检查渠道伙伴条件
      if (rule.conditions.partnerLevel && (!partnerLevel || !rule.conditions.partnerLevel.includes(partnerLevel))) return false;
      
      // 检查行业条件
      if (rule.conditions.industry && (!industry || !rule.conditions.industry.includes(industry))) return false;
      
      return true;
    })
    .sort((a, b) => a.priority - b.priority);

  const originalPrice = basePrice * quantity;
  let finalPrice = originalPrice;
  let totalDiscount = 0;

  // 应用定价规则
  applicableRules.forEach(rule => {
    if (rule.discount.type === 'percentage') {
      const discountAmount = originalPrice * (rule.discount.value / 100);
      finalPrice -= discountAmount;
      totalDiscount += discountAmount;
    } else if (rule.discount.type === 'fixed_amount') {
      finalPrice -= rule.discount.value * quantity;
      totalDiscount += rule.discount.value * quantity;
    }
  });

  // 确保最终价格不为负数
  finalPrice = Math.max(finalPrice, 0);

  return {
    originalPrice,
    finalPrice: Math.round(finalPrice * 100) / 100, // 保留两位小数
    totalDiscount: Math.round(totalDiscount * 100) / 100,
    appliedRules: applicableRules,
  };
}
