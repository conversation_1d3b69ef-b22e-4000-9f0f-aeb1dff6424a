// 模拟数据文件
import { User, Product, Configuration, Quote, Customer } from '@/types';

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    firstName: '管理员',
    lastName: '系统',
    avatar: 'https://via.placeholder.com/64x64?text=Admin',
    roles: [
      {
        id: 'admin',
        name: '系统管理员',
        description: '拥有系统所有权限',
        permissions: [],
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
      },
    ],
    permissions: [],
    status: 'active',
    lastLoginAt: '2024-01-15T10:30:00Z',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    username: 'sales001',
    email: '<EMAIL>',
    firstName: '李',
    lastName: '销售',
    avatar: 'https://via.placeholder.com/64x64?text=李销售',
    roles: [
      {
        id: 'sales_manager',
        name: '销售经理',
        description: '管理销售团队和报价',
        permissions: [],
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
      },
    ],
    permissions: [],
    status: 'active',
    lastLoginAt: '2024-01-15T09:15:00Z',
    createdAt: '2023-03-15T00:00:00Z',
    updatedAt: '2024-01-15T09:15:00Z',
  },
];

// 金蝶软件产品数据
export const mockProducts: Product[] = [
  {
    id: '1',
    name: '金蝶云星辰标准版',
    description: '适用于小微企业的云端财务管理软件，支持总账、应收应付、固定资产等核心财务功能',
    sku: 'KIS-CLOUD-STD',
    category: {
      id: '1',
      name: '财务管理软件',
      description: '企业财务核算与管理软件',
      level: 1,
      path: '/financial-software',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    specifications: [
      {
        id: '1',
        productId: '1',
        name: '软件版本',
        value: '标准版',
        type: 'select',
        required: true,
        options: ['标准版', '专业版', '旗舰版'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '2',
        productId: '1',
        name: '并发用户数',
        value: '3用户',
        type: 'select',
        required: true,
        options: ['1用户', '3用户', '5用户', '10用户', '20用户', '50用户'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '3',
        productId: '1',
        name: '账套数量',
        value: '1个账套',
        type: 'select',
        required: true,
        options: ['1个账套', '3个账套', '5个账套', '不限'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '4',
        productId: '1',
        name: '功能模块',
        value: '基础财务',
        type: 'multiselect',
        required: true,
        options: ['总账管理', '应收应付', '固定资产', '出纳管理', '财务报表', '成本核算'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '5',
        productId: '1',
        name: '服务期限',
        value: '1年',
        type: 'select',
        required: true,
        options: ['1年', '2年', '3年', '5年'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '6',
        productId: '1',
        name: '部署方式',
        value: '云端部署',
        type: 'select',
        required: true,
        options: ['云端部署', '本地部署', '混合部署'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '7',
        productId: '1',
        name: '技术支持',
        value: '标准支持',
        type: 'select',
        required: true,
        options: ['标准支持', '高级支持', '专属支持'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '8',
        productId: '1',
        name: '培训服务',
        value: '在线培训',
        type: 'select',
        required: false,
        options: ['在线培训', '现场培训', '定制培训'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    images: [
      {
        id: '1',
        productId: '1',
        url: 'https://via.placeholder.com/400x300?text=金蝶云星辰',
        alt: '金蝶云星辰标准版',
        isPrimary: true,
        order: 1,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    basePrice: 3980,
    currency: 'CNY',
    status: 'active',
    tags: ['财务管理', '云端SaaS', '小微企业', '热销产品'],
    version: '1.0',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: '金蝶精斗云专业版',
    description: '一体化云端企业管理软件，集成进销存、财务、CRM、项目管理等全业务流程',
    sku: 'JDY-PRO-001',
    category: {
      id: '2',
      name: '企业管理软件',
      description: '一体化企业管理解决方案',
      level: 1,
      path: '/enterprise-software',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    specifications: [
      {
        id: '9',
        productId: '2',
        name: '软件版本',
        value: '专业版',
        type: 'select',
        required: true,
        options: ['基础版', '专业版', '企业版'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '10',
        productId: '2',
        name: '并发用户数',
        value: '5用户',
        type: 'select',
        required: true,
        options: ['3用户', '5用户', '10用户', '20用户', '50用户'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '11',
        productId: '2',
        name: '业务模块',
        value: '进销存+财务',
        type: 'multiselect',
        required: true,
        options: ['进销存管理', '财务管理', 'CRM客户管理', '项目管理', '人事管理', '办公协同'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '12',
        productId: '2',
        name: '数据存储',
        value: '20GB',
        type: 'select',
        required: true,
        options: ['10GB', '20GB', '50GB', '100GB', '500GB'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '13',
        productId: '2',
        name: '服务期限',
        value: '1年',
        type: 'select',
        required: true,
        options: ['1年', '2年', '3年', '5年'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '14',
        productId: '2',
        name: 'API接口',
        value: '5000次/月',
        type: 'select',
        required: false,
        options: ['1000次/月', '5000次/月', '10000次/月', '不限制'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '15',
        productId: '2',
        name: '移动端支持',
        value: '标准版',
        type: 'select',
        required: false,
        options: ['标准版', '专业版', '定制版'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    images: [
      {
        id: '2',
        productId: '2',
        url: 'https://via.placeholder.com/400x300?text=金蝶精斗云',
        alt: '金蝶精斗云专业版',
        isPrimary: true,
        order: 1,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    basePrice: 6980,
    currency: 'CNY',
    status: 'active',
    tags: ['一体化管理', '云端SaaS', '中小企业', '推荐产品'],
    version: '1.0',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '3',
    name: '金蝶云星辰专业版',
    description: '适用于中小企业的专业财务管理软件，支持多账套、多用户协同办公',
    sku: 'KIS-CLOUD-PRO',
    category: {
      id: '1',
      name: '财务软件',
      description: '企业财务管理软件',
      level: 1,
      path: '/financial-software',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    specifications: [
      {
        id: '10',
        productId: '3',
        name: '用户数量',
        value: '10用户',
        type: 'select',
        required: true,
        options: ['5用户', '10用户', '20用户', '50用户'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '11',
        productId: '3',
        name: '账套数量',
        value: '5个账套',
        type: 'select',
        required: true,
        options: ['3个账套', '5个账套', '10个账套', '不限'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '12',
        productId: '3',
        name: '功能模块',
        value: '全功能版',
        type: 'select',
        required: true,
        options: ['基础版', '标准版', '全功能版'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    images: [
      {
        id: '3',
        productId: '3',
        url: 'https://via.placeholder.com/400x300?text=云星辰专业版',
        alt: '金蝶云星辰专业版',
        isPrimary: true,
        order: 1,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    basePrice: 8980,
    currency: 'CNY',
    status: 'active',
    tags: ['财务软件', '专业版', '中小企业'],
    version: '1.0',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '4',
    name: '金蝶K/3 Cloud',
    description: '大中型企业数字化转型平台，提供全面的ERP解决方案',
    sku: 'K3-CLOUD-001',
    category: {
      id: '3',
      name: 'ERP系统',
      description: '企业资源规划系统',
      level: 1,
      path: '/erp-systems',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    specifications: [
      {
        id: '13',
        productId: '4',
        name: '用户数量',
        value: '100用户',
        type: 'select',
        required: true,
        options: ['50用户', '100用户', '200用户', '500用户', '不限'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: '14',
        productId: '4',
        name: '部署方式',
        value: '云端部署',
        type: 'select',
        required: true,
        options: ['云端部署', '本地部署', '混合部署'],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    images: [
      {
        id: '4',
        productId: '4',
        url: 'https://via.placeholder.com/400x300?text=K3+Cloud',
        alt: '金蝶K/3 Cloud',
        isPrimary: true,
        order: 1,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    basePrice: 58000,
    currency: 'CNY',
    status: 'active',
    tags: ['ERP', '大中型企业', '数字化转型'],
    version: '1.0',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

// 模拟客户数据
export const mockCustomers: Customer[] = [
  {
    id: '1',
    name: '王总',
    email: '<EMAIL>',
    phone: '+86 138-0013-8000',
    company: '深圳明星科技有限公司',
    address: {
      street: '南山区科技园南区',
      city: '深圳',
      state: '广东',
      country: '中国',
      postalCode: '518000',
    },
    tier: 'gold',
    creditLimit: 500000,
    paymentTerms: '30天',
    salesPersonId: '2',
    preferences: {
      currency: 'CNY',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      communicationChannel: 'email',
      notifications: {
        quoteUpdates: true,
        promotions: true,
        newsletters: false,
      },
    },
    status: 'active',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: '李经理',
    email: '<EMAIL>',
    phone: '+86 138-0013-8001',
    company: '东莞华信制造有限公司',
    address: {
      street: '松山湖高新技术产业开发区',
      city: '东莞',
      state: '广东',
      country: '中国',
      postalCode: '523000',
    },
    tier: 'silver',
    creditLimit: 200000,
    paymentTerms: '15天',
    salesPersonId: '2',
    preferences: {
      currency: 'CNY',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      communicationChannel: 'email',
      notifications: {
        quoteUpdates: true,
        promotions: false,
        newsletters: true,
      },
    },
    status: 'active',
    createdAt: '2023-03-20T00:00:00Z',
    updatedAt: '2024-01-10T14:20:00Z',
  },
];

// 模拟报价数据
export const mockQuotes: Quote[] = [
  {
    id: '1',
    quoteNumber: 'Q-2024-001',
    customerId: '1',
    customerName: '阿里巴巴集团',
    customerEmail: '<EMAIL>',
    salesPersonId: '2',
    items: [
      {
        id: '1',
        quoteId: '1',
        productId: '1',
        productName: 'Dell PowerEdge R750 服务器',
        configurationId: 'config-1',
        quantity: 2,
        unitPrice: 25000,
        discountPercentage: 10,
        discountAmount: 5000,
        lineTotal: 45000,
        specifications: {
          'CPU': 'Intel Xeon Silver 4314',
          '内存': '32GB DDR4',
        },
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15',
      },
    ],
    subtotal: 50000,
    discountAmount: 5000,
    taxAmount: 4500,
    shippingAmount: 0,
    total: 49500,
    currency: 'CNY',
    status: 'sent',
    validUntil: '2024-02-15',
    notes: '此报价包含标准配置，如需定制请联系销售人员。',
    terms: '1. 报价有效期30天\n2. 付款方式：预付30%，发货前付清余款',
    templateId: 'template-1',
    version: 1,
    parentQuoteId: undefined,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T15:45:00Z',
  },
];

// 模拟配置数据
export const mockConfigurations: Configuration[] = [
  {
    id: 'config-1',
    name: '企业级服务器标准配置',
    description: '适用于中型企业的标准服务器配置方案',
    productId: '1',
    items: [
      {
        id: 'item-1',
        configurationId: 'config-1',
        productId: '1',
        quantity: 1,
        specifications: {
          'CPU': 'Intel Xeon Silver 4314',
          '内存': '32GB DDR4',
        },
        price: 25000,
        position: { x: 0, y: 0 },
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15',
      },
    ],
    rules: [],
    status: 'active',
    version: '1.0',
    templateId: undefined,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
];

// API响应模拟函数
export const createMockApiResponse = <T>(data: T, success = true, message?: string) => ({
  success,
  data,
  message,
  errors: success ? undefined : ['模拟错误'],
  pagination: Array.isArray(data) ? {
    page: 1,
    pageSize: 20,
    total: (data as any[]).length,
    totalPages: Math.ceil((data as any[]).length / 20),
  } : undefined,
});

// 分页响应模拟函数
export const createMockPaginatedResponse = <T>(
  data: T[],
  page: number = 1,
  pageSize: number = 20,
  success = true,
  message?: string
) => ({
  success,
  data: data.slice((page - 1) * pageSize, page * pageSize),
  message,
  errors: success ? undefined : ['模拟错误'],
  pagination: {
    page,
    pageSize,
    total: data.length,
    totalPages: Math.ceil(data.length / pageSize),
  },
});

// 模拟登录响应
export const mockLoginResponse = {
  success: true,
  data: {
    user: mockUsers[0],
    token: 'mock_jwt_token_' + Date.now(),
  },
  message: '登录成功',
};

// 模拟延迟函数
export const mockDelay = (ms: number = 1000) => 
  new Promise(resolve => setTimeout(resolve, ms));
