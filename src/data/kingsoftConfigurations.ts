// 金蝶产品配置数据
export interface KingsoftConfiguration {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  configurationName: string;
  description: string;
  specifications: {
    [key: string]: {
      value: string;
      options: string[];
      priceImpact: number; // 价格影响（正数为加价，负数为减价）
      required: boolean;
    };
  };
  basePrice: number;
  recommendedFor: string[];
  features: string[];
  limitations?: string[];
  supportLevel: string;
  deploymentType: string;
  category: string;
}

// 金蝶云星辰配置方案
export const kisCloudConfigurations: KingsoftConfiguration[] = [
  {
    id: 'kis-cloud-starter',
    productId: '1',
    productName: '金蝶云星辰标准版',
    productSku: 'KIS-CLOUD-STD',
    configurationName: '初创企业套餐',
    description: '适合刚起步的小微企业，提供基础财务管理功能',
    specifications: {
      '用户数量': {
        value: '1用户',
        options: ['1用户', '3用户', '5用户'],
        priceImpact: 0,
        required: true,
      },
      '账套数量': {
        value: '1个账套',
        options: ['1个账套'],
        priceImpact: 0,
        required: true,
      },
      '存储空间': {
        value: '5GB',
        options: ['5GB', '10GB', '20GB'],
        priceImpact: 0,
        required: true,
      },
      '服务期限': {
        value: '1年',
        options: ['1年', '2年', '3年'],
        priceImpact: 0,
        required: true,
      },
      '技术支持': {
        value: '标准支持',
        options: ['标准支持', '高级支持'],
        priceImpact: 0,
        required: true,
      },
    },
    basePrice: 2980,
    recommendedFor: ['初创企业', '个体工商户', '1-3人小团队'],
    features: [
      '基础总账管理',
      '简单报表',
      '凭证录入',
      '账簿查询',
      '移动端查看',
    ],
    limitations: [
      '不支持多账套',
      '报表功能有限',
      '不支持自定义字段',
    ],
    supportLevel: '工作日在线支持',
    deploymentType: '云端部署',
    category: '财务软件',
  },
  {
    id: 'kis-cloud-standard',
    productId: '1',
    productName: '金蝶云星辰标准版',
    productSku: 'KIS-CLOUD-STD',
    configurationName: '标准企业套餐',
    description: '适合成长期小微企业，提供完整的财务管理功能',
    specifications: {
      '用户数量': {
        value: '3用户',
        options: ['1用户', '3用户', '5用户', '10用户'],
        priceImpact: 0,
        required: true,
      },
      '账套数量': {
        value: '1个账套',
        options: ['1个账套', '3个账套'],
        priceImpact: 0,
        required: true,
      },
      '存储空间': {
        value: '10GB',
        options: ['5GB', '10GB', '20GB', '50GB'],
        priceImpact: 0,
        required: true,
      },
      '服务期限': {
        value: '1年',
        options: ['1年', '2年', '3年'],
        priceImpact: 0,
        required: true,
      },
      '技术支持': {
        value: '标准支持',
        options: ['标准支持', '高级支持', '专属支持'],
        priceImpact: 0,
        required: true,
      },
    },
    basePrice: 3980,
    recommendedFor: ['小微企业', '成长期企业', '3-10人团队'],
    features: [
      '完整总账管理',
      '应收应付管理',
      '固定资产管理',
      '出纳管理',
      '财务报表',
      '现金流量表',
      '移动应用支持',
      '数据备份',
    ],
    supportLevel: '工作日在线支持 + 电话支持',
    deploymentType: '云端部署',
    category: '财务软件',
  },
];

// 金蝶精斗云配置方案
export const jdyConfigurations: KingsoftConfiguration[] = [
  {
    id: 'jdy-basic',
    productId: '2',
    productName: '金蝶精斗云专业版',
    productSku: 'JDY-PRO-001',
    configurationName: '基础管理套餐',
    description: '适合需要进销存管理的小企业',
    specifications: {
      '用户数量': {
        value: '3用户',
        options: ['3用户', '5用户', '10用户', '20用户'],
        priceImpact: 0,
        required: true,
      },
      '功能模块': {
        value: '进销存',
        options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],
        priceImpact: 0,
        required: true,
      },
      '存储空间': {
        value: '10GB',
        options: ['10GB', '20GB', '50GB', '100GB'],
        priceImpact: 0,
        required: true,
      },
      'API调用': {
        value: '1000次/月',
        options: ['1000次/月', '5000次/月', '10000次/月', '不限'],
        priceImpact: 0,
        required: false,
      },
    },
    basePrice: 4980,
    recommendedFor: ['贸易企业', '零售企业', '小型制造企业'],
    features: [
      '采购管理',
      '销售管理',
      '库存管理',
      '基础财务',
      '报表分析',
      '移动应用',
    ],
    supportLevel: '工作日在线支持',
    deploymentType: '云端部署',
    category: '企业管理软件',
  },
  {
    id: 'jdy-professional',
    productId: '2',
    productName: '金蝶精斗云专业版',
    productSku: 'JDY-PRO-001',
    configurationName: '专业管理套餐',
    description: '适合需要一体化管理的中小企业',
    specifications: {
      '用户数量': {
        value: '5用户',
        options: ['3用户', '5用户', '10用户', '20用户'],
        priceImpact: 0,
        required: true,
      },
      '功能模块': {
        value: '进销存+财务+CRM',
        options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],
        priceImpact: 0,
        required: true,
      },
      '存储空间': {
        value: '20GB',
        options: ['10GB', '20GB', '50GB', '100GB'],
        priceImpact: 0,
        required: true,
      },
      'API调用': {
        value: '5000次/月',
        options: ['1000次/月', '5000次/月', '10000次/月', '不限'],
        priceImpact: 0,
        required: false,
      },
    },
    basePrice: 6980,
    recommendedFor: ['成长期企业', '多业务企业', '需要客户管理的企业'],
    features: [
      '完整进销存管理',
      '财务一体化',
      'CRM客户管理',
      '销售漏斗',
      '业务报表',
      '工作流审批',
      '移动办公',
      'API接口',
    ],
    supportLevel: '工作日在线支持 + 电话支持',
    deploymentType: '云端部署',
    category: '企业管理软件',
  },
];

// K/3 Cloud配置方案
export const k3CloudConfigurations: KingsoftConfiguration[] = [
  {
    id: 'k3-cloud-standard',
    productId: '4',
    productName: '金蝶K/3 Cloud',
    productSku: 'K3-CLOUD-001',
    configurationName: '标准ERP套餐',
    description: '适合中型企业的全面ERP解决方案',
    specifications: {
      '用户数量': {
        value: '50用户',
        options: ['50用户', '100用户', '200用户', '500用户', '不限'],
        priceImpact: 0,
        required: true,
      },
      '部署方式': {
        value: '云端部署',
        options: ['云端部署', '本地部署', '混合部署'],
        priceImpact: 0,
        required: true,
      },
      '功能模块': {
        value: '财务+供应链',
        options: ['财务', '财务+供应链', '财务+供应链+生产', '全模块'],
        priceImpact: 0,
        required: true,
      },
      '数据存储': {
        value: '500GB',
        options: ['500GB', '1TB', '2TB', '5TB', '不限'],
        priceImpact: 0,
        required: true,
      },
    },
    basePrice: 58000,
    recommendedFor: ['中型企业', '制造企业', '集团企业'],
    features: [
      '全面财务管理',
      '供应链管理',
      '生产制造',
      '人力资源',
      '商业智能',
      '移动应用',
      '开放平台',
      '数据分析',
    ],
    supportLevel: '7x24小时专属支持',
    deploymentType: '云端/本地/混合',
    category: 'ERP系统',
  },
];

// 合并所有配置
export const allKingsoftConfigurations: KingsoftConfiguration[] = [
  ...kisCloudConfigurations,
  ...jdyConfigurations,
  ...k3CloudConfigurations,
];

// 根据需求推荐配置
export function recommendConfiguration(
  companySize: string,
  industry: string,
  budget: number,
  requirements: string[]
): KingsoftConfiguration[] {
  return allKingsoftConfigurations.filter(config => {
    // 根据预算筛选
    if (config.basePrice > budget * 1.2) return false;
    
    // 根据公司规模筛选
    const sizeMatch = config.recommendedFor.some(rec => 
      rec.includes(companySize) || 
      (companySize === '小微企业' && rec.includes('小')) ||
      (companySize === '中型企业' && rec.includes('中')) ||
      (companySize === '大型企业' && rec.includes('大'))
    );
    
    if (!sizeMatch) return false;
    
    // 根据功能需求筛选
    const featureMatch = requirements.some(req => 
      config.features.some(feature => 
        feature.toLowerCase().includes(req.toLowerCase())
      )
    );
    
    return featureMatch;
  }).sort((a, b) => Math.abs(a.basePrice - budget) - Math.abs(b.basePrice - budget));
}
