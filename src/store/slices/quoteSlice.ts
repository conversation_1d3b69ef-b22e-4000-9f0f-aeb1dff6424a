import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Quote, PaginationParams } from '@/types';
import { mockApiService } from '@/services/api';

interface QuoteState {
  quotes: Quote[];
  currentQuote: Quote | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  shareLink: string | null;
  analytics: any;
}

const initialState: QuoteState = {
  quotes: [],
  currentQuote: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  shareLink: null,
  analytics: null,
};

export const fetchQuotes = createAsyncThunk(
  'quote/fetchQuotes',
  async (params: PaginationParams) => {
    const response = await mockApiService.getQuotes(params);
    return response;
  }
);

export const createQuote = createAsyncThunk(
  'quote/createQuote',
  async (data: Partial<Quote>) => {
    // 模拟创建报价
    return { ...data, id: Date.now().toString() } as Quote;
  }
);

export const shareQuote = createAsyncThunk(
  'quote/shareQuote',
  async (id: string) => {
    const response = await mockApiService.shareQuote(id);
    return response.data;
  }
);

const quoteSlice = createSlice({
  name: 'quote',
  initialState,
  reducers: {
    clearCurrentQuote: (state) => {
      state.currentQuote = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearShareLink: (state) => {
      state.shareLink = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchQuotes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuotes.fulfilled, (state, action) => {
        state.loading = false;
        state.quotes = action.payload.data;
      })
      .addCase(fetchQuotes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取报价列表失败';
      })
      .addCase(createQuote.fulfilled, (state, action) => {
        state.quotes.unshift(action.payload);
      })
      .addCase(shareQuote.fulfilled, (state, action) => {
        state.shareLink = action.payload;
      });
  },
});

export const { clearCurrentQuote, clearError, clearShareLink } = quoteSlice.actions;
export default quoteSlice.reducer;
