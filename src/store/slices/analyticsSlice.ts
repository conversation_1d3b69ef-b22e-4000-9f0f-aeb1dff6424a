import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { mockApiService } from '@/services/api';

interface AnalyticsState {
  dashboardData: any;
  salesPerformance: any;
  productPerformance: any;
  customerBehavior: any;
  loading: boolean;
  error: string | null;
}

const initialState: AnalyticsState = {
  dashboardData: null,
  salesPerformance: null,
  productPerformance: null,
  customerBehavior: null,
  loading: false,
  error: null,
};

export const fetchDashboardData = createAsyncThunk(
  'analytics/fetchDashboardData',
  async () => {
    const response = await mockApiService.getDashboardData();
    return response.data;
  }
);

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.loading = false;
        state.dashboardData = action.payload;
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取仪表板数据失败';
      });
  },
});

export const { clearError } = analyticsSlice.actions;
export default analyticsSlice.reducer;
