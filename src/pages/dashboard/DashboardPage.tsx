import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button, Table, Tag, Progress, Modal } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  FileTextOutlined,
  DollarOutlined,
  UserOutlined,
  RiseOutlined,
  EyeOutlined,
  CloudOutlined,
  AppstoreOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchDashboardData } from '@/store/slices/analyticsSlice';
import SoftwareQuoteConfigurator from '@/components/SoftwareQuoteConfigurator';
import TeslaStyleConfigurator from '@/components/TeslaStyleConfigurator';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { dashboardData, loading } = useAppSelector((state) => state.analytics);
  const [quoteConfiguratorVisible, setQuoteConfiguratorVisible] = useState(false);
  const [teslaConfiguratorVisible, setTeslaConfiguratorVisible] = useState(false);

  // 使用数据
  console.log('Dashboard data:', dashboardData, 'Loading:', loading);

  useEffect(() => {
    dispatch(fetchDashboardData());
  }, [dispatch]);

  // 模拟数据
  const salesTrendData = [
    { month: '1月', sales: 65000, quotes: 120 },
    { month: '2月', sales: 78000, quotes: 145 },
    { month: '3月', sales: 92000, quotes: 168 },
    { month: '4月', sales: 85000, quotes: 155 },
    { month: '5月', sales: 110000, quotes: 198 },
    { month: '6月', sales: 125000, quotes: 220 },
  ];

  const productPerformanceData = [
    { name: '金蝶云星辰', value: 40, color: '#1890ff' },
    { name: '金蝶精斗云', value: 30, color: '#52c41a' },
    { name: 'K/3 Cloud', value: 20, color: '#faad14' },
    { name: 'EAS Cloud', value: 8, color: '#f5222d' },
    { name: '其他产品', value: 2, color: '#722ed1' },
  ];

  const recentQuotesData = [
    {
      key: '1',
      quoteNumber: 'Q-2024-001',
      customer: '阿里巴巴集团',
      amount: 125000,
      status: 'pending',
      createdAt: '2024-01-15',
    },
    {
      key: '2',
      quoteNumber: 'Q-2024-002',
      customer: '腾讯科技',
      amount: 89000,
      status: 'approved',
      createdAt: '2024-01-14',
    },
    {
      key: '3',
      quoteNumber: 'Q-2024-003',
      customer: '字节跳动',
      amount: 156000,
      status: 'sent',
      createdAt: '2024-01-13',
    },
    {
      key: '4',
      quoteNumber: 'Q-2024-004',
      customer: '美团',
      amount: 78000,
      status: 'draft',
      createdAt: '2024-01-12',
    },
  ];

  const topProductsData = [
    { name: 'Dell PowerEdge R750', sales: 45, revenue: 450000 },
    { name: 'Cisco Catalyst 9300', sales: 38, revenue: 380000 },
    { name: 'HPE ProLiant DL380', sales: 32, revenue: 320000 },
    { name: 'Juniper EX4300', sales: 28, revenue: 280000 },
    { name: 'NetApp FAS2750', sales: 25, revenue: 250000 },
  ];

  const quotesColumns = [
    {
      title: '报价编号',
      dataIndex: 'quoteNumber',
      key: 'quoteNumber',
      render: (text: string) => <Button type="link" style={{ padding: 0, height: 'auto' }}>{text}</Button>,
    },
    {
      title: '客户',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toLocaleString()}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          draft: { color: 'default', text: '草稿' },
          pending: { color: 'processing', text: '待审批' },
          sent: { color: 'warning', text: '已发送' },
          approved: { color: 'success', text: '已批准' },
          rejected: { color: 'error', text: '已拒绝' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" size="small" icon={<EyeOutlined />}>
            查看
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="dashboard-page">
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Text type="secondary">欢迎回来，这里是您的业务概览</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总报价数"
              value={1250}
              prefix={<FileTextOutlined />}
              suffix={
                <span style={{ fontSize: 12, color: '#52c41a' }}>
                  <ArrowUpOutlined /> 12%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={2500000}
              prefix={<DollarOutlined />}
              precision={0}
              formatter={(value) => `¥${value?.toLocaleString()}`}
              suffix={
                <span style={{ fontSize: 12, color: '#52c41a' }}>
                  <ArrowUpOutlined /> 8%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="转化率"
              value={35.2}
              prefix={<RiseOutlined />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃客户"
              value={186}
              prefix={<UserOutlined />}
              suffix={
                <span style={{ fontSize: 12, color: '#f5222d' }}>
                  <ArrowDownOutlined /> 2%
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 金蝶软件配置中心 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <CloudOutlined style={{ color: '#1890ff' }} />
                <span>金蝶软件配置中心</span>
              </Space>
            }
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<UserOutlined />}
                  onClick={() => setTeslaConfiguratorVisible(true)}
                  style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}
                >
                  个性化配置
                </Button>
                <Button
                  icon={<AppstoreOutlined />}
                  onClick={() => setQuoteConfiguratorVisible(true)}
                >
                  专业报价
                </Button>
              </Space>
            }
          >
            <Row gutter={16}>
              <Col span={6}>
                <Card size="small" hoverable onClick={() => setTeslaConfiguratorVisible(true)}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ fontSize: 24 }}>🎯</div>
                    <Text strong>智能推荐</Text>
                    <Text type="secondary">基于行业角色的个性化配置</Text>
                    <Tag color="purple">AI驱动</Tag>
                  </Space>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" hoverable onClick={() => setTeslaConfiguratorVisible(true)}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <CloudOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                    <Text strong>金蝶云星辰</Text>
                    <Text type="secondary">小微企业财务管理</Text>
                    <Tag color="blue">SaaS云服务</Tag>
                  </Space>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" hoverable onClick={() => setTeslaConfiguratorVisible(true)}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <AppstoreOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                    <Text strong>金蝶精斗云</Text>
                    <Text type="secondary">一体化企业管理</Text>
                    <Tag color="green">一体化平台</Tag>
                  </Space>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" hoverable onClick={() => setQuoteConfiguratorVisible(true)}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <FileTextOutlined style={{ fontSize: 24, color: '#faad14' }} />
                    <Text strong>专业报价</Text>
                    <Text type="secondary">详细配置和报价单</Text>
                    <Tag color="orange">CPQ标准</Tag>
                  </Space>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 销售趋势图 */}
        <Col xs={24} lg={16}>
          <Card title="销售趋势" extra={<Button type="link">查看详情</Button>}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="sales" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  name="销售额"
                />
                <Line 
                  type="monotone" 
                  dataKey="quotes" 
                  stroke="#52c41a" 
                  strokeWidth={2}
                  name="报价数量"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 产品性能分布 */}
        <Col xs={24} lg={8}>
          <Card title="产品性能分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={productPerformanceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {productPerformanceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div style={{ marginTop: 16 }}>
              {productPerformanceData.map((item, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      backgroundColor: item.color,
                      borderRadius: 2,
                      marginRight: 8,
                    }}
                  />
                  <Text style={{ fontSize: 12 }}>{item.name}: {item.value}%</Text>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 最近报价 */}
        <Col xs={24} lg={16}>
          <Card 
            title="最近报价" 
            extra={<Button type="link">查看全部</Button>}
          >
            <Table
              columns={quotesColumns}
              dataSource={recentQuotesData}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* 热门产品 */}
        <Col xs={24} lg={8}>
          <Card title="热门产品">
            <div>
              {topProductsData.map((product, index) => (
                <div key={index} style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Text strong style={{ fontSize: 12 }}>{product.name}</Text>
                    <Text type="secondary" style={{ fontSize: 12 }}>{product.sales}台</Text>
                  </div>
                  <Progress 
                    percent={(product.sales / 50) * 100} 
                    showInfo={false} 
                    strokeColor="#1890ff"
                    size="small"
                  />
                  <Text type="secondary" style={{ fontSize: 11 }}>
                    收入: ¥{product.revenue.toLocaleString()}
                  </Text>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 特斯拉风格个性化配置器 */}
      <Modal
        visible={teslaConfiguratorVisible}
        onCancel={() => setTeslaConfiguratorVisible(false)}
        width="100vw"
        style={{ top: 0, paddingBottom: 0, maxWidth: 'none' }}
        bodyStyle={{ padding: 0, height: '100vh', overflow: 'auto' }}
        footer={null}
        closable={true}
        destroyOnClose
      >
        <TeslaStyleConfigurator />
      </Modal>

      {/* 金蝶软件报价配置器模态框 */}
      <Modal
        title="金蝶软件报价配置器"
        visible={quoteConfiguratorVisible}
        onCancel={() => setQuoteConfiguratorVisible(false)}
        width={1400}
        footer={null}
        style={{ top: 20 }}
        destroyOnClose
      >
        <SoftwareQuoteConfigurator />
      </Modal>
    </div>
  );
};

export default DashboardPage;
