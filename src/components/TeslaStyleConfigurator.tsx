import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Space,
  Steps,
  Radio,
  Checkbox,
  Progress,
  Tag,
  Avatar,
  Statistic,
  Timeline,
  Alert,
  Tooltip,
  Badge,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  StarOutlined,
  TrophyOutlined,
  BulbOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import { allIndustries, getRecommendedConfiguration, Industry, Role, PainPoint } from '@/data/industryRoleConfig';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface ConfigurationState {
  industry: string;
  role: string;
  companySize: 'small' | 'medium' | 'large';
  selectedPainPoints: string[];
  currentStep: number;
}

const TeslaStyleConfigurator: React.FC = () => {
  const [config, setConfig] = useState<ConfigurationState>({
    industry: '',
    role: '',
    companySize: 'small',
    selectedPainPoints: [],
    currentStep: 0,
  });

  const [recommendation, setRecommendation] = useState<any>(null);
  const [selectedIndustry, setSelectedIndustry] = useState<Industry | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  useEffect(() => {
    if (config.industry) {
      const industry = allIndustries.find(i => i.id === config.industry);
      setSelectedIndustry(industry || null);
    }
  }, [config.industry]);

  useEffect(() => {
    if (selectedIndustry && config.role) {
      const role = selectedIndustry.commonRoles.find(r => r.id === config.role);
      setSelectedRole(role || null);
    }
  }, [selectedIndustry, config.role]);

  useEffect(() => {
    if (config.industry && config.role && config.selectedPainPoints.length > 0) {
      try {
        const rec = getRecommendedConfiguration(
          config.industry,
          config.role,
          config.companySize,
          config.selectedPainPoints
        );
        setRecommendation(rec);
      } catch (error) {
        console.error('获取推荐配置失败:', error);
      }
    }
  }, [config]);

  const handleNext = () => {
    setConfig(prev => ({ ...prev, currentStep: prev.currentStep + 1 }));
  };

  const handlePrev = () => {
    setConfig(prev => ({ ...prev, currentStep: prev.currentStep - 1 }));
  };

  const renderIndustrySelection = () => (
    <div style={{ textAlign: 'center', padding: '40px 0' }}>
      <Title level={2} style={{ marginBottom: 8 }}>选择您的行业</Title>
      <Paragraph style={{ fontSize: 16, color: '#666', marginBottom: 40 }}>
        我们将根据您的行业特点，为您推荐最适合的解决方案
      </Paragraph>
      
      <Row gutter={[24, 24]} justify="center">
        {allIndustries.map(industry => (
          <Col key={industry.id} span={8}>
            <Card
              hoverable
              className={config.industry === industry.id ? 'selected-card' : ''}
              onClick={() => setConfig(prev => ({ ...prev, industry: industry.id, role: '', selectedPainPoints: [] }))}
              style={{
                height: 280,
                border: config.industry === industry.id ? '3px solid #1890ff' : '1px solid #d9d9d9',
                borderRadius: 12,
                transition: 'all 0.3s ease',
              }}
              bodyStyle={{ padding: 24, textAlign: 'center' }}
            >
              <div style={{ fontSize: 48, marginBottom: 16 }}>
                {industry.icon === 'factory' && '🏭'}
                {industry.icon === 'shop' && '🏪'}
                {industry.icon === 'customer-service' && '💼'}
              </div>
              <Title level={4} style={{ marginBottom: 12 }}>{industry.name}</Title>
              <Paragraph style={{ color: '#666', fontSize: 14, lineHeight: 1.6 }}>
                {industry.description}
              </Paragraph>
              <div style={{ marginTop: 16 }}>
                <Tag color="blue">{industry.painPoints.length} 个痛点解决方案</Tag>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  const renderRoleSelection = () => {
    if (!selectedIndustry) return null;

    return (
      <div style={{ padding: '40px 0' }}>
        <div style={{ textAlign: 'center', marginBottom: 40 }}>
          <Title level={2}>您在公司的角色是？</Title>
          <Paragraph style={{ fontSize: 16, color: '#666' }}>
            不同角色关注的重点不同，我们将为您提供个性化的功能推荐
          </Paragraph>
        </div>

        <Row gutter={[24, 24]}>
          {selectedIndustry.commonRoles.map(role => (
            <Col key={role.id} span={12}>
              <Card
                hoverable
                className={config.role === role.id ? 'selected-card' : ''}
                onClick={() => setConfig(prev => ({ ...prev, role: role.id, selectedPainPoints: [] }))}
                style={{
                  border: config.role === role.id ? '3px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 12,
                  height: 320,
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>
                  <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
                  <div style={{ flex: 1 }}>
                    <Title level={4} style={{ marginBottom: 8 }}>{role.name}</Title>
                    <Paragraph style={{ color: '#666', marginBottom: 16 }}>
                      {role.description}
                    </Paragraph>
                    
                    <div style={{ marginBottom: 12 }}>
                      <Text strong style={{ color: '#1890ff' }}>主要职责：</Text>
                      <div style={{ marginTop: 4 }}>
                        {role.responsibilities.slice(0, 3).map((resp, index) => (
                          <Tag key={index} style={{ marginBottom: 4 }}>{resp}</Tag>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <Text strong style={{ color: '#f5222d' }}>关注痛点：</Text>
                      <div style={{ marginTop: 4 }}>
                        {role.painPoints.slice(0, 2).map((pain, index) => (
                          <Tag key={index} color="red" style={{ marginBottom: 4 }}>{pain}</Tag>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  const renderPainPointSelection = () => {
    if (!selectedIndustry) return null;

    return (
      <div style={{ padding: '40px 0' }}>
        <div style={{ textAlign: 'center', marginBottom: 40 }}>
          <Title level={2}>您当前面临的主要挑战是？</Title>
          <Paragraph style={{ fontSize: 16, color: '#666' }}>
            选择您最关心的业务痛点，我们将为您推荐针对性的解决方案
          </Paragraph>
        </div>

        <Row gutter={[24, 24]}>
          {selectedIndustry.painPoints.map(painPoint => (
            <Col key={painPoint.id} span={24}>
              <Card
                hoverable
                className={config.selectedPainPoints.includes(painPoint.id) ? 'selected-card' : ''}
                onClick={() => {
                  const newPainPoints = config.selectedPainPoints.includes(painPoint.id)
                    ? config.selectedPainPoints.filter(p => p !== painPoint.id)
                    : [...config.selectedPainPoints, painPoint.id];
                  setConfig(prev => ({ ...prev, selectedPainPoints: newPainPoints }));
                }}
                style={{
                  border: config.selectedPainPoints.includes(painPoint.id) ? '3px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 12,
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
                      <Title level={4} style={{ margin: 0 }}>{painPoint.title}</Title>
                      <Badge 
                        count={`${painPoint.frequency}%`} 
                        style={{ backgroundColor: painPoint.impact === 'high' ? '#f5222d' : '#faad14' }}
                      />
                    </div>
                    <Paragraph style={{ color: '#666', marginBottom: 16 }}>
                      {painPoint.description}
                    </Paragraph>
                    
                    {painPoint.solutions.map(solution => (
                      <div key={solution.id} style={{ marginBottom: 8 }}>
                        <Space>
                          <BulbOutlined style={{ color: '#52c41a' }} />
                          <Text strong style={{ color: '#52c41a' }}>{solution.title}</Text>
                          <Tag color="green">{solution.estimatedROI}</Tag>
                        </Space>
                      </div>
                    ))}
                  </div>
                  
                  <div style={{ textAlign: 'center' }}>
                    <Progress
                      type="circle"
                      percent={painPoint.frequency}
                      width={60}
                      format={() => `${painPoint.frequency}%`}
                      strokeColor={painPoint.impact === 'high' ? '#f5222d' : '#faad14'}
                    />
                    <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                      行业普遍性
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  const renderRecommendation = () => {
    if (!recommendation || !selectedIndustry || !selectedRole) return null;

    return (
      <div style={{ padding: '40px 0' }}>
        <div style={{ textAlign: 'center', marginBottom: 40 }}>
          <Title level={2}>🎉 为您量身定制的解决方案</Title>
          <Paragraph style={{ fontSize: 16, color: '#666' }}>
            基于您的行业特点和角色需求，我们为您推荐以下配置
          </Paragraph>
        </div>

        <Row gutter={[24, 24]}>
          <Col span={16}>
            <Card title="推荐配置详情" style={{ marginBottom: 24 }}>
              <Timeline>
                <Timeline.Item 
                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  color="green"
                >
                  <div>
                    <Text strong>产品选择：</Text>
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {recommendation.recommendedProduct === 'kis-cloud' ? '金蝶云星辰' : 
                       recommendation.recommendedProduct === 'jdy' ? '金蝶精斗云' : 'K/3 Cloud'}
                    </Tag>
                  </div>
                  <div style={{ marginTop: 8, color: '#666' }}>
                    最适合{selectedIndustry.name}{selectedRole.name}的专业解决方案
                  </div>
                </Timeline.Item>
                
                <Timeline.Item 
                  dot={<RocketOutlined style={{ color: '#1890ff' }} />}
                  color="blue"
                >
                  <div>
                    <Text strong>核心功能模块：</Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    {recommendation.recommendedModules.map((module: string, index: number) => (
                      <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
                        {module}
                      </Tag>
                    ))}
                  </div>
                </Timeline.Item>
                
                <Timeline.Item 
                  dot={<TeamOutlined style={{ color: '#faad14' }} />}
                  color="orange"
                >
                  <div>
                    <Text strong>实施服务：</Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    {recommendation.recommendedServices.map((service: string, index: number) => (
                      <Tag key={index} color="orange" style={{ marginBottom: 4 }}>
                        {service}
                      </Tag>
                    ))}
                  </div>
                </Timeline.Item>
              </Timeline>
            </Card>

            <Alert
              message="投资回报预期"
              description={
                <div>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text>• {recommendation.roi}</Text>
                    <Text>• 预计提升工作效率 30-50%</Text>
                    <Text>• 减少人工错误 80% 以上</Text>
                    <Text>• 提升决策效率 60% 以上</Text>
                  </Space>
                </div>
              }
              type="success"
              showIcon
              icon={<TrophyOutlined />}
            />
          </Col>

          <Col span={8}>
            <Card title="价格预估" style={{ marginBottom: 24 }}>
              <div style={{ textAlign: 'center' }}>
                <Statistic
                  title="预估总价"
                  value={recommendation.estimatedPrice}
                  precision={0}
                  prefix="¥"
                  valueStyle={{ color: '#1890ff', fontSize: 32 }}
                />
                <div style={{ marginTop: 16, color: '#666' }}>
                  <Space direction="vertical" size="small">
                    <Text>推荐用户数：{recommendation.estimatedUsers} 人</Text>
                    <Text>包含实施服务</Text>
                    <Text>首年价格，续费享优惠</Text>
                  </Space>
                </div>
              </div>
            </Card>

            <Card title="为什么选择我们？" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <StarOutlined style={{ color: '#faad14' }} />
                  <Text>行业领先的财务软件</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <RiseOutlined style={{ color: '#52c41a' }} />
                  <Text>500万+ 企业的选择</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <ClockCircleOutlined style={{ color: '#1890ff' }} />
                  <Text>7x24小时专业支持</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <DollarOutlined style={{ color: '#722ed1' }} />
                  <Text>灵活的付费方式</Text>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        <div style={{ textAlign: 'center', marginTop: 40 }}>
          <Space size="large">
            <Button type="primary" size="large" style={{ minWidth: 120 }}>
              立即购买
            </Button>
            <Button size="large" style={{ minWidth: 120 }}>
              申请试用
            </Button>
            <Button size="large" style={{ minWidth: 120 }}>
              联系顾问
            </Button>
          </Space>
        </div>
      </div>
    );
  };

  const steps = [
    { title: '选择行业', content: renderIndustrySelection() },
    { title: '确定角色', content: renderRoleSelection() },
    { title: '识别痛点', content: renderPainPointSelection() },
    { title: '推荐方案', content: renderRecommendation() },
  ];

  const canProceed = () => {
    switch (config.currentStep) {
      case 0: return config.industry !== '';
      case 1: return config.role !== '';
      case 2: return config.selectedPainPoints.length > 0;
      default: return true;
    }
  };

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
      <div style={{ padding: '40px 24px', maxWidth: 1200, margin: '0 auto' }}>
        <Card style={{ borderRadius: 16, boxShadow: '0 20px 40px rgba(0,0,0,0.1)' }}>
          <div style={{ padding: '20px 0' }}>
            <Steps 
              current={config.currentStep} 
              style={{ marginBottom: 40 }}
              size="small"
            >
              {steps.map(item => (
                <Step key={item.title} title={item.title} />
              ))}
            </Steps>

            <div style={{ minHeight: 500 }}>
              {steps[config.currentStep].content}
            </div>

            <div style={{ textAlign: 'center', marginTop: 40, borderTop: '1px solid #f0f0f0', paddingTop: 24 }}>
              <Space>
                {config.currentStep > 0 && (
                  <Button size="large" onClick={handlePrev}>
                    上一步
                  </Button>
                )}
                {config.currentStep < steps.length - 1 && (
                  <Button 
                    type="primary" 
                    size="large" 
                    onClick={handleNext}
                    disabled={!canProceed()}
                  >
                    下一步
                  </Button>
                )}
              </Space>
            </div>
          </div>
        </Card>
      </div>

      <style>{`
        .selected-card {
          transform: translateY(-4px) !important;
          box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2) !important;
        }
      `}</style>
    </div>
  );
};

export default TeslaStyleConfigurator;
