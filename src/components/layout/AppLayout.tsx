import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Space, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  SettingOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  FileTextOutlined,
  UserOutlined,
  Bar<PERSON>hartOutlined,
  TeamOutlined,
  ApiOutlined,
  BellOutlined,
  LogoutOutlined,
  ProfileOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/store';
import { logout } from '@/store/slices/authSlice';
import { setTheme } from '@/store/slices/appSlice';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  const { user } = useAppSelector((state) => state.auth);
  const { notifications, theme } = useAppSelector((state) => state.app);

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/products',
      icon: <AppstoreOutlined />,
      label: '产品管理',
      children: [
        { key: '/products/list', label: '产品列表' },
        { key: '/products/categories', label: '产品分类' },
      ],
    },
    {
      key: '/configurator',
      icon: <SettingOutlined />,
      label: '产品配置',
    },
    {
      key: '/bundles',
      icon: <ShoppingCartOutlined />,
      label: '产品组合',
    },
    {
      key: '/pricing',
      icon: <DollarOutlined />,
      label: '价格管理',
    },
    {
      key: '/quotes',
      icon: <FileTextOutlined />,
      label: '报价管理',
    },
    {
      key: '/customer-portal',
      icon: <UserOutlined />,
      label: '客户门户',
    },
    {
      key: '/analytics',
      icon: <BarChartOutlined />,
      label: '数据分析',
    },
    {
      key: '/users',
      icon: <TeamOutlined />,
      label: '用户管理',
    },
    {
      key: '/integration',
      icon: <ApiOutlined />,
      label: '系统集成',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const toggleTheme = () => {
    dispatch(setTheme({
      mode: theme.mode === 'light' ? 'dark' : 'light',
    }));
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const unreadNotifications = notifications.filter(n => !n.read).length;

  return (
    <Layout className="app-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="app-sider"
        width={240}
        collapsedWidth={80}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? 0 : '0 24px',
          borderBottom: '1px solid #f0f0f0'
        }}>
          {!collapsed && (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              Link CPQ
            </Title>
          )}
          {collapsed && (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              CPQ
            </Title>
          )}
        </div>
        
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header className="app-header" style={{ 
          padding: '0 24px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          background: '#fff'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          
          <Space size="large">
            <Badge count={unreadNotifications} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: '16px' }}
                onClick={() => navigate('/notifications')}
              />
            </Badge>
            
            <Button
              type="text"
              onClick={toggleTheme}
              style={{ fontSize: '16px' }}
            >
              {theme.mode === 'light' ? '🌙' : '☀️'}
            </Button>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  src={user?.avatar}
                  icon={<UserOutlined />}
                  size="small"
                />
                <span>{user?.firstName} {user?.lastName}</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content className="app-content">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
