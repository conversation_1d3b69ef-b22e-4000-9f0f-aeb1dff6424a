import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Space,
  Divider,
  Form,
  Select,
  InputNumber,
  Checkbox,
  Table,
  Alert,
  Statistic,
  Steps,
  Radio,
  Tag,
  Tooltip,
} from 'antd';
import {
  CalculatorOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  SendOutlined,
} from '@ant-design/icons';
import { allSoftwareProducts, calculateSoftwareQuote, SoftwareProduct } from '@/data/softwareQuoteData';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Step } = Steps;

interface QuoteConfiguration {
  productId: string;
  versionId: string;
  userCount: number;
  selectedModules: string[];
  selectedServices: string[];
  deploymentId: string;
  supportId: string;
  servicePeriod: number;
}

const SoftwareQuoteConfigurator: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState<SoftwareProduct | null>(null);
  const [configuration, setConfiguration] = useState<QuoteConfiguration>({
    productId: '',
    versionId: '',
    userCount: 1,
    selectedModules: [],
    selectedServices: [],
    deploymentId: '',
    supportId: '',
    servicePeriod: 1,
  });
  const [quoteResult, setQuoteResult] = useState<any>(null);

  useEffect(() => {
    if (configuration.productId) {
      const product = allSoftwareProducts.find(p => p.id === configuration.productId);
      setSelectedProduct(product || null);
      
      // 设置默认值
      if (product && !configuration.versionId) {
        const recommendedVersion = product.versions.find(v => v.recommended) || product.versions[0];
        const defaultDeployment = product.deploymentOptions[0];
        const defaultSupport = product.supportLevels[0];
        const requiredModules = product.modules.filter(m => m.required).map(m => m.id);
        
        setConfiguration(prev => ({
          ...prev,
          versionId: recommendedVersion.id,
          deploymentId: defaultDeployment.id,
          supportId: defaultSupport.id,
          selectedModules: requiredModules,
        }));
      }
    }
  }, [configuration.productId]);

  useEffect(() => {
    if (selectedProduct && configuration.versionId && configuration.deploymentId && configuration.supportId) {
      try {
        const result = calculateSoftwareQuote(
          configuration.productId,
          configuration.versionId,
          configuration.userCount,
          configuration.selectedModules,
          configuration.selectedServices,
          configuration.deploymentId,
          configuration.supportId,
          configuration.servicePeriod
        );
        setQuoteResult(result);
      } catch (error) {
        console.error('报价计算错误:', error);
      }
    }
  }, [selectedProduct, configuration]);

  const handleNext = () => {
    setCurrentStep(prev => Math.min(prev + 1, 3));
  };

  const handlePrev = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleProductSelect = (productId: string) => {
    setConfiguration(prev => ({
      ...prev,
      productId,
      versionId: '',
      selectedModules: [],
      selectedServices: [],
      deploymentId: '',
      supportId: '',
    }));
  };

  const handleModuleChange = (moduleIds: string[]) => {
    if (!selectedProduct) return;
    
    // 确保必需模块始终被选中
    const requiredModules = selectedProduct.modules.filter(m => m.required).map(m => m.id);
    const allSelected = Array.from(new Set([...requiredModules, ...moduleIds]));
    
    setConfiguration(prev => ({
      ...prev,
      selectedModules: allSelected,
    }));
  };

  const renderProductSelection = () => (
    <Card title="选择产品" size="small">
      <Row gutter={16}>
        {allSoftwareProducts.map(product => (
          <Col span={12} key={product.id}>
            <Card
              hoverable
              size="small"
              className={configuration.productId === product.id ? 'selected-card' : ''}
              onClick={() => handleProductSelect(product.id)}
              style={{
                border: configuration.productId === product.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
              }}
            >
              <Title level={5}>{product.name}</Title>
              <Paragraph ellipsis={{ rows: 2 }}>{product.description}</Paragraph>
              <Space wrap>
                {product.targetCustomer.map(target => (
                  <Tag key={target}>{target}</Tag>
                ))}
              </Space>
              <Divider />
              <Text strong>起价: ¥{product.basePrice}/用户/年</Text>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );

  const renderVersionSelection = () => {
    if (!selectedProduct) return null;

    return (
      <Card title="选择版本" size="small">
        <Radio.Group
          value={configuration.versionId}
          onChange={(e) => setConfiguration(prev => ({ ...prev, versionId: e.target.value }))}
        >
          <Row gutter={16}>
            {selectedProduct.versions.map(version => (
              <Col span={8} key={version.id}>
                <Card size="small" style={{ marginBottom: 16 }}>
                  <Radio value={version.id}>
                    <Title level={5}>
                      {version.name}
                      {version.recommended && <Tag color="gold" style={{ marginLeft: 8 }}>推荐</Tag>}
                    </Title>
                  </Radio>
                  <Paragraph>{version.description}</Paragraph>
                  <Text type="secondary">最大用户数: {version.maxUsers}</Text>
                  <Divider />
                  <Text strong>
                    价格: ¥{(selectedProduct.basePrice * version.priceMultiplier).toFixed(0)}/用户/年
                  </Text>
                </Card>
              </Col>
            ))}
          </Row>
        </Radio.Group>
      </Card>
    );
  };

  const renderConfiguration = () => {
    if (!selectedProduct) return null;

    return (
      <Row gutter={16}>
        <Col span={12}>
          <Card title="基础配置" size="small" style={{ marginBottom: 16 }}>
            <Form layout="vertical">
              <Form.Item label="用户数量">
                <InputNumber
                  min={1}
                  max={selectedProduct.versions.find(v => v.id === configuration.versionId)?.maxUsers || 999}
                  value={configuration.userCount}
                  onChange={(value) => setConfiguration(prev => ({ ...prev, userCount: value || 1 }))}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item label="服务期限">
                <Select
                  value={configuration.servicePeriod}
                  onChange={(value) => setConfiguration(prev => ({ ...prev, servicePeriod: value }))}
                >
                  <Option value={1}>1年</Option>
                  <Option value={2}>2年</Option>
                  <Option value={3}>3年</Option>
                  <Option value={5}>5年</Option>
                </Select>
              </Form.Item>
            </Form>
          </Card>

          <Card title="功能模块" size="small" style={{ marginBottom: 16 }}>
            <Checkbox.Group
              value={configuration.selectedModules}
              onChange={handleModuleChange}
            >
              <Row>
                {selectedProduct.modules.map(module => (
                  <Col span={24} key={module.id} style={{ marginBottom: 8 }}>
                    <Checkbox
                      value={module.id}
                      disabled={module.required}
                    >
                      <Space>
                        <Text strong>{module.name}</Text>
                        {module.required && <Tag color="red">必需</Tag>}
                        <Tooltip title={module.description}>
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    </Checkbox>
                    <br />
                    <Text type="secondary" style={{ marginLeft: 24 }}>
                      {module.price > 0 && (
                        module.priceType === 'per_user' 
                          ? `¥${module.price}/用户/年` 
                          : `¥${module.price}/年`
                      )}
                    </Text>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="部署方式" size="small" style={{ marginBottom: 16 }}>
            <Radio.Group
              value={configuration.deploymentId}
              onChange={(e) => setConfiguration(prev => ({ ...prev, deploymentId: e.target.value }))}
            >
              {selectedProduct.deploymentOptions.map(option => (
                <Radio key={option.id} value={option.id} style={{ display: 'block', marginBottom: 8 }}>
                  <Space direction="vertical" size="small">
                    <Text strong>{option.name}</Text>
                    <Text type="secondary">{option.description}</Text>
                    {option.priceImpact > 0 && (
                      <Text type="warning">+¥{option.priceImpact}/年</Text>
                    )}
                  </Space>
                </Radio>
              ))}
            </Radio.Group>
          </Card>

          <Card title="技术支持" size="small" style={{ marginBottom: 16 }}>
            <Radio.Group
              value={configuration.supportId}
              onChange={(e) => setConfiguration(prev => ({ ...prev, supportId: e.target.value }))}
            >
              {selectedProduct.supportLevels.map(level => (
                <Radio key={level.id} value={level.id} style={{ display: 'block', marginBottom: 8 }}>
                  <Space direction="vertical" size="small">
                    <Text strong>{level.name}</Text>
                    <Text type="secondary">{level.description}</Text>
                    <Text type="secondary">响应时间: {level.responseTime}</Text>
                    {level.price > 0 && (
                      <Text type="warning">+¥{level.price}/年</Text>
                    )}
                  </Space>
                </Radio>
              ))}
            </Radio.Group>
          </Card>

          <Card title="实施服务" size="small">
            <Checkbox.Group
              value={configuration.selectedServices}
              onChange={(serviceIds) => setConfiguration(prev => ({ ...prev, selectedServices: serviceIds }))}
            >
              {selectedProduct.services.map(service => (
                <Checkbox key={service.id} value={service.id} style={{ display: 'block', marginBottom: 8 }}>
                  <Space direction="vertical" size="small">
                    <Text strong>{service.name}</Text>
                    <Text type="secondary">{service.description}</Text>
                    <Text type="secondary">周期: {service.duration}</Text>
                    <Text type="warning">¥{service.price}</Text>
                  </Space>
                </Checkbox>
              ))}
            </Checkbox.Group>
          </Card>
        </Col>
      </Row>
    );
  };

  const renderQuoteResult = () => {
    if (!quoteResult) return null;

    const columns = [
      {
        title: '项目',
        dataIndex: 'item',
        key: 'item',
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: '单位',
        dataIndex: 'unit',
        key: 'unit',
      },
      {
        title: '单价',
        dataIndex: 'unitPrice',
        key: 'unitPrice',
        render: (price: number) => `¥${price.toLocaleString()}`,
      },
      {
        title: '期限',
        dataIndex: 'period',
        key: 'period',
        render: (period: number) => `${period}年`,
      },
      {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        render: (amount: number) => `¥${amount.toLocaleString()}`,
      },
    ];

    return (
      <Card title="报价结果" size="small">
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="软件费用"
              value={quoteResult.baseAmount}
              precision={0}
              prefix="¥"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="模块费用"
              value={quoteResult.moduleAmount}
              precision={0}
              prefix="¥"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="服务费用"
              value={quoteResult.serviceAmount + quoteResult.deploymentAmount + quoteResult.supportAmount}
              precision={0}
              prefix="¥"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总计"
              value={quoteResult.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#cf1322', fontWeight: 'bold' }}
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={quoteResult.breakdown}
          pagination={false}
          size="small"
          rowKey="item"
          summary={() => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={5}>
                <Text strong>总计</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                <Text strong>¥{quoteResult.totalAmount.toLocaleString()}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          )}
        />

        <Divider />

        <Space>
          <Button type="primary" icon={<FileTextOutlined />}>
            生成报价单
          </Button>
          <Button icon={<DownloadOutlined />}>
            导出PDF
          </Button>
          <Button icon={<SendOutlined />}>
            发送客户
          </Button>
        </Space>
      </Card>
    );
  };

  const steps = [
    {
      title: '选择产品',
      content: renderProductSelection(),
    },
    {
      title: '选择版本',
      content: renderVersionSelection(),
    },
    {
      title: '配置选项',
      content: renderConfiguration(),
    },
    {
      title: '生成报价',
      content: renderQuoteResult(),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <CalculatorOutlined />
            <span>金蝶软件报价配置器</span>
          </Space>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div style={{ minHeight: '500px' }}>
          {steps[currentStep].content}
        </div>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button
                type="primary"
                onClick={handleNext}
                disabled={
                  (currentStep === 0 && !configuration.productId) ||
                  (currentStep === 1 && !configuration.versionId)
                }
              >
                下一步
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button type="primary" icon={<CheckCircleOutlined />}>
                确认报价
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SoftwareQuoteConfigurator;
