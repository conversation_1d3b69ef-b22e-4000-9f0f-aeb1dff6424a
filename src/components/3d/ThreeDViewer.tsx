import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Environment, ContactShadows, Html, useProgress } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { Suspense } from 'react';
import { Button, Card, Slider, Select, ColorPicker, Space, Spin, Alert, Tooltip } from 'antd';
import { 
  RotateLeftOutlined, 
  RotateRightOutlined, 
  ZoomInOutlined, 
  ZoomOutOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import * as THREE from 'three';
import './ThreeDViewer.less';

const { Option } = Select;

interface ThreeDViewerProps {
  modelUrl: string;
  productId: number;
  configuration?: Record<string, any>;
  onConfigurationChange?: (config: Record<string, any>) => void;
  width?: number;
  height?: number;
  showControls?: boolean;
  showEnvironment?: boolean;
  autoRotate?: boolean;
  enableInteraction?: boolean;
}

interface ModelProps {
  url: string;
  configuration: Record<string, any>;
  onLoad?: (model: any) => void;
}

// 3D模型组件
const Model: React.FC<ModelProps> = ({ url, configuration, onLoad }) => {
  const gltf = useLoader(GLTFLoader, url);
  const meshRef = useRef<THREE.Group>(null);

  useEffect(() => {
    if (gltf && onLoad) {
      onLoad(gltf);
    }
  }, [gltf, onLoad]);

  useEffect(() => {
    if (meshRef.current && configuration) {
      // 应用配置变更
      applyConfiguration(meshRef.current, configuration);
    }
  }, [configuration]);

  const applyConfiguration = (model: THREE.Group, config: Record<string, any>) => {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 应用颜色变更
        if (config.color && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => {
              if (mat instanceof THREE.MeshStandardMaterial) {
                mat.color.setHex(config.color.replace('#', '0x'));
              }
            });
          } else if (child.material instanceof THREE.MeshStandardMaterial) {
            child.material.color.setHex(config.color.replace('#', '0x'));
          }
        }

        // 应用材质变更
        if (config.material && child.material) {
          const materialProps = getMaterialProperties(config.material);
          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => {
              if (mat instanceof THREE.MeshStandardMaterial) {
                Object.assign(mat, materialProps);
              }
            });
          } else if (child.material instanceof THREE.MeshStandardMaterial) {
            Object.assign(child.material, materialProps);
          }
        }

        // 应用透明度变更
        if (config.opacity !== undefined && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => {
              mat.transparent = config.opacity < 1;
              mat.opacity = config.opacity;
            });
          } else {
            child.material.transparent = config.opacity < 1;
            child.material.opacity = config.opacity;
          }
        }
      }
    });
  };

  const getMaterialProperties = (materialType: string) => {
    switch (materialType) {
      case 'metal':
        return { metalness: 0.9, roughness: 0.1 };
      case 'plastic':
        return { metalness: 0.0, roughness: 0.8 };
      case 'glass':
        return { metalness: 0.0, roughness: 0.0, transparent: true, opacity: 0.3 };
      case 'wood':
        return { metalness: 0.0, roughness: 0.9 };
      case 'leather':
        return { metalness: 0.0, roughness: 0.7 };
      default:
        return { metalness: 0.5, roughness: 0.5 };
    }
  };

  return (
    <group ref={meshRef}>
      <primitive object={gltf.scene} />
    </group>
  );
};

// 加载进度组件
const Loader = () => {
  const { progress } = useProgress();
  return (
    <Html center>
      <div className="loader-container">
        <Spin size="large" />
        <div className="loader-text">加载中... {Math.round(progress)}%</div>
      </div>
    </Html>
  );
};

// 主要的3D查看器组件
const ThreeDViewer: React.FC<ThreeDViewerProps> = ({
  modelUrl,
  productId,
  configuration = {},
  onConfigurationChange,
  width = 800,
  height = 600,
  showControls = true,
  showEnvironment = true,
  autoRotate = false,
  enableInteraction = true
}) => {
  const [currentConfig, setCurrentConfig] = useState(configuration);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([5, 5, 5]);
  const [zoom, setZoom] = useState(1);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const controlsRef = useRef<any>(null);

  // 可用的材质选项
  const materialOptions = [
    { label: '金属', value: 'metal' },
    { label: '塑料', value: 'plastic' },
    { label: '玻璃', value: 'glass' },
    { label: '木材', value: 'wood' },
    { label: '皮革', value: 'leather' }
  ];

  // 可用的颜色选项
  const colorOptions = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', 
    '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080'
  ];

  const handleConfigurationChange = useCallback((key: string, value: any) => {
    const newConfig = { ...currentConfig, [key]: value };
    setCurrentConfig(newConfig);
    onConfigurationChange?.(newConfig);
  }, [currentConfig, onConfigurationChange]);

  const handleModelLoad = useCallback((model: any) => {
    setModelLoaded(true);
    setError(null);
    
    // 自动调整相机位置
    const box = new THREE.Box3().setFromObject(model.scene);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());
    const maxDim = Math.max(size.x, size.y, size.z);
    const distance = maxDim * 2;
    
    setCameraPosition([distance, distance, distance]);
  }, []);

  const handleError = useCallback((error: any) => {
    console.error('3D模型加载失败:', error);
    setError('3D模型加载失败，请检查网络连接或联系技术支持');
    setModelLoaded(false);
  }, []);

  const resetCamera = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset();
    }
    setZoom(1);
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      canvasRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  const downloadModel = useCallback(async () => {
    try {
      // 实现模型下载逻辑
      const response = await fetch(`/api/products/${productId}/3d-model/download`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `product_${productId}_model.glb`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载模型失败:', error);
    }
  }, [productId]);

  const captureScreenshot = useCallback(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const dataURL = canvas.toDataURL('image/png');
      const a = document.createElement('a');
      a.href = dataURL;
      a.download = `product_${productId}_screenshot.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }, [productId]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  if (error) {
    return (
      <Card style={{ width, height }}>
        <Alert
          message="3D模型加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => window.location.reload()}>
              重新加载
            </Button>
          }
        />
      </Card>
    );
  }

  return (
    <div className="threed-viewer-container" style={{ width, height }}>
      <div className="threed-canvas-container" style={{ width: '100%', height: showControls ? height - 120 : height }}>
        <Canvas
          ref={canvasRef}
          camera={{ position: cameraPosition, fov: 50 }}
          onError={handleError}
        >
          <Suspense fallback={<Loader />}>
            {showEnvironment && (
              <>
                <Environment preset="studio" />
                <ContactShadows position={[0, -1, 0]} opacity={0.4} scale={10} blur={2} far={1} />
              </>
            )}
            
            <ambientLight intensity={0.5} />
            <directionalLight position={[10, 10, 5]} intensity={1} />
            <pointLight position={[-10, -10, -5]} intensity={0.5} />
            
            <Model 
              url={modelUrl} 
              configuration={currentConfig}
              onLoad={handleModelLoad}
            />
            
            {enableInteraction && (
              <OrbitControls
                ref={controlsRef}
                autoRotate={autoRotate}
                autoRotateSpeed={2}
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                minDistance={1}
                maxDistance={50}
              />
            )}
          </Suspense>
        </Canvas>
      </div>

      {showControls && (
        <Card className="threed-controls" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 基本控制按钮 */}
            <Space wrap>
              <Tooltip title="重置视角">
                <Button icon={<ReloadOutlined />} onClick={resetCamera} />
              </Tooltip>
              <Tooltip title="全屏">
                <Button icon={<FullscreenOutlined />} onClick={toggleFullscreen} />
              </Tooltip>
              <Tooltip title="下载模型">
                <Button icon={<DownloadOutlined />} onClick={downloadModel} />
              </Tooltip>
              <Tooltip title="截图">
                <Button icon={<SettingOutlined />} onClick={captureScreenshot} />
              </Tooltip>
            </Space>

            {/* 配置控制 */}
            <Space wrap>
              <div>
                <label>材质：</label>
                <Select
                  style={{ width: 100 }}
                  value={currentConfig.material}
                  onChange={(value) => handleConfigurationChange('material', value)}
                  placeholder="选择材质"
                >
                  {materialOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <label>颜色：</label>
                <ColorPicker
                  value={currentConfig.color || '#FFFFFF'}
                  onChange={(color) => handleConfigurationChange('color', color.toHexString())}
                  presets={[
                    { label: '推荐', colors: colorOptions }
                  ]}
                />
              </div>

              <div>
                <label>透明度：</label>
                <Slider
                  style={{ width: 100 }}
                  min={0}
                  max={1}
                  step={0.1}
                  value={currentConfig.opacity || 1}
                  onChange={(value) => handleConfigurationChange('opacity', value)}
                />
              </div>
            </Space>
          </Space>
        </Card>
      )}
    </div>
  );
};

export default ThreeDViewer;
