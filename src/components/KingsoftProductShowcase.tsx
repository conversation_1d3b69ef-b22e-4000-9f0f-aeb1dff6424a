import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Tag,
  Typography,
  Space,
  Divider,
  Tabs,
  List,
  Modal,
  Form,
  Select,
  InputNumber,
  Alert,
  Statistic,
} from 'antd';
import {
  StarOutlined,
  CloudOutlined,
  TeamOutlined,
  SettingOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { mockProducts } from '@/data/mockData';
import { calculateKingsoftPrice } from '@/data/kingsoftPricingRules';
import { allKingsoftConfigurations, recommendConfiguration } from '@/data/kingsoftConfigurations';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface PriceCalculatorProps {
  productSku: string;
  basePrice: number;
}

const PriceCalculator: React.FC<PriceCalculatorProps> = ({ productSku, basePrice }) => {
  const [quantity, setQuantity] = useState(1);
  const [customerTier, setCustomerTier] = useState('standard');
  const [isRenewal, setIsRenewal] = useState(false);
  const [industry, setIndustry] = useState<string>();

  const priceResult = calculateKingsoftPrice(
    basePrice,
    productSku,
    quantity,
    customerTier,
    isRenewal,
    undefined,
    industry
  );

  return (
    <Card title="价格计算器" size="small">
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="购买数量">
              <InputNumber
                min={1}
                value={quantity}
                onChange={(value) => setQuantity(value || 1)}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="客户类型">
              <Select value={customerTier} onChange={setCustomerTier}>
                <Option value="standard">标准客户</Option>
                <Option value="gold">金牌客户</Option>
                <Option value="platinum">白金客户</Option>
                <Option value="enterprise">企业客户</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="是否续费">
              <Select value={isRenewal} onChange={setIsRenewal}>
                <Option value={false}>新购</Option>
                <Option value={true}>续费</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="行业类型">
              <Select value={industry} onChange={setIndustry} allowClear>
                <Option value="manufacturing">制造业</Option>
                <Option value="retail">零售业</Option>
                <Option value="service">服务业</Option>
                <Option value="technology">科技业</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Divider />

      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="原价"
            value={priceResult.originalPrice}
            precision={2}
            prefix="¥"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="优惠金额"
            value={priceResult.totalDiscount}
            precision={2}
            prefix="¥"
            valueStyle={{ color: '#cf1322' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="最终价格"
            value={priceResult.finalPrice}
            precision={2}
            prefix="¥"
            valueStyle={{ color: '#3f8600' }}
          />
        </Col>
      </Row>

      {priceResult.appliedRules.length > 0 && (
        <>
          <Divider />
          <Text strong>已应用的优惠规则：</Text>
          <List
            size="small"
            dataSource={priceResult.appliedRules}
            renderItem={(rule) => (
              <List.Item>
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <Text>{rule.name}</Text>
                  <Tag color="green">
                    {rule.discount.type === 'percentage' ? `${rule.discount.value}%折扣` : `¥${rule.discount.value}优惠`}
                  </Tag>
                </Space>
              </List.Item>
            )}
          />
        </>
      )}
    </Card>
  );
};

const KingsoftProductShowcase: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<string>('1');
  const [configModalVisible, setConfigModalVisible] = useState(false);

  const kingsoftProducts = mockProducts.filter(p => 
    p.sku?.includes('KIS') || p.sku?.includes('JDY') || p.sku?.includes('K3')
  );

  const currentProduct = kingsoftProducts.find(p => p.id === selectedProduct);
  const productConfigurations = allKingsoftConfigurations.filter(
    config => config.productId === selectedProduct
  );

  const handleConfigureProduct = () => {
    setConfigModalVisible(true);
  };

  const handleRecommendation = () => {
    const recommendations = recommendConfiguration(
      '小微企业',
      'technology',
      10000,
      ['财务管理', '进销存']
    );
    
    Modal.info({
      title: '推荐配置',
      width: 600,
      content: (
        <div>
          <Paragraph>根据您的需求，我们为您推荐以下配置：</Paragraph>
          {recommendations.map(config => (
            <Card key={config.id} size="small" style={{ marginBottom: 8 }}>
              <Title level={5}>{config.configurationName}</Title>
              <Text>{config.description}</Text>
              <br />
              <Text strong>价格: ¥{config.basePrice}</Text>
            </Card>
          ))}
        </div>
      ),
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Card title="金蝶产品系列" size="small">
            <List
              dataSource={kingsoftProducts}
              renderItem={(product) => (
                <List.Item
                  style={{
                    cursor: 'pointer',
                    backgroundColor: selectedProduct === product.id ? '#f0f0f0' : 'transparent',
                    padding: '8px',
                    borderRadius: '4px',
                  }}
                  onClick={() => setSelectedProduct(product.id)}
                >
                  <List.Item.Meta
                    title={product.name}
                    description={
                      <Space>
                        <Text type="secondary">¥{product.basePrice}</Text>
                        {product.tags?.map(tag => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={18}>
          {currentProduct && (
            <Card
              title={
                <Space>
                  <CloudOutlined />
                  {currentProduct.name}
                  <Tag color="blue">{currentProduct.sku}</Tag>
                </Space>
              }
              extra={
                <Space>
                  <Button type="primary" icon={<SettingOutlined />} onClick={handleConfigureProduct}>
                    产品配置
                  </Button>
                  <Button icon={<StarOutlined />} onClick={handleRecommendation}>
                    智能推荐
                  </Button>
                </Space>
              }
            >
              <Row gutter={24}>
                <Col span={16}>
                  <Tabs
                    defaultActiveKey="overview"
                    items={[
                      {
                        key: 'overview',
                        label: '产品概述',
                        children: (
                          <>
                            <Paragraph>{currentProduct.description}</Paragraph>

                            <Title level={5}>产品特色</Title>
                            <Row gutter={16}>
                              {currentProduct.specifications?.map((spec, index) => (
                                <Col span={12} key={index} style={{ marginBottom: 16 }}>
                                  <Card size="small">
                                    <Space>
                                      <TeamOutlined />
                                      <div>
                                        <Text strong>{spec.name}</Text>
                                        <br />
                                        <Text type="secondary">{spec.value}</Text>
                                      </div>
                                    </Space>
                                  </Card>
                                </Col>
                              ))}
                            </Row>

                            <Title level={5}>产品标签</Title>
                            <Space wrap>
                              {currentProduct.tags?.map(tag => (
                                <Tag key={tag} color="blue">{tag}</Tag>
                              ))}
                            </Space>
                          </>
                        ),
                      },
                      {
                        key: 'configurations',
                        label: '配置方案',
                        children: (
                          <Row gutter={16}>
                            {productConfigurations.map(config => (
                              <Col span={12} key={config.id} style={{ marginBottom: 16 }}>
                                <Card
                                  size="small"
                                  title={config.configurationName}
                                  extra={<Text strong>¥{config.basePrice}</Text>}
                                >
                                  <Paragraph ellipsis={{ rows: 2 }}>
                                    {config.description}
                                  </Paragraph>

                                  <Space wrap>
                                    {config.recommendedFor.map(rec => (
                                      <Tag key={rec}>{rec}</Tag>
                                    ))}
                                  </Space>

                                  <Divider />

                                  <List
                                    size="small"
                                    dataSource={config.features.slice(0, 4)}
                                    renderItem={(feature) => (
                                      <List.Item style={{ padding: '2px 0' }}>
                                        <Space>
                                          <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                          <Text style={{ fontSize: '12px' }}>{feature}</Text>
                                        </Space>
                                      </List.Item>
                                    )}
                                  />

                                  {config.features.length > 4 && (
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                      还有 {config.features.length - 4} 项功能...
                                    </Text>
                                  )}
                                </Card>
                              </Col>
                            ))}
                          </Row>
                        ),
                      },
                      {
                        key: 'support',
                        label: '技术支持',
                        children: (
                          <>
                            <Alert
                              message="专业技术支持"
                              description="金蝶提供全方位的技术支持服务，确保您的业务顺利运行"
                              type="info"
                              showIcon
                              style={{ marginBottom: 16 }}
                            />

                            <Row gutter={16}>
                              <Col span={8}>
                                <Card size="small" title="在线支持">
                                  <Space direction="vertical">
                                    <Text>• 工作日 9:00-18:00</Text>
                                    <Text>• 在线客服响应</Text>
                                    <Text>• 远程协助</Text>
                                  </Space>
                                </Card>
                              </Col>
                              <Col span={8}>
                                <Card size="small" title="电话支持">
                                  <Space direction="vertical">
                                    <Text>• 400客服热线</Text>
                                    <Text>• 技术专家支持</Text>
                                    <Text>• 紧急问题处理</Text>
                                  </Space>
                                </Card>
                              </Col>
                              <Col span={8}>
                                <Card size="small" title="培训服务">
                                  <Space direction="vertical">
                                    <Text>• 产品使用培训</Text>
                                    <Text>• 在线视频教程</Text>
                                    <Text>• 现场实施指导</Text>
                                  </Space>
                                </Card>
                              </Col>
                            </Row>
                          </>
                        ),
                      },
                    ]}
                  />
                </Col>

                <Col span={8}>
                  <PriceCalculator
                    productSku={currentProduct.sku || ''}
                    basePrice={currentProduct.basePrice}
                  />
                </Col>
              </Row>
            </Card>
          )}
        </Col>
      </Row>

      <Modal
        title="产品配置向导"
        visible={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        width={800}
        footer={null}
      >
        <Alert
          message="配置向导"
          description="请根据您的业务需求选择合适的产品配置"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        {/* 这里可以添加配置向导的具体内容 */}
        <Text>配置向导功能正在开发中...</Text>
      </Modal>
    </div>
  );
};

export default KingsoftProductShowcase;
