package com.linkcpq.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Set;

/**
 * 用户更新请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "用户更新请求")
public class UserUpdateRequest {

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    @Schema(description = "部门", example = "销售部")
    private String department;

    @Size(max = 100, message = "职位名称长度不能超过100个字符")
    @Schema(description = "职位", example = "销售经理")
    private String position;

    @Schema(description = "角色ID集合", example = "[1, 2]")
    private Set<Long> roleIds;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;
}
