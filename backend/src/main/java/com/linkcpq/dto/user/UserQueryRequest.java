package com.linkcpq.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户查询请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "用户查询请求")
public class UserQueryRequest {

    @Schema(description = "用户名（模糊查询）", example = "admin")
    private String username;

    @Schema(description = "邮箱（模糊查询）", example = "<EMAIL>")
    private String email;

    @Schema(description = "真实姓名（模糊查询）", example = "张三")
    private String realName;

    @Schema(description = "手机号（模糊查询）", example = "138")
    private String phone;

    @Schema(description = "部门（模糊查询）", example = "销售")
    private String department;

    @Schema(description = "职位（模糊查询）", example = "经理")
    private String position;

    @Schema(description = "角色ID", example = "1")
    private Long roleId;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createdAtStart;

    @Schema(description = "创建时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime createdAtEnd;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    private Integer pageSize = 20;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortBy = "createdAt";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortDirection = "desc";
}
