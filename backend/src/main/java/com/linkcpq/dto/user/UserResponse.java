package com.linkcpq.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 用户响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "用户响应")
public class UserResponse {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "部门", example = "销售部")
    private String department;

    @Schema(description = "职位", example = "销售经理")
    private String position;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Schema(description = "邮箱是否已验证", example = "true")
    private Boolean emailVerified;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @Schema(description = "最后登录IP", example = "*************")
    private String lastLoginIp;

    @Schema(description = "密码修改时间")
    private LocalDateTime passwordChangedAt;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "角色列表")
    private Set<RoleInfo> roles;

    @Data
    @Schema(description = "角色信息")
    public static class RoleInfo {
        @Schema(description = "角色ID", example = "1")
        private Long id;

        @Schema(description = "角色名称", example = "管理员")
        private String name;

        @Schema(description = "角色代码", example = "ADMIN")
        private String code;

        @Schema(description = "角色描述", example = "系统管理员")
        private String description;
    }
}
