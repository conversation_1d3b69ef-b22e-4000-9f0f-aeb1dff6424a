package com.linkcpq.dto.quote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报价创建请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "报价创建请求")
public class QuoteCreateRequest {

    @NotBlank(message = "报价名称不能为空")
    @Size(max = 200, message = "报价名称长度不能超过200个字符")
    @Schema(description = "报价名称", example = "金蝶云星辰标准版报价")
    private String quoteName;

    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Size(max = 100, message = "客户联系人长度不能超过100个字符")
    @Schema(description = "客户联系人", example = "张经理")
    private String customerContact;

    @Size(max = 50, message = "联系电话长度不能超过50个字符")
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "有效期至")
    private LocalDateTime validUntil;

    @Schema(description = "币种", example = "CNY")
    private String currency = "CNY";

    @Schema(description = "税率", example = "0.13")
    private BigDecimal taxRate = BigDecimal.valueOf(0.13);

    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    @Schema(description = "备注")
    private String remark;

    @NotNull(message = "报价项目不能为空")
    @Schema(description = "报价项目列表")
    private List<QuoteItemRequest> items;

    @Data
    @Schema(description = "报价项目请求")
    public static class QuoteItemRequest {

        @NotNull(message = "产品ID不能为空")
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @NotBlank(message = "产品名称不能为空")
        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @NotBlank(message = "产品规格不能为空")
        @Schema(description = "产品规格", example = "3用户/1年")
        private String specification;

        @NotNull(message = "数量不能为空")
        @Positive(message = "数量必须大于0")
        @Schema(description = "数量", example = "1")
        private Integer quantity;

        @NotNull(message = "单价不能为空")
        @Positive(message = "单价必须大于0")
        @Schema(description = "单价", example = "3980.00")
        private BigDecimal unitPrice;

        @Schema(description = "折扣率", example = "0.1")
        private BigDecimal discountRate = BigDecimal.ZERO;

        @Schema(description = "配置参数")
        private String configurationParams;

        @Size(max = 500, message = "备注长度不能超过500个字符")
        @Schema(description = "备注")
        private String remark;
    }
}
