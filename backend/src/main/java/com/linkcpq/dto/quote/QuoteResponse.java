package com.linkcpq.dto.quote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报价响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "报价响应")
public class QuoteResponse {

    @Schema(description = "报价ID", example = "1")
    private Long id;

    @Schema(description = "报价编号", example = "Q202401001")
    private String quoteNumber;

    @Schema(description = "报价名称", example = "金蝶云星辰标准版报价")
    private String quoteName;

    @Schema(description = "客户信息")
    private CustomerInfo customer;

    @Schema(description = "客户联系人", example = "张经理")
    private String customerContact;

    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "报价状态", example = "DRAFT")
    private String status;

    @Schema(description = "状态描述", example = "草稿")
    private String statusDesc;

    @Schema(description = "有效期至")
    private LocalDateTime validUntil;

    @Schema(description = "币种", example = "CNY")
    private String currency;

    @Schema(description = "税率", example = "0.13")
    private BigDecimal taxRate;

    @Schema(description = "小计金额", example = "3980.00")
    private BigDecimal subtotalAmount;

    @Schema(description = "折扣金额", example = "398.00")
    private BigDecimal discountAmount;

    @Schema(description = "税额", example = "465.66")
    private BigDecimal taxAmount;

    @Schema(description = "总金额", example = "4047.66")
    private BigDecimal totalAmount;

    @Schema(description = "创建人信息")
    private CreatorInfo createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "报价项目列表")
    private List<QuoteItemResponse> items;

    @Data
    @Schema(description = "客户信息")
    public static class CustomerInfo {
        @Schema(description = "客户ID", example = "1")
        private Long id;

        @Schema(description = "客户名称", example = "某某科技有限公司")
        private String name;

        @Schema(description = "客户代码", example = "C001")
        private String code;

        @Schema(description = "客户类型", example = "企业客户")
        private String type;
    }

    @Data
    @Schema(description = "创建人信息")
    public static class CreatorInfo {
        @Schema(description = "用户ID", example = "1")
        private Long id;

        @Schema(description = "用户名", example = "sales01")
        private String username;

        @Schema(description = "真实姓名", example = "李销售")
        private String realName;

        @Schema(description = "部门", example = "销售部")
        private String department;
    }

    @Data
    @Schema(description = "报价项目响应")
    public static class QuoteItemResponse {
        @Schema(description = "项目ID", example = "1")
        private Long id;

        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "产品规格", example = "3用户/1年")
        private String specification;

        @Schema(description = "数量", example = "1")
        private Integer quantity;

        @Schema(description = "单价", example = "3980.00")
        private BigDecimal unitPrice;

        @Schema(description = "折扣率", example = "0.1")
        private BigDecimal discountRate;

        @Schema(description = "折扣金额", example = "398.00")
        private BigDecimal discountAmount;

        @Schema(description = "小计金额", example = "3582.00")
        private BigDecimal subtotalAmount;

        @Schema(description = "配置参数")
        private String configurationParams;

        @Schema(description = "备注")
        private String remark;
    }
}
