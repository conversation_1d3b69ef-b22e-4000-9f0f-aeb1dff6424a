package com.linkcpq.dto.quote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报价更新请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "报价更新请求")
public class QuoteUpdateRequest {

    @Size(max = 200, message = "报价名称长度不能超过200个字符")
    @Schema(description = "报价名称", example = "金蝶云星辰标准版报价")
    private String quoteName;

    @Size(max = 100, message = "客户联系人长度不能超过100个字符")
    @Schema(description = "客户联系人", example = "张经理")
    private String customerContact;

    @Size(max = 50, message = "联系电话长度不能超过50个字符")
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "有效期至")
    private LocalDateTime validUntil;

    @Schema(description = "币种", example = "CNY")
    private String currency;

    @Schema(description = "税率", example = "0.13")
    private BigDecimal taxRate;

    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "报价项目列表")
    private List<QuoteCreateRequest.QuoteItemRequest> items;
}
