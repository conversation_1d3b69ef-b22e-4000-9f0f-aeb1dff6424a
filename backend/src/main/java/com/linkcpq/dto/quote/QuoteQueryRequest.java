package com.linkcpq.dto.quote;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报价查询请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "报价查询请求")
public class QuoteQueryRequest {

    @Schema(description = "报价编号（模糊查询）", example = "Q2024")
    private String quoteNumber;

    @Schema(description = "报价名称（模糊查询）", example = "金蝶云星辰")
    private String quoteName;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "客户名称（模糊查询）", example = "某某公司")
    private String customerName;

    @Schema(description = "客户联系人（模糊查询）", example = "张经理")
    private String customerContact;

    @Schema(description = "报价状态", example = "DRAFT", allowableValues = {"DRAFT", "PENDING", "APPROVED", "REJECTED", "EXPIRED", "CONVERTED"})
    private String status;

    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    @Schema(description = "创建人姓名（模糊查询）", example = "李销售")
    private String createdByName;

    @Schema(description = "金额范围-最小值", example = "1000.00")
    private BigDecimal totalAmountMin;

    @Schema(description = "金额范围-最大值", example = "50000.00")
    private BigDecimal totalAmountMax;

    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createdAtStart;

    @Schema(description = "创建时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime createdAtEnd;

    @Schema(description = "有效期开始", example = "2024-01-01T00:00:00")
    private LocalDateTime validUntilStart;

    @Schema(description = "有效期结束", example = "2024-12-31T23:59:59")
    private LocalDateTime validUntilEnd;

    @Schema(description = "币种", example = "CNY")
    private String currency;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    private Integer pageSize = 20;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortBy = "createdAt";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortDirection = "desc";
}
