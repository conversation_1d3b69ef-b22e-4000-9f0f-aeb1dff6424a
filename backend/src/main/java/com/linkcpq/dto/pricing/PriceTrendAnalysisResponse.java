package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格趋势分析响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格趋势分析响应")
public class PriceTrendAnalysisResponse {

    @Schema(description = "分析时间")
    private LocalDateTime analysisTime;

    @Schema(description = "产品趋势分析")
    private List<ProductTrendAnalysis> productTrends;

    @Schema(description = "整体市场趋势")
    private MarketTrendSummary marketTrend;

    @Schema(description = "预测数据")
    private ForecastData forecast;

    @Data
    @Schema(description = "产品趋势分析")
    public static class ProductTrendAnalysis {
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "趋势方向", example = "INCREASING", allowableValues = {"INCREASING", "DECREASING", "STABLE", "VOLATILE"})
        private String trendDirection;

        @Schema(description = "趋势强度", example = "MODERATE", allowableValues = {"WEAK", "MODERATE", "STRONG"})
        private String trendStrength;

        @Schema(description = "平均增长率", example = "0.05")
        private BigDecimal averageGrowthRate;

        @Schema(description = "价格波动率", example = "0.12")
        private BigDecimal volatility;

        @Schema(description = "历史数据点")
        private List<TrendDataPoint> historicalData;

        @Schema(description = "关键事件")
        private List<TrendEvent> keyEvents;
    }

    @Data
    @Schema(description = "趋势数据点")
    public static class TrendDataPoint {
        @Schema(description = "时间点")
        private LocalDateTime timestamp;

        @Schema(description = "价格", example = "3980.00")
        private BigDecimal price;

        @Schema(description = "销量", example = "100")
        private Integer volume;

        @Schema(description = "收入", example = "398000.00")
        private BigDecimal revenue;

        @Schema(description = "环比增长率", example = "0.02")
        private BigDecimal growthRate;
    }

    @Data
    @Schema(description = "趋势事件")
    public static class TrendEvent {
        @Schema(description = "事件时间")
        private LocalDateTime eventTime;

        @Schema(description = "事件类型", example = "PRICE_CHANGE")
        private String eventType;

        @Schema(description = "事件描述", example = "产品价格上调5%")
        private String description;

        @Schema(description = "价格影响", example = "0.05")
        private BigDecimal priceImpact;
    }

    @Data
    @Schema(description = "市场趋势摘要")
    public static class MarketTrendSummary {
        @Schema(description = "整体趋势", example = "GROWING")
        private String overallTrend;

        @Schema(description = "市场平均价格", example = "3500.00")
        private BigDecimal marketAveragePrice;

        @Schema(description = "价格区间", example = "2800.00 - 4500.00")
        private String priceRange;

        @Schema(description = "主要驱动因素")
        private List<String> keyDrivers;
    }

    @Data
    @Schema(description = "预测数据")
    public static class ForecastData {
        @Schema(description = "预测期间（月）", example = "6")
        private Integer forecastPeriod;

        @Schema(description = "预测数据点")
        private List<TrendDataPoint> forecastPoints;

        @Schema(description = "预测置信度", example = "0.75")
        private BigDecimal confidence;

        @Schema(description = "预测方法", example = "时间序列分析")
        private String forecastMethod;
    }
}
