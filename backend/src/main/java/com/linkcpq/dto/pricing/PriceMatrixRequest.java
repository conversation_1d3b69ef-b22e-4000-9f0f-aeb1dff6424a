package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 价格矩阵请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格矩阵请求")
public class PriceMatrixRequest {

    @NotNull(message = "产品ID列表不能为空")
    @Schema(description = "产品ID列表")
    private List<Long> productIds;

    @Schema(description = "客户类型列表")
    private List<String> customerTypes;

    @Schema(description = "数量区间列表")
    private List<QuantityRange> quantityRanges;

    @Schema(description = "地区列表")
    private List<String> regions;

    @Schema(description = "合同期限列表（月）")
    private List<Integer> contractPeriods;

    @Schema(description = "包含折扣信息", example = "true")
    private Boolean includeDiscounts = true;

    @Schema(description = "包含阶梯价格", example = "true")
    private Boolean includeTierPricing = true;

    @Data
    @Schema(description = "数量区间")
    public static class QuantityRange {
        @Schema(description = "最小数量", example = "1")
        private Integer minQuantity;

        @Schema(description = "最大数量", example = "10")
        private Integer maxQuantity;

        @Schema(description = "区间名称", example = "小批量")
        private String rangeName;
    }
}
