package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 价格优化请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格优化请求")
public class PriceOptimizationRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "优化目标", example = "PROFIT", allowableValues = {"PROFIT", "REVENUE", "MARKET_SHARE", "COMPETITIVE"})
    private String optimizationGoal = "PROFIT";

    @Schema(description = "当前价格", example = "3980.00")
    private BigDecimal currentPrice;

    @Schema(description = "成本价格", example = "2000.00")
    private BigDecimal costPrice;

    @Schema(description = "目标利润率", example = "0.40")
    private BigDecimal targetMargin;

    @Schema(description = "价格弹性系数", example = "-1.2")
    private BigDecimal priceElasticity;

    @Schema(description = "市场需求数据")
    private List<DemandPoint> demandData;

    @Schema(description = "约束条件")
    private PriceConstraints constraints;

    @Data
    @Schema(description = "需求点")
    public static class DemandPoint {
        @Schema(description = "价格", example = "3500.00")
        private BigDecimal price;

        @Schema(description = "需求量", example = "1000")
        private Integer demand;

        @Schema(description = "时间段", example = "2024-Q1")
        private String period;
    }

    @Data
    @Schema(description = "价格约束")
    public static class PriceConstraints {
        @Schema(description = "最低价格", example = "2500.00")
        private BigDecimal minPrice;

        @Schema(description = "最高价格", example = "5000.00")
        private BigDecimal maxPrice;

        @Schema(description = "最低利润率", example = "0.20")
        private BigDecimal minMargin;

        @Schema(description = "竞争对手价格上限", example = "4200.00")
        private BigDecimal competitorPriceCeiling;
    }
}
