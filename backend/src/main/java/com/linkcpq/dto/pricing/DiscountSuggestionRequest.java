package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 折扣建议请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "折扣建议请求")
public class DiscountSuggestionRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @Schema(description = "购买数量", example = "10")
    private Integer quantity;

    @NotNull(message = "原价不能为空")
    @Positive(message = "原价必须大于0")
    @Schema(description = "原价", example = "3980.00")
    private BigDecimal originalPrice;

    @Schema(description = "目标价格", example = "3500.00")
    private BigDecimal targetPrice;

    @Schema(description = "客户类型", example = "VIP", allowableValues = {"NORMAL", "VIP", "PARTNER", "ENTERPRISE"})
    private String customerType;

    @Schema(description = "销售渠道", example = "DIRECT", allowableValues = {"DIRECT", "PARTNER", "ONLINE", "RETAIL"})
    private String salesChannel;

    @Schema(description = "合同期限（月）", example = "12")
    private Integer contractPeriod;

    @Schema(description = "是否续费客户", example = "false")
    private Boolean isRenewal = false;

    @Schema(description = "历史购买金额", example = "50000.00")
    private BigDecimal historicalPurchaseAmount;

    @Schema(description = "竞争对手价格列表")
    private List<CompetitorPrice> competitorPrices;

    @Schema(description = "业务场景", example = "NEW_CUSTOMER", allowableValues = {"NEW_CUSTOMER", "RENEWAL", "UPGRADE", "CROSS_SELL"})
    private String businessScenario;

    @Schema(description = "紧急程度", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String urgency = "NORMAL";

    @Data
    @Schema(description = "竞争对手价格")
    public static class CompetitorPrice {
        @Schema(description = "竞争对手名称", example = "用友")
        private String competitorName;

        @Schema(description = "竞争对手产品", example = "用友T+")
        private String competitorProduct;

        @Schema(description = "竞争对手价格", example = "3200.00")
        private BigDecimal price;

        @Schema(description = "价格来源", example = "客户提供")
        private String source;
    }
}
