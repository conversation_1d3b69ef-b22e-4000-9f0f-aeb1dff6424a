package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格优化响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格优化响应")
public class PriceOptimizationResponse {

    @Schema(description = "优化后价格", example = "3650.00")
    private BigDecimal optimizedPrice;

    @Schema(description = "预期利润率", example = "0.45")
    private BigDecimal expectedMargin;

    @Schema(description = "预期收入", example = "3650000.00")
    private BigDecimal expectedRevenue;

    @Schema(description = "预期利润", example = "1642500.00")
    private BigDecimal expectedProfit;

    @Schema(description = "预期销量", example = "1000")
    private Integer expectedVolume;

    @Schema(description = "价格敏感性分析")
    private List<SensitivityPoint> sensitivityAnalysis;

    @Schema(description = "优化建议")
    private List<OptimizationRecommendation> recommendations;

    @Schema(description = "风险评估")
    private String riskAssessment;

    @Data
    @Schema(description = "敏感性分析点")
    public static class SensitivityPoint {
        @Schema(description = "价格变化", example = "0.05")
        private BigDecimal priceChange;

        @Schema(description = "需求变化", example = "-0.06")
        private BigDecimal demandChange;

        @Schema(description = "利润变化", example = "0.03")
        private BigDecimal profitChange;
    }

    @Data
    @Schema(description = "优化建议")
    public static class OptimizationRecommendation {
        @Schema(description = "建议类型", example = "PRICE_ADJUSTMENT")
        private String type;

        @Schema(description = "建议描述", example = "建议将价格调整至3650元以获得最佳利润")
        private String description;

        @Schema(description = "预期影响", example = "利润提升15%")
        private String expectedImpact;

        @Schema(description = "实施优先级", example = "HIGH")
        private String priority;
    }
}
