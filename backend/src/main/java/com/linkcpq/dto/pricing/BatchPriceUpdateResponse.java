package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量价格更新响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "批量价格更新响应")
public class BatchPriceUpdateResponse {

    @Schema(description = "更新任务ID", example = "1")
    private Long taskId;

    @Schema(description = "总更新数量", example = "50")
    private Integer totalCount;

    @Schema(description = "成功数量", example = "48")
    private Integer successCount;

    @Schema(description = "失败数量", example = "2")
    private Integer failureCount;

    @Schema(description = "任务状态", example = "COMPLETED", allowableValues = {"PENDING", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED"})
    private String status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    private LocalDateTime completionTime;

    @Schema(description = "更新结果详情")
    private List<UpdateResult> results;

    @Schema(description = "错误汇总")
    private List<String> errorSummary;

    @Data
    @Schema(description = "更新结果")
    public static class UpdateResult {
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "更新状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILED", "SKIPPED"})
        private String status;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "更新前价格", example = "3980.00")
        private String oldPrice;

        @Schema(description = "更新后价格", example = "4180.00")
        private String newPrice;
    }
}
