package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 定价分析响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "定价分析响应")
public class PricingAnalysisResponse {

    @Schema(description = "分析报告ID", example = "1")
    private Long reportId;

    @Schema(description = "分析类型", example = "PROFITABILITY")
    private String analysisType;

    @Schema(description = "生成时间")
    private LocalDateTime generatedAt;

    @Schema(description = "分析结果摘要")
    private AnalysisSummary summary;

    @Schema(description = "产品分析结果")
    private List<ProductAnalysis> productAnalyses;

    @Schema(description = "趋势分析")
    private TrendAnalysis trendAnalysis;

    @Schema(description = "竞争分析")
    private CompetitorAnalysis competitorAnalysis;

    @Schema(description = "建议措施")
    private List<Recommendation> recommendations;

    @Data
    @Schema(description = "分析摘要")
    public static class AnalysisSummary {
        @Schema(description = "总产品数", example = "25")
        private Integer totalProducts;

        @Schema(description = "平均利润率", example = "0.42")
        private BigDecimal averageMargin;

        @Schema(description = "最高利润率", example = "0.65")
        private BigDecimal maxMargin;

        @Schema(description = "最低利润率", example = "0.18")
        private BigDecimal minMargin;

        @Schema(description = "价格竞争力评分", example = "7.8")
        private BigDecimal competitivenessScore;

        @Schema(description = "市场定位", example = "PREMIUM")
        private String marketPosition;
    }

    @Data
    @Schema(description = "产品分析")
    public static class ProductAnalysis {
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "当前价格", example = "3980.00")
        private BigDecimal currentPrice;

        @Schema(description = "建议价格", example = "4100.00")
        private BigDecimal suggestedPrice;

        @Schema(description = "利润率", example = "0.45")
        private BigDecimal margin;

        @Schema(description = "销量", example = "1200")
        private Integer salesVolume;

        @Schema(description = "收入", example = "4776000.00")
        private BigDecimal revenue;

        @Schema(description = "竞争力评分", example = "8.2")
        private BigDecimal competitivenessScore;

        @Schema(description = "价格弹性", example = "-1.2")
        private BigDecimal priceElasticity;
    }

    @Data
    @Schema(description = "趋势分析")
    public static class TrendAnalysis {
        @Schema(description = "价格趋势", example = "INCREASING")
        private String priceTrend;

        @Schema(description = "需求趋势", example = "STABLE")
        private String demandTrend;

        @Schema(description = "利润趋势", example = "INCREASING")
        private String profitTrend;

        @Schema(description = "历史数据点")
        private List<DataPoint> historicalData;

        @Schema(description = "预测数据点")
        private List<DataPoint> forecastData;
    }

    @Data
    @Schema(description = "数据点")
    public static class DataPoint {
        @Schema(description = "时间")
        private LocalDateTime timestamp;

        @Schema(description = "价格", example = "3980.00")
        private BigDecimal price;

        @Schema(description = "销量", example = "100")
        private Integer volume;

        @Schema(description = "收入", example = "398000.00")
        private BigDecimal revenue;

        @Schema(description = "利润", example = "179100.00")
        private BigDecimal profit;
    }

    @Data
    @Schema(description = "竞争分析")
    public static class CompetitorAnalysis {
        @Schema(description = "主要竞争对手")
        private List<String> mainCompetitors;

        @Schema(description = "价格对比")
        private Map<String, BigDecimal> priceComparison;

        @Schema(description = "市场份额对比")
        private Map<String, BigDecimal> marketShareComparison;

        @Schema(description = "竞争优势")
        private List<String> competitiveAdvantages;

        @Schema(description = "竞争劣势")
        private List<String> competitiveDisadvantages;
    }

    @Data
    @Schema(description = "建议措施")
    public static class Recommendation {
        @Schema(description = "建议类型", example = "PRICE_ADJUSTMENT")
        private String type;

        @Schema(description = "建议描述", example = "建议将云星辰标准版价格上调至4100元")
        private String description;

        @Schema(description = "预期影响", example = "利润提升8%")
        private String expectedImpact;

        @Schema(description = "优先级", example = "HIGH")
        private String priority;

        @Schema(description = "实施难度", example = "MEDIUM")
        private String implementationDifficulty;
    }
}
