package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量价格更新请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "批量价格更新请求")
public class BatchPriceUpdateRequest {

    @NotEmpty(message = "价格更新列表不能为空")
    @Schema(description = "价格更新列表")
    private List<PriceUpdateItem> priceUpdates;

    @Schema(description = "更新原因", example = "年度调价")
    private String updateReason;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveDate;

    @Schema(description = "是否立即生效", example = "false")
    private Boolean immediateEffect = false;

    @Schema(description = "审批流程ID", example = "1")
    private Long approvalWorkflowId;

    @Data
    @Schema(description = "价格更新项")
    public static class PriceUpdateItem {

        @NotNull(message = "产品ID不能为空")
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "当前价格", example = "3980.00")
        private BigDecimal currentPrice;

        @NotNull(message = "新价格不能为空")
        @Schema(description = "新价格", example = "4180.00")
        private BigDecimal newPrice;

        @Schema(description = "价格变化类型", example = "INCREASE", allowableValues = {"INCREASE", "DECREASE", "FIXED"})
        private String changeType;

        @Schema(description = "变化幅度", example = "0.05")
        private BigDecimal changeRate;

        @Schema(description = "变化金额", example = "200.00")
        private BigDecimal changeAmount;

        @Schema(description = "客户类型限制", example = "ALL", allowableValues = {"ALL", "VIP", "ENTERPRISE", "STANDARD"})
        private String customerTypeRestriction = "ALL";

        @Schema(description = "地区限制", example = "ALL")
        private String regionRestriction = "ALL";

        @Schema(description = "备注")
        private String remark;
    }
}
