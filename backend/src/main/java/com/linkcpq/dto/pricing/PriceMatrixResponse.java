package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格矩阵响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格矩阵响应")
public class PriceMatrixResponse {

    @Schema(description = "生成时间")
    private LocalDateTime generatedAt;

    @Schema(description = "价格矩阵数据")
    private List<PriceMatrixRow> priceMatrix;

    @Schema(description = "产品信息映射")
    private Map<Long, ProductInfo> productInfoMap;

    @Schema(description = "客户类型信息")
    private List<CustomerTypeInfo> customerTypeInfos;

    @Data
    @Schema(description = "价格矩阵行")
    public static class PriceMatrixRow {
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String productName;

        @Schema(description = "价格详情")
        private List<PriceDetail> priceDetails;
    }

    @Data
    @Schema(description = "价格详情")
    public static class PriceDetail {
        @Schema(description = "客户类型", example = "VIP")
        private String customerType;

        @Schema(description = "数量区间", example = "1-10")
        private String quantityRange;

        @Schema(description = "地区", example = "华东")
        private String region;

        @Schema(description = "合同期限（月）", example = "12")
        private Integer contractPeriod;

        @Schema(description = "基础价格", example = "3980.00")
        private BigDecimal basePrice;

        @Schema(description = "折扣率", example = "0.10")
        private BigDecimal discountRate;

        @Schema(description = "最终价格", example = "3582.00")
        private BigDecimal finalPrice;

        @Schema(description = "阶梯价格信息")
        private List<TierPrice> tierPrices;
    }

    @Data
    @Schema(description = "阶梯价格")
    public static class TierPrice {
        @Schema(description = "最小数量", example = "1")
        private Integer minQuantity;

        @Schema(description = "最大数量", example = "10")
        private Integer maxQuantity;

        @Schema(description = "单价", example = "3980.00")
        private BigDecimal unitPrice;

        @Schema(description = "折扣率", example = "0.05")
        private BigDecimal discountRate;
    }

    @Data
    @Schema(description = "产品信息")
    public static class ProductInfo {
        @Schema(description = "产品代码", example = "KIS_CLOUD_STD")
        private String productCode;

        @Schema(description = "产品类型", example = "财务软件")
        private String productType;

        @Schema(description = "产品分类", example = "云产品")
        private String category;

        @Schema(description = "标准价格", example = "3980.00")
        private BigDecimal standardPrice;
    }

    @Data
    @Schema(description = "客户类型信息")
    public static class CustomerTypeInfo {
        @Schema(description = "客户类型", example = "VIP")
        private String customerType;

        @Schema(description = "类型描述", example = "VIP客户")
        private String description;

        @Schema(description = "默认折扣率", example = "0.15")
        private BigDecimal defaultDiscountRate;

        @Schema(description = "最大折扣率", example = "0.25")
        private BigDecimal maxDiscountRate;
    }
}
