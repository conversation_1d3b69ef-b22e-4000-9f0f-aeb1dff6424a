package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 定价规则导出请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "定价规则导出请求")
public class PricingRuleExportRequest {

    @Schema(description = "规则ID列表")
    private List<Long> ruleIds;

    @Schema(description = "导出格式", example = "EXCEL", allowableValues = {"EXCEL", "CSV", "JSON", "XML"})
    private String exportFormat = "EXCEL";

    @Schema(description = "是否包含历史数据", example = "false")
    private Boolean includeHistory = false;

    @Schema(description = "是否包含应用记录", example = "true")
    private Boolean includeApplications = true;

    @Schema(description = "文件名", example = "pricing_rules_export")
    private String fileName;

    @Schema(description = "导出范围", example = "SELECTED", allowableValues = {"ALL", "SELECTED", "ACTIVE", "INACTIVE"})
    private String exportScope = "SELECTED";

    @Schema(description = "产品类型过滤")
    private List<String> productTypes;

    @Schema(description = "客户类型过滤")
    private List<String> customerTypes;

    @Schema(description = "规则类型过滤")
    private List<String> ruleTypes;
}
