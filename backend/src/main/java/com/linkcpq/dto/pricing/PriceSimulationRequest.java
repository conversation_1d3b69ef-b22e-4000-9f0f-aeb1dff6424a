package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 价格模拟请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格模拟请求")
public class PriceSimulationRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "模拟场景列表")
    private List<SimulationScenario> scenarios;

    @Schema(description = "模拟参数")
    private SimulationParameters parameters;

    @Data
    @Schema(description = "模拟场景")
    public static class SimulationScenario {
        @Schema(description = "场景名称", example = "价格上调5%")
        private String scenarioName;

        @Schema(description = "价格调整", example = "0.05")
        private BigDecimal priceAdjustment;

        @Schema(description = "调整类型", example = "PERCENTAGE", allowableValues = {"PERCENTAGE", "ABSOLUTE"})
        private String adjustmentType = "PERCENTAGE";

        @Schema(description = "目标客户群", example = "ALL", allowableValues = {"ALL", "VIP", "ENTERPRISE", "STANDARD"})
        private String targetCustomerGroup = "ALL";

        @Schema(description = "生效时间范围（天）", example = "30")
        private Integer effectiveDays = 30;
    }

    @Data
    @Schema(description = "模拟参数")
    public static class SimulationParameters {
        @Schema(description = "价格弹性系数", example = "-1.2")
        private BigDecimal priceElasticity;

        @Schema(description = "市场饱和度", example = "0.7")
        private BigDecimal marketSaturation;

        @Schema(description = "竞争反应系数", example = "0.3")
        private BigDecimal competitorReactionFactor;

        @Schema(description = "季节性因子", example = "1.1")
        private BigDecimal seasonalityFactor;

        @Schema(description = "考虑竞争对手反应", example = "true")
        private Boolean includeCompetitorReaction = true;

        @Schema(description = "考虑市场趋势", example = "true")
        private Boolean includeMarketTrend = true;
    }
}
