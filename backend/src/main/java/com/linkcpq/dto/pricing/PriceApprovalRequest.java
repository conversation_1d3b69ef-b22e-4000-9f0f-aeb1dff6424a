package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格审批请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格审批请求")
public class PriceApprovalRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "报价ID", example = "1")
    private Long quoteId;

    @NotNull(message = "当前价格不能为空")
    @Schema(description = "当前价格", example = "3980.00")
    private BigDecimal currentPrice;

    @NotNull(message = "申请价格不能为空")
    @Schema(description = "申请价格", example = "3500.00")
    private BigDecimal requestedPrice;

    @Schema(description = "折扣率", example = "0.12")
    private BigDecimal discountRate;

    @Schema(description = "折扣金额", example = "480.00")
    private BigDecimal discountAmount;

    @NotBlank(message = "申请原因不能为空")
    @Size(max = 1000, message = "申请原因长度不能超过1000个字符")
    @Schema(description = "申请原因", example = "大客户批量采购，给予特殊折扣")
    private String requestReason;

    @Schema(description = "业务场景", example = "LARGE_ORDER", allowableValues = {"LARGE_ORDER", "COMPETITIVE_MATCH", "STRATEGIC_CUSTOMER", "INVENTORY_CLEARANCE", "PROMOTION"})
    private String businessScenario;

    @Schema(description = "竞争对手价格", example = "3400.00")
    private BigDecimal competitorPrice;

    @Schema(description = "竞争对手名称", example = "用友")
    private String competitorName;

    @Schema(description = "预期订单金额", example = "350000.00")
    private BigDecimal expectedOrderAmount;

    @Schema(description = "预期数量", example = "100")
    private Integer expectedQuantity;

    @Schema(description = "合同期限（月）", example = "24")
    private Integer contractPeriod;

    @Schema(description = "客户重要性", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "STRATEGIC"})
    private String customerImportance;

    @Schema(description = "紧急程度", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String urgency = "NORMAL";

    @Schema(description = "期望审批时间")
    private LocalDateTime expectedApprovalTime;

    @Schema(description = "销售人员备注")
    private String salesNotes;

    @Schema(description = "附件文件列表")
    private List<String> attachments;
}
