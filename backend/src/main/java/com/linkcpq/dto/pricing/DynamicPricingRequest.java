package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 动态定价请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "动态定价请求")
public class DynamicPricingRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "购买数量", example = "5")
    private Integer quantity;

    @Schema(description = "市场条件")
    private MarketConditions marketConditions;

    @Schema(description = "客户特征")
    private CustomerProfile customerProfile;

    @Schema(description = "实时因子")
    private Map<String, Object> realtimeFactors;

    @Data
    @Schema(description = "市场条件")
    public static class MarketConditions {
        @Schema(description = "需求强度", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String demandIntensity;

        @Schema(description = "库存水平", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH"})
        private String inventoryLevel;

        @Schema(description = "竞争激烈程度", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String competitionIntensity;

        @Schema(description = "季节性因子", example = "1.1")
        private BigDecimal seasonalityFactor;

        @Schema(description = "市场趋势", example = "GROWING", allowableValues = {"DECLINING", "STABLE", "GROWING"})
        private String marketTrend;
    }

    @Data
    @Schema(description = "客户特征")
    public static class CustomerProfile {
        @Schema(description = "客户价值等级", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String valueLevel;

        @Schema(description = "价格敏感度", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String priceSensitivity;

        @Schema(description = "购买频率", example = "REGULAR", allowableValues = {"RARE", "OCCASIONAL", "REGULAR", "FREQUENT"})
        private String purchaseFrequency;

        @Schema(description = "历史折扣率", example = "0.12")
        private BigDecimal historicalDiscountRate;

        @Schema(description = "信用等级", example = "A", allowableValues = {"A", "B", "C", "D"})
        private String creditRating;

        @Schema(description = "地理位置", example = "华东")
        private String location;
    }
}
