package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格模拟响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格模拟响应")
public class PriceSimulationResponse {

    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "产品名称", example = "金蝶云星辰标准版")
    private String productName;

    @Schema(description = "当前价格", example = "3980.00")
    private BigDecimal currentPrice;

    @Schema(description = "模拟结果列表")
    private List<SimulationResult> simulationResults;

    @Schema(description = "最佳场景")
    private SimulationResult bestScenario;

    @Schema(description = "风险评估")
    private RiskAssessment riskAssessment;

    @Data
    @Schema(description = "模拟结果")
    public static class SimulationResult {
        @Schema(description = "场景名称", example = "价格上调5%")
        private String scenarioName;

        @Schema(description = "模拟价格", example = "4179.00")
        private BigDecimal simulatedPrice;

        @Schema(description = "预期销量", example = "950")
        private Integer expectedVolume;

        @Schema(description = "预期收入", example = "3970050.00")
        private BigDecimal expectedRevenue;

        @Schema(description = "预期利润", example = "1786522.50")
        private BigDecimal expectedProfit;

        @Schema(description = "利润率", example = "0.45")
        private BigDecimal profitMargin;

        @Schema(description = "市场份额变化", example = "-0.02")
        private BigDecimal marketShareChange;

        @Schema(description = "竞争对手反应概率", example = "0.3")
        private BigDecimal competitorReactionProbability;

        @Schema(description = "成功概率", example = "0.75")
        private BigDecimal successProbability;

        @Schema(description = "投资回报率", example = "0.18")
        private BigDecimal roi;
    }

    @Data
    @Schema(description = "风险评估")
    public static class RiskAssessment {
        @Schema(description = "整体风险等级", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String overallRiskLevel;

        @Schema(description = "价格风险", example = "MEDIUM")
        private String priceRisk;

        @Schema(description = "市场风险", example = "LOW")
        private String marketRisk;

        @Schema(description = "竞争风险", example = "HIGH")
        private String competitionRisk;

        @Schema(description = "风险因素")
        private List<String> riskFactors;

        @Schema(description = "缓解措施")
        private List<String> mitigationStrategies;
    }
}
