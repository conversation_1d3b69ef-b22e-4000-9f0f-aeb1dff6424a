package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定价规则导入响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "定价规则导入响应")
public class PricingRuleImportResponse {

    @Schema(description = "导入任务ID", example = "1")
    private Long taskId;

    @Schema(description = "导入状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILED", "PARTIAL_SUCCESS"})
    private String importStatus;

    @Schema(description = "总记录数", example = "100")
    private Integer totalRecords;

    @Schema(description = "成功导入数", example = "95")
    private Integer successCount;

    @Schema(description = "失败数", example = "5")
    private Integer failureCount;

    @Schema(description = "跳过数", example = "0")
    private Integer skippedCount;

    @Schema(description = "导入开始时间")
    private LocalDateTime startTime;

    @Schema(description = "导入完成时间")
    private LocalDateTime completionTime;

    @Schema(description = "导入结果详情")
    private List<ImportResult> results;

    @Schema(description = "错误汇总")
    private List<String> errorSummary;

    @Schema(description = "警告信息")
    private List<String> warnings;

    @Data
    @Schema(description = "导入结果")
    public static class ImportResult {
        @Schema(description = "行号", example = "1")
        private Integer rowNumber;

        @Schema(description = "规则名称", example = "VIP客户折扣规则")
        private String ruleName;

        @Schema(description = "导入状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILED", "SKIPPED"})
        private String status;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "创建的规则ID", example = "123")
        private Long createdRuleId;
    }
}
