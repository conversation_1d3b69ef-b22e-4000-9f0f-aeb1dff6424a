package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格趋势分析请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格趋势分析请求")
public class PriceTrendAnalysisRequest {

    @Schema(description = "产品ID列表")
    private List<Long> productIds;

    @Schema(description = "分析时间范围开始")
    private LocalDateTime startDate;

    @Schema(description = "分析时间范围结束")
    private LocalDateTime endDate;

    @Schema(description = "时间粒度", example = "MONTHLY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"})
    private String timeGranularity = "MONTHLY";

    @Schema(description = "趋势类型", example = "PRICE", allowableValues = {"PRICE", "VOLUME", "REVENUE", "MARGIN"})
    private String trendType = "PRICE";

    @Schema(description = "包含预测", example = "true")
    private Boolean includeForecast = false;

    @Schema(description = "预测期间（月）", example = "6")
    private Integer forecastPeriod = 6;

    @Schema(description = "客户类型过滤")
    private List<String> customerTypes;

    @Schema(description = "地区过滤")
    private List<String> regions;

    @Schema(description = "销售渠道过滤")
    private List<String> salesChannels;

    @Schema(description = "包含季节性分析", example = "true")
    private Boolean includeSeasonality = false;

    @Schema(description = "包含竞争对手数据", example = "false")
    private Boolean includeCompetitorData = false;
}
