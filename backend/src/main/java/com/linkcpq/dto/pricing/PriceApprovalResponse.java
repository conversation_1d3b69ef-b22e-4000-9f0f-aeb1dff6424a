package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格审批响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "价格审批响应")
public class PriceApprovalResponse {

    @Schema(description = "审批ID", example = "1")
    private Long approvalId;

    @Schema(description = "审批状态", example = "APPROVED", allowableValues = {"PENDING", "APPROVED", "REJECTED", "CANCELLED"})
    private String approvalStatus;

    @Schema(description = "审批结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "CONDITIONAL_APPROVED"})
    private String approvalResult;

    @Schema(description = "最终批准价格", example = "3600.00")
    private BigDecimal approvedPrice;

    @Schema(description = "批准折扣率", example = "0.095")
    private BigDecimal approvedDiscountRate;

    @Schema(description = "审批人信息")
    private ApproverInfo approver;

    @Schema(description = "审批时间")
    private LocalDateTime approvalTime;

    @Schema(description = "审批意见", example = "考虑到客户的战略重要性，批准此次特殊折扣")
    private String approvalComments;

    @Schema(description = "审批条件")
    private List<String> approvalConditions;

    @Schema(description = "有效期")
    private LocalDateTime validUntil;

    @Schema(description = "审批流程记录")
    private List<ApprovalStep> approvalSteps;

    @Schema(description = "风险评估结果")
    private RiskAssessmentResult riskAssessment;

    @Data
    @Schema(description = "审批人信息")
    public static class ApproverInfo {
        @Schema(description = "审批人ID", example = "1")
        private Long approverId;

        @Schema(description = "审批人姓名", example = "张经理")
        private String approverName;

        @Schema(description = "审批人职位", example = "销售总监")
        private String approverTitle;

        @Schema(description = "审批级别", example = "DIRECTOR", allowableValues = {"MANAGER", "DIRECTOR", "VP", "CEO"})
        private String approvalLevel;
    }

    @Data
    @Schema(description = "审批步骤")
    public static class ApprovalStep {
        @Schema(description = "步骤序号", example = "1")
        private Integer stepNumber;

        @Schema(description = "审批人", example = "李经理")
        private String approverName;

        @Schema(description = "审批动作", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "FORWARDED"})
        private String action;

        @Schema(description = "审批时间")
        private LocalDateTime actionTime;

        @Schema(description = "审批意见", example = "同意给予特殊折扣")
        private String comments;
    }

    @Data
    @Schema(description = "风险评估结果")
    public static class RiskAssessmentResult {
        @Schema(description = "风险等级", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String riskLevel;

        @Schema(description = "利润影响", example = "-0.05")
        private BigDecimal profitImpact;

        @Schema(description = "市场影响", example = "NEUTRAL", allowableValues = {"POSITIVE", "NEUTRAL", "NEGATIVE"})
        private String marketImpact;

        @Schema(description = "风险因素")
        private List<String> riskFactors;

        @Schema(description = "缓解措施")
        private List<String> mitigationMeasures;
    }
}
