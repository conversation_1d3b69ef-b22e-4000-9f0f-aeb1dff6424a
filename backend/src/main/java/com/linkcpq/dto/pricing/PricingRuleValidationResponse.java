package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定价规则验证响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "定价规则验证响应")
public class PricingRuleValidationResponse {

    @Schema(description = "验证结果", example = "VALID", allowableValues = {"VALID", "INVALID", "WARNING"})
    private String validationResult;

    @Schema(description = "验证通过", example = "true")
    private Boolean isValid;

    @Schema(description = "验证时间")
    private LocalDateTime validatedAt;

    @Schema(description = "验证详情")
    private List<ValidationDetail> validationDetails;

    @Schema(description = "错误信息")
    private List<String> errors;

    @Schema(description = "警告信息")
    private List<String> warnings;

    @Schema(description = "建议修复措施")
    private List<String> suggestions;

    @Data
    @Schema(description = "验证详情")
    public static class ValidationDetail {
        @Schema(description = "规则ID", example = "1")
        private Long ruleId;

        @Schema(description = "规则名称", example = "VIP客户折扣规则")
        private String ruleName;

        @Schema(description = "验证状态", example = "PASSED", allowableValues = {"PASSED", "FAILED", "WARNING"})
        private String status;

        @Schema(description = "验证消息", example = "规则验证通过")
        private String message;

        @Schema(description = "影响级别", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
        private String severity;
    }
}
