package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 阶梯定价请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "阶梯定价请求")
public class TierPricingRequest {

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @Schema(description = "购买数量", example = "50")
    private Integer quantity;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "客户类型", example = "ENTERPRISE")
    private String customerType;

    @Schema(description = "合同期限（月）", example = "24")
    private Integer contractPeriod;

    @Schema(description = "是否年付", example = "true")
    private Boolean annualPayment = false;

    @Schema(description = "基础价格", example = "3980.00")
    private BigDecimal basePrice;
}
