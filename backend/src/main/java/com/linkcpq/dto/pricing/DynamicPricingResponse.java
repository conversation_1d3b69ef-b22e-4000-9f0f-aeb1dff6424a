package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 动态定价响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "动态定价响应")
public class DynamicPricingResponse {

    @Schema(description = "动态价格", example = "3750.00")
    private BigDecimal dynamicPrice;

    @Schema(description = "基础价格", example = "3980.00")
    private BigDecimal basePrice;

    @Schema(description = "总调整幅度", example = "-0.058")
    private BigDecimal totalAdjustment;

    @Schema(description = "价格有效期")
    private LocalDateTime validUntil;

    @Schema(description = "定价因子分析")
    private List<PricingFactor> pricingFactors;

    @Schema(description = "置信度", example = "0.85")
    private BigDecimal confidence;

    @Schema(description = "推荐理由")
    private List<String> recommendations;

    @Schema(description = "风险提示")
    private List<String> riskWarnings;

    @Schema(description = "替代方案")
    private List<AlternativeOption> alternatives;

    @Data
    @Schema(description = "定价因子")
    public static class PricingFactor {
        @Schema(description = "因子名称", example = "市场需求")
        private String factorName;

        @Schema(description = "因子类型", example = "MARKET", allowableValues = {"MARKET", "CUSTOMER", "PRODUCT", "COMPETITION", "SEASONAL"})
        private String factorType;

        @Schema(description = "因子值", example = "HIGH")
        private String factorValue;

        @Schema(description = "影响权重", example = "0.25")
        private BigDecimal weight;

        @Schema(description = "价格影响", example = "-0.02")
        private BigDecimal priceImpact;

        @Schema(description = "影响说明", example = "高需求导致价格上调2%")
        private String description;
    }

    @Data
    @Schema(description = "替代方案")
    public static class AlternativeOption {
        @Schema(description = "方案名称", example = "保守定价")
        private String optionName;

        @Schema(description = "建议价格", example = "3850.00")
        private BigDecimal suggestedPrice;

        @Schema(description = "预期成交概率", example = "0.90")
        private BigDecimal winProbability;

        @Schema(description = "预期利润", example = "1540.00")
        private BigDecimal expectedProfit;

        @Schema(description = "方案描述", example = "采用较为保守的定价策略，确保高成交率")
        private String description;

        @Schema(description = "适用场景")
        private List<String> applicableScenarios;
    }
}
