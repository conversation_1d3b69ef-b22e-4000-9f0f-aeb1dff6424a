package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 竞争定价响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "竞争定价响应")
public class CompetitivePricingResponse {

    @Schema(description = "建议价格", example = "3400.00")
    private BigDecimal suggestedPrice;

    @Schema(description = "价格定位", example = "COMPETITIVE", allowableValues = {"PREMIUM", "COMPETITIVE", "AGGRESSIVE", "PENETRATION"})
    private String pricePosition;

    @Schema(description = "竞争优势分析")
    private List<CompetitiveAdvantage> advantages;

    @Schema(description = "风险评估")
    private RiskAssessment riskAssessment;

    @Schema(description = "市场定位建议")
    private String positioningRecommendation;

    @Data
    @Schema(description = "竞争优势")
    public static class CompetitiveAdvantage {
        @Schema(description = "优势类型", example = "PRICE", allowableValues = {"PRICE", "FEATURE", "SERVICE", "BRAND"})
        private String type;

        @Schema(description = "优势描述", example = "价格比竞品低15%")
        private String description;

        @Schema(description = "重要性", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String importance;

        @Schema(description = "影响分数", example = "8.5")
        private BigDecimal impactScore;
    }

    @Data
    @Schema(description = "风险评估")
    public static class RiskAssessment {
        @Schema(description = "价格战风险", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String priceWarRisk;

        @Schema(description = "利润率影响", example = "-0.05")
        private BigDecimal marginImpact;

        @Schema(description = "市场反应预测", example = "POSITIVE", allowableValues = {"NEGATIVE", "NEUTRAL", "POSITIVE"})
        private String marketReaction;

        @Schema(description = "建议监控指标")
        private List<String> monitoringMetrics;
    }
}
