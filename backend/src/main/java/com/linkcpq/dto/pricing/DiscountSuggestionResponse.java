package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 折扣建议响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "折扣建议响应")
public class DiscountSuggestionResponse {

    @Schema(description = "建议折扣率", example = "0.15")
    private BigDecimal suggestedDiscountRate;

    @Schema(description = "建议价格", example = "3383.00")
    private BigDecimal suggestedPrice;

    @Schema(description = "最小折扣率", example = "0.05")
    private BigDecimal minDiscountRate;

    @Schema(description = "最大折扣率", example = "0.25")
    private BigDecimal maxDiscountRate;

    @Schema(description = "折扣原因", example = "大客户优惠")
    private String discountReason;

    @Schema(description = "置信度", example = "0.85")
    private BigDecimal confidence;

    @Schema(description = "风险等级", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH"})
    private String riskLevel;

    @Schema(description = "需要审批", example = "false")
    private Boolean requiresApproval;

    @Schema(description = "审批级别", example = "MANAGER", allowableValues = {"NONE", "MANAGER", "DIRECTOR", "VP"})
    private String approvalLevel;

    @Schema(description = "折扣建议列表")
    private List<DiscountOption> discountOptions;

    @Schema(description = "市场分析")
    private MarketAnalysis marketAnalysis;

    @Schema(description = "建议有效期（小时）", example = "72")
    private Integer validityHours;

    @Schema(description = "备注")
    private String remarks;

    @Data
    @Schema(description = "折扣选项")
    public static class DiscountOption {
        @Schema(description = "折扣率", example = "0.10")
        private BigDecimal discountRate;

        @Schema(description = "折扣价格", example = "3582.00")
        private BigDecimal discountedPrice;

        @Schema(description = "选项描述", example = "标准折扣")
        private String description;

        @Schema(description = "适用条件", example = "购买数量≥5")
        private String conditions;

        @Schema(description = "优先级", example = "1")
        private Integer priority;

        @Schema(description = "预期成交概率", example = "0.75")
        private BigDecimal winProbability;
    }

    @Data
    @Schema(description = "市场分析")
    public static class MarketAnalysis {
        @Schema(description = "市场平均价格", example = "3500.00")
        private BigDecimal marketAveragePrice;

        @Schema(description = "竞争优势", example = "功能更全面")
        private String competitiveAdvantage;

        @Schema(description = "价格定位", example = "PREMIUM", allowableValues = {"BUDGET", "STANDARD", "PREMIUM", "LUXURY"})
        private String pricePosition;

        @Schema(description = "市场份额影响", example = "POSITIVE", allowableValues = {"NEGATIVE", "NEUTRAL", "POSITIVE"})
        private String marketShareImpact;
    }
}
