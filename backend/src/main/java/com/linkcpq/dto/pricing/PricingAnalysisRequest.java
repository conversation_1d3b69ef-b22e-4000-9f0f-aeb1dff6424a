package com.linkcpq.dto.pricing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定价分析请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "定价分析请求")
public class PricingAnalysisRequest {

    @Schema(description = "产品ID列表")
    private List<Long> productIds;

    @Schema(description = "分析类型", example = "PROFITABILITY", allowableValues = {"PROFITABILITY", "COMPETITIVENESS", "MARKET_POSITION", "TREND_ANALYSIS"})
    private String analysisType;

    @Schema(description = "分析时间范围开始")
    private LocalDateTime startDate;

    @Schema(description = "分析时间范围结束")
    private LocalDateTime endDate;

    @Schema(description = "客户类型过滤")
    private List<String> customerTypes;

    @Schema(description = "地区过滤")
    private List<String> regions;

    @Schema(description = "销售渠道过滤")
    private List<String> salesChannels;

    @Schema(description = "分析维度", example = "PRODUCT", allowableValues = {"PRODUCT", "CUSTOMER", "REGION", "CHANNEL", "TIME"})
    private String dimension = "PRODUCT";

    @Schema(description = "聚合方式", example = "MONTHLY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"})
    private String aggregation = "MONTHLY";

    @Schema(description = "包含竞争对手分析", example = "true")
    private Boolean includeCompetitorAnalysis = false;

    @Schema(description = "包含趋势预测", example = "true")
    private Boolean includeTrendForecast = false;
}
