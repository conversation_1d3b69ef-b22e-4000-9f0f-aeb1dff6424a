package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配置响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置响应")
public class ConfigurationResponse {

    @Schema(description = "配置ID", example = "1")
    private Long id;

    @Schema(description = "配置名称", example = "金蝶云星辰标准版配置")
    private String configurationName;

    @Schema(description = "产品信息")
    private ProductInfo product;

    @Schema(description = "客户信息")
    private CustomerInfo customer;

    @Schema(description = "配置状态", example = "VALID")
    private String status;

    @Schema(description = "状态描述", example = "有效")
    private String statusDesc;

    @Schema(description = "配置版本", example = "1.0")
    private String version;

    @Schema(description = "配置描述", example = "适用于中小企业的标准财务管理配置")
    private String description;

    @Schema(description = "配置项列表")
    private List<ConfigurationItemResponse> items;

    @Schema(description = "配置规则")
    private Map<String, Object> rules;

    @Schema(description = "配置约束")
    private Map<String, Object> constraints;

    @Schema(description = "验证结果")
    private ValidationResult validationResult;

    @Schema(description = "创建人信息")
    private CreatorInfo createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "备注")
    private String remark;

    @Data
    @Schema(description = "产品信息")
    public static class ProductInfo {
        @Schema(description = "产品ID", example = "1")
        private Long id;

        @Schema(description = "产品名称", example = "金蝶云星辰标准版")
        private String name;

        @Schema(description = "产品代码", example = "KIS_CLOUD_STD")
        private String code;

        @Schema(description = "产品类型", example = "财务软件")
        private String type;
    }

    @Data
    @Schema(description = "客户信息")
    public static class CustomerInfo {
        @Schema(description = "客户ID", example = "1")
        private Long id;

        @Schema(description = "客户名称", example = "某某科技有限公司")
        private String name;

        @Schema(description = "客户代码", example = "C001")
        private String code;
    }

    @Data
    @Schema(description = "配置项响应")
    public static class ConfigurationItemResponse {
        @Schema(description = "配置项ID", example = "1")
        private Long id;

        @Schema(description = "配置项名称", example = "用户数量")
        private String name;

        @Schema(description = "配置项代码", example = "user_count")
        private String code;

        @Schema(description = "配置项类型", example = "NUMBER")
        private String type;

        @Schema(description = "配置项值", example = "3")
        private Object value;

        @Schema(description = "默认值", example = "1")
        private Object defaultValue;

        @Schema(description = "是否必填", example = "true")
        private Boolean required;

        @Schema(description = "最小值", example = "1")
        private Object minValue;

        @Schema(description = "最大值", example = "100")
        private Object maxValue;

        @Schema(description = "可选值列表")
        private List<Object> options;

        @Schema(description = "验证规则")
        private String validationRule;

        @Schema(description = "显示顺序", example = "1")
        private Integer displayOrder;

        @Schema(description = "是否可见", example = "true")
        private Boolean visible;

        @Schema(description = "是否可编辑", example = "true")
        private Boolean editable;

        @Schema(description = "配置项描述", example = "系统支持的最大用户数量")
        private String description;

        @Schema(description = "单位", example = "个")
        private String unit;

        @Schema(description = "分组", example = "基础配置")
        private String group;

        @Schema(description = "依赖项")
        private List<String> dependencies;

        @Schema(description = "条件显示规则")
        private Map<String, Object> conditionalRules;

        @Schema(description = "验证状态", example = "VALID")
        private String validationStatus;

        @Schema(description = "验证消息")
        private String validationMessage;
    }

    @Data
    @Schema(description = "验证结果")
    public static class ValidationResult {
        @Schema(description = "是否有效", example = "true")
        private Boolean valid;

        @Schema(description = "错误信息列表")
        private List<String> errors;

        @Schema(description = "警告信息列表")
        private List<String> warnings;

        @Schema(description = "验证时间")
        private LocalDateTime validatedAt;
    }

    @Data
    @Schema(description = "创建人信息")
    public static class CreatorInfo {
        @Schema(description = "用户ID", example = "1")
        private Long id;

        @Schema(description = "用户名", example = "admin")
        private String username;

        @Schema(description = "真实姓名", example = "张三")
        private String realName;

        @Schema(description = "部门", example = "销售部")
        private String department;
    }
}
