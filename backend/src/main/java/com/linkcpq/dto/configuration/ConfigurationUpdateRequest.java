package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 配置更新请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置更新请求")
public class ConfigurationUpdateRequest {

    @Size(max = 200, message = "配置名称长度不能超过200个字符")
    @Schema(description = "配置名称", example = "金蝶云星辰标准版配置")
    private String configurationName;

    @Schema(description = "配置版本", example = "1.1")
    private String version;

    @Size(max = 1000, message = "配置描述长度不能超过1000个字符")
    @Schema(description = "配置描述", example = "适用于中小企业的标准财务管理配置")
    private String description;

    @Schema(description = "配置项列表")
    private List<ConfigurationCreateRequest.ConfigurationItemRequest> items;

    @Schema(description = "配置规则")
    private Map<String, Object> rules;

    @Schema(description = "配置约束")
    private Map<String, Object> constraints;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;
}
