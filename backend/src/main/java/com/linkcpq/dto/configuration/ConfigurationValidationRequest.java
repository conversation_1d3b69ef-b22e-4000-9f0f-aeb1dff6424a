package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 配置验证请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置验证请求")
public class ConfigurationValidationRequest {

    @NotNull(message = "配置ID不能为空")
    @Schema(description = "配置ID", example = "1")
    private Long configurationId;

    @Schema(description = "验证类型", example = "FULL", allowableValues = {"FULL", "BASIC", "RULES_ONLY", "DEPENDENCIES_ONLY"})
    private String validationType = "FULL";

    @Schema(description = "验证规则ID列表")
    private List<Long> ruleIds;

    @Schema(description = "验证上下文")
    private Map<String, Object> validationContext;

    @Schema(description = "是否严格模式", example = "false")
    private Boolean strictMode = false;

    @Schema(description = "是否验证依赖关系", example = "true")
    private Boolean validateDependencies = true;

    @Schema(description = "是否验证约束条件", example = "true")
    private Boolean validateConstraints = true;

    @Schema(description = "是否验证业务规则", example = "true")
    private Boolean validateBusinessRules = true;

    @Schema(description = "客户ID（用于客户特定验证）", example = "1")
    private Long customerId;

    @Schema(description = "产品ID（用于产品特定验证）", example = "1")
    private Long productId;
}
