package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 配置创建请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置创建请求")
public class ConfigurationCreateRequest {

    @NotBlank(message = "配置名称不能为空")
    @Size(max = 200, message = "配置名称长度不能超过200个字符")
    @Schema(description = "配置名称", example = "金蝶云星辰标准版配置")
    private String configurationName;

    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "配置版本", example = "1.0")
    private String version = "1.0";

    @Size(max = 1000, message = "配置描述长度不能超过1000个字符")
    @Schema(description = "配置描述", example = "适用于中小企业的标准财务管理配置")
    private String description;

    @NotNull(message = "配置项不能为空")
    @Schema(description = "配置项列表")
    private List<ConfigurationItemRequest> items;

    @Schema(description = "配置规则")
    private Map<String, Object> rules;

    @Schema(description = "配置约束")
    private Map<String, Object> constraints;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;

    @Data
    @Schema(description = "配置项请求")
    public static class ConfigurationItemRequest {

        @NotBlank(message = "配置项名称不能为空")
        @Schema(description = "配置项名称", example = "用户数量")
        private String name;

        @NotBlank(message = "配置项代码不能为空")
        @Schema(description = "配置项代码", example = "user_count")
        private String code;

        @NotBlank(message = "配置项类型不能为空")
        @Schema(description = "配置项类型", example = "NUMBER", allowableValues = {"TEXT", "NUMBER", "BOOLEAN", "SELECT", "MULTI_SELECT", "DATE", "RANGE"})
        private String type;

        @Schema(description = "配置项值", example = "3")
        private Object value;

        @Schema(description = "默认值", example = "1")
        private Object defaultValue;

        @Schema(description = "是否必填", example = "true")
        private Boolean required = false;

        @Schema(description = "最小值", example = "1")
        private Object minValue;

        @Schema(description = "最大值", example = "100")
        private Object maxValue;

        @Schema(description = "可选值列表")
        private List<Object> options;

        @Schema(description = "验证规则")
        private String validationRule;

        @Schema(description = "显示顺序", example = "1")
        private Integer displayOrder = 0;

        @Schema(description = "是否可见", example = "true")
        private Boolean visible = true;

        @Schema(description = "是否可编辑", example = "true")
        private Boolean editable = true;

        @Schema(description = "配置项描述", example = "系统支持的最大用户数量")
        private String description;

        @Schema(description = "单位", example = "个")
        private String unit;

        @Schema(description = "分组", example = "基础配置")
        private String group;

        @Schema(description = "依赖项")
        private List<String> dependencies;

        @Schema(description = "条件显示规则")
        private Map<String, Object> conditionalRules;
    }
}
