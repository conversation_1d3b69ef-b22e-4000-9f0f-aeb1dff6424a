package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 配置查询请求DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置查询请求")
public class ConfigurationQueryRequest {

    @Schema(description = "配置名称（模糊查询）", example = "金蝶云星辰")
    private String configurationName;

    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "产品名称（模糊查询）", example = "云星辰")
    private String productName;

    @Schema(description = "客户ID", example = "1")
    private Long customerId;

    @Schema(description = "客户名称（模糊查询）", example = "某某公司")
    private String customerName;

    @Schema(description = "配置状态", example = "DRAFT", allowableValues = {"DRAFT", "VALID", "INVALID", "ARCHIVED"})
    private String status;

    @Schema(description = "配置版本", example = "1.0")
    private String version;

    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    @Schema(description = "创建人姓名（模糊查询）", example = "张三")
    private String createdByName;

    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createdAtStart;

    @Schema(description = "创建时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime createdAtEnd;

    @Schema(description = "更新时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime updatedAtStart;

    @Schema(description = "更新时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime updatedAtEnd;

    @Schema(description = "配置项名称（模糊查询）", example = "用户数量")
    private String itemName;

    @Schema(description = "配置项代码", example = "user_count")
    private String itemCode;

    @Schema(description = "配置项类型", example = "NUMBER")
    private String itemType;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    private Integer pageSize = 20;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortBy = "createdAt";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortDirection = "desc";
}
