package com.linkcpq.dto.configuration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 配置验证响应DTO
 * <AUTHOR> CPQ Team
 */
@Data
@Schema(description = "配置验证响应")
public class ConfigurationValidationResponse {

    @Schema(description = "配置ID", example = "1")
    private Long configurationId;

    @Schema(description = "验证结果", example = "VALID", allowableValues = {"VALID", "INVALID", "WARNING"})
    private String validationResult;

    @Schema(description = "验证通过", example = "true")
    private Boolean isValid;

    @Schema(description = "验证时间")
    private LocalDateTime validatedAt;

    @Schema(description = "验证详情")
    private List<ValidationDetail> validationDetails;

    @Schema(description = "错误信息")
    private List<String> errors;

    @Schema(description = "警告信息")
    private List<String> warnings;

    @Schema(description = "建议修复措施")
    private List<String> suggestions;

    @Schema(description = "验证摘要")
    private ValidationSummary summary;

    @Data
    @Schema(description = "验证详情")
    public static class ValidationDetail {
        @Schema(description = "验证项名称", example = "用户数量配置")
        private String itemName;

        @Schema(description = "验证项代码", example = "user_count")
        private String itemCode;

        @Schema(description = "验证状态", example = "PASSED", allowableValues = {"PASSED", "FAILED", "WARNING", "SKIPPED"})
        private String status;

        @Schema(description = "验证消息", example = "用户数量配置有效")
        private String message;

        @Schema(description = "影响级别", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
        private String severity;

        @Schema(description = "验证规则", example = "用户数量必须在1-1000之间")
        private String validationRule;

        @Schema(description = "当前值", example = "50")
        private Object currentValue;

        @Schema(description = "期望值", example = "1-1000")
        private Object expectedValue;
    }

    @Data
    @Schema(description = "验证摘要")
    public static class ValidationSummary {
        @Schema(description = "总验证项数", example = "25")
        private Integer totalItems;

        @Schema(description = "通过数", example = "20")
        private Integer passedItems;

        @Schema(description = "失败数", example = "3")
        private Integer failedItems;

        @Schema(description = "警告数", example = "2")
        private Integer warningItems;

        @Schema(description = "跳过数", example = "0")
        private Integer skippedItems;

        @Schema(description = "验证通过率", example = "0.8")
        private Double passRate;
    }
}
