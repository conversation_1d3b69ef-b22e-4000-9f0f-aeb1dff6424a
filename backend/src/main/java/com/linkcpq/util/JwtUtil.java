package com.linkcpq.util;

import com.linkcpq.entity.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR> CPQ Team
 */
@Slf4j
@Component
public class JwtUtil {

    @Value("${cpq.jwt.secret}")
    private String secret;

    @Value("${cpq.jwt.expiration}")
    private Long accessTokenExpiration;

    @Value("${cpq.jwt.refresh-expiration}")
    private Long refreshTokenExpiration;

    private static final String TOKEN_TYPE_ACCESS = "access";
    private static final String TOKEN_TYPE_REFRESH = "refresh";
    private static final String CLAIM_USER_ID = "userId";
    private static final String CLAIM_USERNAME = "username";
    private static final String CLAIM_TOKEN_TYPE = "tokenType";

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成访问Token
     */
    public String generateAccessToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, user.getId());
        claims.put(CLAIM_USERNAME, user.getUsername());
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_ACCESS);
        
        return createToken(claims, user.getUsername(), accessTokenExpiration);
    }

    /**
     * 生成刷新Token
     */
    public String generateRefreshToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, user.getId());
        claims.put(CLAIM_USERNAME, user.getUsername());
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_REFRESH);
        
        return createToken(claims, user.getUsername(), refreshTokenExpiration);
    }

    /**
     * 创建Token
     */
    private String createToken(Map<String, Object> claims, String subject, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 验证访问Token
     */
    public boolean validateAccessToken(String token) {
        try {
            Claims claims = parseToken(token);
            String tokenType = (String) claims.get(CLAIM_TOKEN_TYPE);
            return TOKEN_TYPE_ACCESS.equals(tokenType) && !isTokenExpired(claims);
        } catch (Exception e) {
            log.warn("访问Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证刷新Token
     */
    public boolean validateRefreshToken(String token) {
        try {
            Claims claims = parseToken(token);
            String tokenType = (String) claims.get(CLAIM_TOKEN_TYPE);
            return TOKEN_TYPE_REFRESH.equals(tokenType) && !isTokenExpired(claims);
        } catch (Exception e) {
            log.warn("刷新Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析Token
     */
    private Claims parseToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 检查Token是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        return claims.getExpiration().before(new Date());
    }

    /**
     * 从访问Token中获取用户ID
     */
    public Long getUserIdFromAccessToken(String token) {
        try {
            Claims claims = parseToken(token);
            String tokenType = (String) claims.get(CLAIM_TOKEN_TYPE);
            if (TOKEN_TYPE_ACCESS.equals(tokenType)) {
                Object userId = claims.get(CLAIM_USER_ID);
                if (userId instanceof Number) {
                    return ((Number) userId).longValue();
                }
            }
        } catch (Exception e) {
            log.warn("从访问Token获取用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从刷新Token中获取用户ID
     */
    public Long getUserIdFromRefreshToken(String token) {
        try {
            Claims claims = parseToken(token);
            String tokenType = (String) claims.get(CLAIM_TOKEN_TYPE);
            if (TOKEN_TYPE_REFRESH.equals(tokenType)) {
                Object userId = claims.get(CLAIM_USER_ID);
                if (userId instanceof Number) {
                    return ((Number) userId).longValue();
                }
            }
        } catch (Exception e) {
            log.warn("从刷新Token获取用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从访问Token中获取用户名
     */
    public String getUsernameFromAccessToken(String token) {
        try {
            Claims claims = parseToken(token);
            String tokenType = (String) claims.get(CLAIM_TOKEN_TYPE);
            if (TOKEN_TYPE_ACCESS.equals(tokenType)) {
                return (String) claims.get(CLAIM_USERNAME);
            }
        } catch (Exception e) {
            log.warn("从访问Token获取用户名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取Token过期时间(毫秒)
     */
    public long getTokenExpiration(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            return expiration.getTime() - System.currentTimeMillis();
        } catch (Exception e) {
            log.warn("获取Token过期时间失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取访问Token过期时间
     */
    public Long getAccessTokenExpiration() {
        return accessTokenExpiration;
    }

    /**
     * 获取刷新Token过期时间
     */
    public Long getRefreshTokenExpiration() {
        return refreshTokenExpiration;
    }

    /**
     * 生成通用Token（用于重置密码等）
     */
    public String generateToken(String username, Long expiration) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USERNAME, username);
        claims.put(CLAIM_TOKEN_TYPE, "reset");

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 验证通用Token
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = parseToken(token);
            return !isTokenExpired(claims);
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从Token中获取用户名（通用方法）
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return (String) claims.get(CLAIM_USERNAME);
        } catch (Exception e) {
            log.warn("从Token获取用户名失败: {}", e.getMessage());
            return null;
        }
    }
}
