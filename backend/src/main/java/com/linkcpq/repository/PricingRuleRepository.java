package com.linkcpq.repository;

import com.linkcpq.entity.PricingRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 定价规则数据访问层
 * <AUTHOR> CPQ Team
 */
@Repository
public interface PricingRuleRepository extends JpaRepository<PricingRule, Long>, JpaSpecificationExecutor<PricingRule> {

    /**
     * 根据规则名称查找定价规则
     */
    Optional<PricingRule> findByRuleNameAndDeletedFalse(String ruleName);

    /**
     * 根据规则代码查找定价规则
     */
    Optional<PricingRule> findByRuleCodeAndDeletedFalse(String ruleCode);

    /**
     * 根据规则类型查找定价规则列表
     */
    List<PricingRule> findByRuleTypeAndDeletedFalse(String ruleType);

    /**
     * 查找启用的定价规则列表
     */
    List<PricingRule> findByIsActiveTrueAndDeletedFalse();

    /**
     * 根据产品ID查找适用的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.isActive = true AND pr.deleted = false " +
           "AND (pr.applicableProducts IS NULL OR pr.applicableProducts LIKE %:productId%)")
    List<PricingRule> findApplicableRulesByProductId(@Param("productId") String productId);

    /**
     * 根据客户类型查找适用的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.isActive = true AND pr.deleted = false " +
           "AND (pr.applicableCustomerTypes IS NULL OR pr.applicableCustomerTypes LIKE %:customerType%)")
    List<PricingRule> findApplicableRulesByCustomerType(@Param("customerType") String customerType);

    /**
     * 根据优先级排序查找定价规则
     */
    List<PricingRule> findByDeletedFalseOrderByPriorityAsc();

    /**
     * 查找指定优先级范围的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.priority BETWEEN :minPriority AND :maxPriority " +
           "AND pr.deleted = false ORDER BY pr.priority ASC")
    List<PricingRule> findByPriorityRange(@Param("minPriority") Integer minPriority, 
                                         @Param("maxPriority") Integer maxPriority);

    /**
     * 根据规则分组查找定价规则
     */
    List<PricingRule> findByRuleGroupAndDeletedFalse(String ruleGroup);

    /**
     * 查找全局定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.isGlobal = true AND pr.isActive = true AND pr.deleted = false")
    List<PricingRule> findGlobalRules();

    /**
     * 根据产品和客户类型查找适用的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.isActive = true AND pr.deleted = false " +
           "AND (pr.applicableProducts IS NULL OR pr.applicableProducts LIKE %:productId%) " +
           "AND (pr.applicableCustomerTypes IS NULL OR pr.applicableCustomerTypes LIKE %:customerType%) " +
           "ORDER BY pr.priority ASC")
    List<PricingRule> findApplicableRules(@Param("productId") String productId, 
                                         @Param("customerType") String customerType);

    /**
     * 统计启用的定价规则数量
     */
    @Query("SELECT COUNT(pr) FROM PricingRule pr WHERE pr.isActive = true AND pr.deleted = false")
    Long countActiveRules();

    /**
     * 统计指定类型的定价规则数量
     */
    @Query("SELECT COUNT(pr) FROM PricingRule pr WHERE pr.ruleType = :ruleType AND pr.deleted = false")
    Long countByRuleType(@Param("ruleType") String ruleType);

    /**
     * 查找即将过期的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.endDate <= CURRENT_DATE + :days " +
           "AND pr.isActive = true AND pr.deleted = false")
    List<PricingRule> findExpiringRules(@Param("days") Integer days);

    /**
     * 查找已过期的定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.endDate < CURRENT_DATE " +
           "AND pr.isActive = true AND pr.deleted = false")
    List<PricingRule> findExpiredRules();

    /**
     * 根据创建人查找定价规则
     */
    List<PricingRule> findByCreatedByAndDeletedFalse(Long createdBy);

    /**
     * 模糊查询规则名称
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.ruleName LIKE %:name% AND pr.deleted = false")
    List<PricingRule> findByRuleNameContaining(@Param("name") String name);

    /**
     * 根据标签查找定价规则
     */
    @Query("SELECT pr FROM PricingRule pr WHERE pr.tags LIKE %:tag% AND pr.deleted = false")
    List<PricingRule> findByTag(@Param("tag") String tag);
}
