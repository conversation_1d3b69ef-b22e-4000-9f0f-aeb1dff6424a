package com.linkcpq.repository;

import com.linkcpq.entity.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 客户数据访问层
 * <AUTHOR> CPQ Team
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long>, JpaSpecificationExecutor<Customer> {

    /**
     * 根据客户代码查找客户
     */
    Optional<Customer> findByCustomerCodeAndDeletedFalse(String customerCode);

    /**
     * 根据客户代码查找客户（兼容方法）
     */
    default Customer findByCustomerCode(String customerCode) {
        return findByCustomerCodeAndDeletedFalse(customerCode).orElse(null);
    }

    /**
     * 根据客户名称查找客户
     */
    Optional<Customer> findByCustomerNameAndDeletedFalse(String customerName);

    /**
     * 根据客户名称查找客户（兼容方法）
     */
    default Customer findByCustomerName(String customerName) {
        return findByCustomerNameAndDeletedFalse(customerName).orElse(null);
    }

    /**
     * 根据联系邮箱查找客户
     */
    Optional<Customer> findByContactEmailAndDeletedFalse(String contactEmail);

    /**
     * 根据销售代表ID查找客户列表
     */
    List<Customer> findBySalesRepIdAndDeletedFalse(Long salesRepId);

    /**
     * 根据客户类型查找客户列表
     */
    List<Customer> findByCustomerTypeAndDeletedFalse(String customerType);

    /**
     * 根据地区查找客户列表
     */
    List<Customer> findByRegionAndDeletedFalse(String region);

    /**
     * 查找VIP客户列表
     */
    List<Customer> findByIsVipTrueAndDeletedFalse();

    /**
     * 查找活跃客户列表
     */
    List<Customer> findByIsActiveTrueAndDeletedFalse();

    /**
     * 根据客户状态查找客户列表
     */
    List<Customer> findByStatusAndDeletedFalse(String status);

    /**
     * 模糊查询客户名称
     */
    @Query("SELECT c FROM Customer c WHERE c.customerName LIKE %:name% AND c.deleted = false")
    List<Customer> findByCustomerNameContaining(@Param("name") String name);

    /**
     * 根据行业查找客户
     */
    List<Customer> findByIndustryAndDeletedFalse(String industry);

    /**
     * 根据公司规模查找客户
     */
    List<Customer> findByCompanySizeAndDeletedFalse(String companySize);

    /**
     * 根据信用等级查找客户
     */
    List<Customer> findByCreditRatingAndDeletedFalse(String creditRating);

    /**
     * 查找指定省份的客户
     */
    List<Customer> findByProvinceAndDeletedFalse(String province);

    /**
     * 查找指定城市的客户
     */
    List<Customer> findByCityAndDeletedFalse(String city);

    /**
     * 统计客户总数
     */
    @Query("SELECT COUNT(c) FROM Customer c WHERE c.deleted = false")
    Long countActiveCustomers();

    /**
     * 统计VIP客户数量
     */
    @Query("SELECT COUNT(c) FROM Customer c WHERE c.isVip = true AND c.deleted = false")
    Long countVipCustomers();

    /**
     * 统计指定销售代表的客户数量
     */
    @Query("SELECT COUNT(c) FROM Customer c WHERE c.salesRepId = :salesRepId AND c.deleted = false")
    Long countBySalesRep(@Param("salesRepId") Long salesRepId);
}
