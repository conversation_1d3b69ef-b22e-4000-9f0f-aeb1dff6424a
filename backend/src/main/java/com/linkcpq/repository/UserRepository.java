package com.linkcpq.repository;

import com.linkcpq.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Repository接口
 * 
 * <AUTHOR> CPQ Team
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户（未删除）
     */
    Optional<User> findByUsernameAndDeletedFalse(String username);

    /**
     * 根据用户名查找用户（兼容方法）
     */
    default User findByUsername(String username) {
        return findByUsernameAndDeletedFalse(username).orElse(null);
    }

    /**
     * 根据邮箱查找用户（未删除）
     */
    Optional<User> findByEmailAndDeletedFalse(String email);

    /**
     * 根据邮箱查找用户（兼容方法）
     */
    default User findByEmail(String email) {
        return findByEmailAndDeletedFalse(email).orElse(null);
    }

    /**
     * 根据ID查找用户（未删除）
     */
    Optional<User> findByIdAndDeletedFalse(Long id);

    /**
     * 检查用户名是否存在（未删除）
     */
    boolean existsByUsernameAndDeletedFalse(String username);

    /**
     * 检查邮箱是否存在（未删除）
     */
    boolean existsByEmailAndDeletedFalse(String email);

    /**
     * 根据状态查找用户
     */
    List<User> findByStatusAndDeletedFalse(User.UserStatus status);

    /**
     * 查找锁定已过期的用户
     */
    @Query("SELECT u FROM User u WHERE u.status = 'LOCKED' AND u.lockedUntil < :now AND u.deleted = false")
    List<User> findExpiredLockedUsers(@Param("now") LocalDateTime now);

    /**
     * 根据部门查找用户
     */
    List<User> findByDepartmentAndDeletedFalse(String department);

    /**
     * 根据上级主管ID查找用户
     */
    List<User> findByManagerIdAndDeletedFalse(Long managerId);

    /**
     * 查找最近登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt >= :since AND u.deleted = false ORDER BY u.lastLoginAt DESC")
    List<User> findRecentlyLoggedInUsers(@Param("since") LocalDateTime since);

    /**
     * 查找从未登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt IS NULL AND u.deleted = false")
    List<User> findNeverLoggedInUsers();

    /**
     * 统计用户数量按状态
     */
    @Query("SELECT u.status, COUNT(u) FROM User u WHERE u.deleted = false GROUP BY u.status")
    List<Object[]> countUsersByStatus();

    /**
     * 查找邮箱未验证的用户
     */
    List<User> findByEmailVerifiedFalseAndDeletedFalse();

    /**
     * 查找启用了双因子认证的用户
     */
    List<User> findByTwoFactorEnabledTrueAndDeletedFalse();

    /**
     * 根据员工编号查找用户
     */
    Optional<User> findByEmployeeNumberAndDeletedFalse(String employeeNumber);

    /**
     * 查找指定时间范围内创建的用户
     */
    @Query("SELECT u FROM User u WHERE u.createdAt BETWEEN :startDate AND :endDate AND u.deleted = false")
    List<User> findUsersCreatedBetween(@Param("startDate") LocalDateTime startDate, 
                                      @Param("endDate") LocalDateTime endDate);

    /**
     * 查找登录失败次数超过指定次数的用户
     */
    @Query("SELECT u FROM User u WHERE u.failedLoginCount >= :threshold AND u.deleted = false")
    List<User> findUsersWithFailedLoginCountGreaterThan(@Param("threshold") Integer threshold);

    /**
     * 查找密码需要更新的用户（密码超过指定天数未更新）
     */
    @Query("SELECT u FROM User u WHERE u.passwordChangedAt < :threshold AND u.deleted = false")
    List<User> findUsersWithOldPasswords(@Param("threshold") LocalDateTime threshold);
}
