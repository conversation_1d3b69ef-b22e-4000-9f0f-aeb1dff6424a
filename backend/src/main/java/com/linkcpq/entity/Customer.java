package com.linkcpq.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户实体类
 * <AUTHOR> CPQ Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "customers")
public class Customer extends BaseEntity {

    @Column(name = "customer_code", unique = true, nullable = false, length = 50)
    private String customerCode;

    @Column(name = "customer_name", nullable = false, length = 200)
    private String customerName;

    @Column(name = "customer_type", length = 50)
    private String customerType;

    @Column(name = "industry", length = 100)
    private String industry;

    @Column(name = "company_size", length = 50)
    private String companySize;

    @Column(name = "annual_revenue", precision = 15, scale = 2)
    private BigDecimal annualRevenue;

    @Column(name = "credit_rating", length = 10)
    private String creditRating;

    @Column(name = "payment_terms", length = 50)
    private String paymentTerms;

    @Column(name = "tax_number", length = 50)
    private String taxNumber;

    @Column(name = "legal_representative", length = 100)
    private String legalRepresentative;

    @Column(name = "registered_address", length = 500)
    private String registeredAddress;

    @Column(name = "business_address", length = 500)
    private String businessAddress;

    @Column(name = "contact_person", length = 100)
    private String contactPerson;

    @Column(name = "contact_phone", length = 50)
    private String contactPhone;

    @Column(name = "contact_email", length = 100)
    private String contactEmail;

    @Column(name = "website", length = 200)
    private String website;

    @Column(name = "region", length = 50)
    private String region;

    @Column(name = "province", length = 50)
    private String province;

    @Column(name = "city", length = 50)
    private String city;

    @Column(name = "district", length = 50)
    private String district;

    @Column(name = "postal_code", length = 20)
    private String postalCode;

    @Column(name = "sales_rep_id")
    private Long salesRepId;

    @Column(name = "sales_rep_name", length = 100)
    private String salesRepName;

    @Column(name = "customer_source", length = 50)
    private String customerSource;

    @Column(name = "acquisition_date")
    private LocalDateTime acquisitionDate;

    @Column(name = "first_purchase_date")
    private LocalDateTime firstPurchaseDate;

    @Column(name = "last_purchase_date")
    private LocalDateTime lastPurchaseDate;

    @Column(name = "total_purchase_amount", precision = 15, scale = 2)
    private BigDecimal totalPurchaseAmount;

    @Column(name = "purchase_frequency")
    private Integer purchaseFrequency;

    @Column(name = "average_order_value", precision = 15, scale = 2)
    private BigDecimal averageOrderValue;

    @Column(name = "customer_lifetime_value", precision = 15, scale = 2)
    private BigDecimal customerLifetimeValue;

    @Column(name = "preferred_discount_rate", precision = 5, scale = 4)
    private BigDecimal preferredDiscountRate;

    @Column(name = "max_discount_rate", precision = 5, scale = 4)
    private BigDecimal maxDiscountRate;

    @Column(name = "price_sensitivity", length = 20)
    private String priceSensitivity;

    @Column(name = "status", length = 20)
    private String status;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "is_vip")
    private Boolean isVip = false;

    @Column(name = "vip_level", length = 20)
    private String vipLevel;

    @Column(name = "partnership_type", length = 50)
    private String partnershipType;

    @Column(name = "contract_start_date")
    private LocalDateTime contractStartDate;

    @Column(name = "contract_end_date")
    private LocalDateTime contractEndDate;

    @Column(name = "renewal_probability", precision = 5, scale = 4)
    private BigDecimal renewalProbability;

    @Column(name = "churn_risk", length = 20)
    private String churnRisk;

    @Column(name = "satisfaction_score", precision = 3, scale = 2)
    private BigDecimal satisfactionScore;

    @Column(name = "nps_score")
    private Integer npsScore;

    @Column(name = "support_level", length = 50)
    private String supportLevel;

    @Column(name = "preferred_communication", length = 50)
    private String preferredCommunication;

    @Column(name = "time_zone", length = 50)
    private String timeZone;

    @Column(name = "language_preference", length = 20)
    private String languagePreference;

    @Column(name = "currency_preference", length = 10)
    private String currencyPreference;

    @Column(name = "tags", length = 500)
    private String tags;

    @Column(name = "notes", length = 2000)
    private String notes;

    @Column(name = "internal_notes", length = 2000)
    private String internalNotes;
}
