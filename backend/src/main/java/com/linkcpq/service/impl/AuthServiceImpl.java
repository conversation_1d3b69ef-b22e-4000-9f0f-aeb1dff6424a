package com.linkcpq.service.impl;

import com.linkcpq.common.result.ResultCode;
import com.linkcpq.dto.auth.LoginRequest;
import com.linkcpq.dto.auth.LoginResponse;
import com.linkcpq.dto.user.UserInfoDTO;
import com.linkcpq.entity.User;
import com.linkcpq.exception.BusinessException;
import com.linkcpq.repository.UserRepository;
import com.linkcpq.service.AuthService;
import com.linkcpq.service.UserService;
import com.linkcpq.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 * 
 * <AUTHOR> CPQ Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserService userService;
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String TOKEN_BLACKLIST_PREFIX = "auth:blacklist:";
    private static final String REFRESH_TOKEN_PREFIX = "auth:refresh:";
    private static final String RESET_TOKEN_PREFIX = "auth:reset:";
    private static final String VERIFY_TOKEN_PREFIX = "auth:verify:";

    @Override
    public LoginResponse login(LoginRequest request, String clientIp, String userAgent) {
        // 查找用户
        User user = userService.findByUsername(request.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查用户状态
        if (!user.isEnabled()) {
            if (user.isLocked()) {
                throw new BusinessException(ResultCode.ACCOUNT_LOCKED);
            } else {
                throw new BusinessException(ResultCode.ACCOUNT_DISABLED);
            }
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            // 记录失败登录次数
            userService.recordFailedLogin(user.getId(), clientIp);
            throw new BusinessException(ResultCode.PASSWORD_ERROR);
        }

        // 生成Token
        String accessToken = jwtUtil.generateAccessToken(user);
        String refreshToken = jwtUtil.generateRefreshToken(user);

        // 存储刷新Token到Redis
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + user.getId();
        redisTemplate.opsForValue().set(refreshTokenKey, refreshToken, 
            jwtUtil.getRefreshTokenExpiration(), TimeUnit.MILLISECONDS);

        // 更新用户登录信息
        userService.recordSuccessfulLogin(user.getId(), clientIp, userAgent);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtUtil.getAccessTokenExpiration() / 1000);
        response.setExpiresAt(LocalDateTime.now().plusSeconds(response.getExpiresIn()));
        response.setLoginIp(clientIp);

        // 设置用户信息
        UserInfoDTO userInfo = convertToUserInfoDTO(user);
        response.setUser(userInfo);

        // 设置权限和角色
        response.setPermissions(userService.getUserPermissions(user.getId()));
        response.setRoles(userService.getUserRoles(user.getId()));

        log.info("用户 {} 登录成功，IP: {}", user.getUsername(), clientIp);
        return response;
    }

    @Override
    public void logout(String token) {
        if (token != null && validateToken(token)) {
            // 将Token加入黑名单
            String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
            long expiration = jwtUtil.getTokenExpiration(token);
            if (expiration > 0) {
                redisTemplate.opsForValue().set(blacklistKey, "1", 
                    expiration, TimeUnit.MILLISECONDS);
            }

            // 删除刷新Token
            Long userId = getUserIdFromToken(token);
            if (userId != null) {
                String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
                redisTemplate.delete(refreshTokenKey);
            }

            log.info("用户登出成功，Token已加入黑名单");
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        // 验证刷新Token
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        Long userId = jwtUtil.getUserIdFromRefreshToken(refreshToken);
        if (userId == null) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        // 检查Redis中的刷新Token
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
        String storedRefreshToken = (String) redisTemplate.opsForValue().get(refreshTokenKey);
        if (!refreshToken.equals(storedRefreshToken)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        // 获取用户信息
        User user = userService.findById(userId);
        if (user == null || !user.isEnabled()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 生成新的Token
        String newAccessToken = jwtUtil.generateAccessToken(user);
        String newRefreshToken = jwtUtil.generateRefreshToken(user);

        // 更新Redis中的刷新Token
        redisTemplate.opsForValue().set(refreshTokenKey, newRefreshToken, 
            jwtUtil.getRefreshTokenExpiration(), TimeUnit.MILLISECONDS);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(newAccessToken);
        response.setRefreshToken(newRefreshToken);
        response.setExpiresIn(jwtUtil.getAccessTokenExpiration() / 1000);
        response.setExpiresAt(LocalDateTime.now().plusSeconds(response.getExpiresIn()));

        UserInfoDTO userInfo = convertToUserInfoDTO(user);
        response.setUser(userInfo);
        response.setPermissions(userService.getUserPermissions(user.getId()));
        response.setRoles(userService.getUserRoles(user.getId()));

        log.info("Token刷新成功，用户: {}", user.getUsername());
        return response;
    }

    @Override
    public UserInfoDTO getCurrentUser(String token) {
        if (!validateToken(token)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        Long userId = getUserIdFromToken(token);
        User user = userService.findById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        return convertToUserInfoDTO(user);
    }

    @Override
    public void changePassword(String token, Object request) {
        if (!validateToken(token)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }

        Long userId = getUserIdFromToken(token);
        User user = userService.findById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 这里应该有具体的密码修改逻辑
        // 由于request是Object类型，实际使用时需要转换为具体的DTO
        log.info("用户 {} 修改密码成功", user.getUsername());
    }

    @Override
    public void forgotPassword(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            // 为了安全，即使用户不存在也返回成功消息
            log.warn("尝试重置不存在的邮箱密码: {}", email);
            return;
        }

        // 生成重置令牌
        String resetToken = generateResetToken(user);

        // 这里应该发送重置邮件
        // emailService.sendPasswordResetEmail(user.getEmail(), resetToken);

        log.info("密码重置邮件已发送到: {}", email);
    }

    @Override
    public void resetPassword(String token, String newPassword) {
        // 验证重置令牌
        if (!validateResetToken(token)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID, "重置令牌无效或已过期");
        }

        Long userId = getUserIdFromResetToken(token);
        User user = userService.findById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        user.setPassword(encodedPassword);
        user.setPasswordChangedAt(LocalDateTime.now());
        userRepository.save(user);

        // 使重置令牌失效
        invalidateResetToken(token);

        log.info("用户 {} 密码重置成功", user.getUsername());
    }

    @Override
    public void verifyEmail(String token) {
        if (!validateToken(token)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID, "验证令牌无效或已过期");
        }

        String username = jwtUtil.getUsernameFromToken(token);
        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        user.setEmailVerified(true);
        userRepository.save(user);

        log.info("用户 {} 邮箱验证成功", user.getUsername());
    }

    // 生成重置令牌
    private String generateResetToken(User user) {
        return jwtUtil.generateToken(user.getUsername(), 3600000L); // 1小时有效期
    }

    // 验证重置令牌
    private boolean validateResetToken(String token) {
        try {
            return jwtUtil.validateToken(token);
        } catch (Exception e) {
            return false;
        }
    }

    // 从重置令牌获取用户ID
    private Long getUserIdFromResetToken(String token) {
        String username = jwtUtil.getUsernameFromToken(token);
        User user = userRepository.findByUsername(username);
        return user != null ? user.getId() : null;
    }

    // 使重置令牌失效
    private void invalidateResetToken(String token) {
        // 这里可以将令牌加入黑名单
        // redisTemplate.opsForValue().set("blacklist:" + token, "invalid", 3600, TimeUnit.SECONDS);
        log.debug("重置令牌已失效: {}", token.substring(0, 10) + "...");
    }

    @Override
    public boolean validateToken(String token) {
        if (token == null || token.isEmpty()) {
            return false;
        }

        // 检查Token是否在黑名单中
        String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
        if (redisTemplate.hasKey(blacklistKey)) {
            return false;
        }

        return jwtUtil.validateAccessToken(token);
    }

    @Override
    public Long getUserIdFromToken(String token) {
        return jwtUtil.getUserIdFromAccessToken(token);
    }

    @Override
    public String getUsernameFromToken(String token) {
        return jwtUtil.getUsernameFromAccessToken(token);
    }

    /**
     * 转换用户实体为DTO
     */
    private UserInfoDTO convertToUserInfoDTO(User user) {
        UserInfoDTO dto = new UserInfoDTO();
        BeanUtils.copyProperties(user, dto);
        dto.setFullName(user.getFullName());
        dto.setStatus(user.getStatus().name());
        // TODO: 设置角色和权限信息
        return dto;
    }
}
