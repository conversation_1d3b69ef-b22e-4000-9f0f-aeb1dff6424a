package com.linkcpq.service.impl;

import com.linkcpq.entity.Customer;
import com.linkcpq.repository.CustomerRepository;
import com.linkcpq.service.CustomerService;
import com.linkcpq.common.result.PageResult;
import com.linkcpq.common.exception.BusinessException;
import com.linkcpq.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户服务实现类
 * <AUTHOR> CPQ Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl implements CustomerService {

    private final CustomerRepository customerRepository;

    @Override
    @Transactional
    public Long createCustomer(Customer customer) {
        log.info("创建客户: {}", customer.getCustomerName());
        
        // 验证客户代码唯一性
        if (StringUtils.hasText(customer.getCustomerCode()) && 
            existsByCustomerCode(customer.getCustomerCode())) {
            throw new BusinessException(ResultCode.CUSTOMER_ALREADY_EXISTS, "客户代码已存在");
        }
        
        // 验证客户名称唯一性
        if (existsByCustomerName(customer.getCustomerName())) {
            throw new BusinessException(ResultCode.CUSTOMER_ALREADY_EXISTS, "客户名称已存在");
        }
        
        // 设置默认值
        if (customer.getIsActive() == null) {
            customer.setIsActive(true);
        }
        if (customer.getIsVip() == null) {
            customer.setIsVip(false);
        }
        if (customer.getAcquisitionDate() == null) {
            customer.setAcquisitionDate(LocalDateTime.now());
        }
        
        Customer savedCustomer = customerRepository.save(customer);
        log.info("客户创建成功，ID: {}", savedCustomer.getId());
        return savedCustomer.getId();
    }

    @Override
    @Transactional
    public void updateCustomer(Long id, Customer customer) {
        log.info("更新客户: {}", id);
        
        Customer existingCustomer = getCustomerById(id);
        
        // 更新字段
        if (StringUtils.hasText(customer.getCustomerName())) {
            existingCustomer.setCustomerName(customer.getCustomerName());
        }
        if (StringUtils.hasText(customer.getCustomerType())) {
            existingCustomer.setCustomerType(customer.getCustomerType());
        }
        if (StringUtils.hasText(customer.getIndustry())) {
            existingCustomer.setIndustry(customer.getIndustry());
        }
        if (StringUtils.hasText(customer.getContactPerson())) {
            existingCustomer.setContactPerson(customer.getContactPerson());
        }
        if (StringUtils.hasText(customer.getContactPhone())) {
            existingCustomer.setContactPhone(customer.getContactPhone());
        }
        if (StringUtils.hasText(customer.getContactEmail())) {
            existingCustomer.setContactEmail(customer.getContactEmail());
        }
        
        customerRepository.save(existingCustomer);
        log.info("客户更新成功: {}", id);
    }

    @Override
    @Transactional
    public void deleteCustomer(Long id) {
        log.info("删除客户: {}", id);
        
        Customer customer = getCustomerById(id);
        customer.setDeleted(true);
        customerRepository.save(customer);
        
        log.info("客户删除成功: {}", id);
    }

    @Override
    public Customer getCustomerById(Long id) {
        return customerRepository.findById(id)
                .filter(customer -> !customer.getDeleted())
                .orElseThrow(() -> new BusinessException(ResultCode.CUSTOMER_NOT_FOUND));
    }

    @Override
    public Customer getCustomerByCode(String customerCode) {
        return customerRepository.findByCustomerCode(customerCode);
    }

    @Override
    public Customer getCustomerByName(String customerName) {
        return customerRepository.findByCustomerName(customerName);
    }

    @Override
    public PageResult<Customer> getCustomers(int page, int pageSize, String keyword) {
        Pageable pageable = PageRequest.of(page - 1, pageSize, Sort.by("createdAt").descending());
        
        Page<Customer> customerPage;
        if (StringUtils.hasText(keyword)) {
            customerPage = customerRepository.findAll(pageable);
        } else {
            customerPage = customerRepository.findAll(pageable);
        }
        
        return PageResult.success(customerPage.getContent(), customerPage.getTotalElements(), page, pageSize);
    }

    @Override
    public List<Customer> getCustomersBySalesRep(Long salesRepId) {
        return customerRepository.findBySalesRepIdAndDeletedFalse(salesRepId);
    }

    @Override
    public List<Customer> getCustomersByType(String customerType) {
        return customerRepository.findByCustomerTypeAndDeletedFalse(customerType);
    }

    @Override
    public List<Customer> getVipCustomers() {
        return customerRepository.findByIsVipTrueAndDeletedFalse();
    }

    @Override
    public List<Customer> getActiveCustomers() {
        return customerRepository.findByIsActiveTrueAndDeletedFalse();
    }

    @Override
    public List<Customer> getCustomersByRegion(String region) {
        return customerRepository.findByRegionAndDeletedFalse(region);
    }

    @Override
    @Transactional
    public void activateCustomer(Long id) {
        Customer customer = getCustomerById(id);
        customer.setIsActive(true);
        customerRepository.save(customer);
        log.info("客户激活成功: {}", id);
    }

    @Override
    @Transactional
    public void deactivateCustomer(Long id) {
        Customer customer = getCustomerById(id);
        customer.setIsActive(false);
        customerRepository.save(customer);
        log.info("客户停用成功: {}", id);
    }

    @Override
    @Transactional
    public void setVipCustomer(Long id, String vipLevel) {
        Customer customer = getCustomerById(id);
        customer.setIsVip(true);
        customer.setVipLevel(vipLevel);
        customerRepository.save(customer);
        log.info("设置VIP客户成功: {}, 等级: {}", id, vipLevel);
    }

    @Override
    @Transactional
    public void cancelVipCustomer(Long id) {
        Customer customer = getCustomerById(id);
        customer.setIsVip(false);
        customer.setVipLevel(null);
        customerRepository.save(customer);
        log.info("取消VIP客户成功: {}", id);
    }

    @Override
    @Transactional
    public void updateCreditRating(Long id, String creditRating) {
        Customer customer = getCustomerById(id);
        customer.setCreditRating(creditRating);
        customerRepository.save(customer);
        log.info("更新客户信用等级成功: {}, 等级: {}", id, creditRating);
    }

    @Override
    public Long getTotalCustomerCount() {
        return customerRepository.countActiveCustomers();
    }

    @Override
    public Long getVipCustomerCount() {
        return customerRepository.countVipCustomers();
    }

    @Override
    public Long getActiveCustomerCount() {
        return customerRepository.countActiveCustomers();
    }

    @Override
    public Long getCustomerCountByIndustry(String industry) {
        return (long) customerRepository.findByIndustryAndDeletedFalse(industry).size();
    }

    @Override
    public boolean existsByCustomerCode(String customerCode) {
        return customerRepository.findByCustomerCodeAndDeletedFalse(customerCode).isPresent();
    }

    @Override
    public boolean existsByCustomerName(String customerName) {
        return customerRepository.findByCustomerNameAndDeletedFalse(customerName).isPresent();
    }
}
