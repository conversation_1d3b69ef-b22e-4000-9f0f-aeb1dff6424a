package com.linkcpq.service;

import com.linkcpq.entity.Customer;
import com.linkcpq.common.result.PageResult;

import java.util.List;

/**
 * 客户服务接口
 * <AUTHOR> CPQ Team
 */
public interface CustomerService {

    /**
     * 创建客户
     */
    Long createCustomer(Customer customer);

    /**
     * 更新客户信息
     */
    void updateCustomer(Long id, Customer customer);

    /**
     * 删除客户
     */
    void deleteCustomer(Long id);

    /**
     * 根据ID获取客户详情
     */
    Customer getCustomerById(Long id);

    /**
     * 根据客户代码获取客户
     */
    Customer getCustomerByCode(String customerCode);

    /**
     * 根据客户名称获取客户
     */
    Customer getCustomerByName(String customerName);

    /**
     * 分页查询客户列表
     */
    PageResult<Customer> getCustomers(int page, int pageSize, String keyword);

    /**
     * 根据销售代表获取客户列表
     */
    List<Customer> getCustomersBySalesRep(Long salesRepId);

    /**
     * 根据客户类型获取客户列表
     */
    List<Customer> getCustomersByType(String customerType);

    /**
     * 获取VIP客户列表
     */
    List<Customer> getVipCustomers();

    /**
     * 获取活跃客户列表
     */
    List<Customer> getActiveCustomers();

    /**
     * 根据地区获取客户列表
     */
    List<Customer> getCustomersByRegion(String region);

    /**
     * 激活客户
     */
    void activateCustomer(Long id);

    /**
     * 停用客户
     */
    void deactivateCustomer(Long id);

    /**
     * 设置VIP客户
     */
    void setVipCustomer(Long id, String vipLevel);

    /**
     * 取消VIP客户
     */
    void cancelVipCustomer(Long id);

    /**
     * 更新客户信用等级
     */
    void updateCreditRating(Long id, String creditRating);

    /**
     * 统计客户总数
     */
    Long getTotalCustomerCount();

    /**
     * 统计VIP客户数量
     */
    Long getVipCustomerCount();

    /**
     * 统计活跃客户数量
     */
    Long getActiveCustomerCount();

    /**
     * 根据行业统计客户数量
     */
    Long getCustomerCountByIndustry(String industry);

    /**
     * 检查客户代码是否存在
     */
    boolean existsByCustomerCode(String customerCode);

    /**
     * 检查客户名称是否存在
     */
    boolean existsByCustomerName(String customerName);
}
