package com.linkcpq.service;

import com.linkcpq.dto.configuration.*;
import com.linkcpq.common.result.PageResult;

/**
 * 配置管理服务接口
 * <AUTHOR> CPQ Team
 */
public interface ConfigurationService {

    /**
     * 创建配置
     */
    Long createConfiguration(ConfigurationCreateRequest request);

    /**
     * 更新配置
     */
    void updateConfiguration(Long id, ConfigurationUpdateRequest request);

    /**
     * 删除配置
     */
    void deleteConfiguration(Long id);

    /**
     * 根据ID获取配置详情
     */
    ConfigurationResponse getConfigurationById(Long id);

    /**
     * 分页查询配置列表
     */
    PageResult<ConfigurationResponse> getConfigurations(ConfigurationQueryRequest request);

    /**
     * 验证配置
     */
    ConfigurationResponse.ValidationResult validateConfiguration(Long id);

    /**
     * 复制配置
     */
    Long copyConfiguration(Long id, String newName);

    /**
     * 归档配置
     */
    void archiveConfiguration(Long id);

    /**
     * 激活配置
     */
    void activateConfiguration(Long id);

    /**
     * 根据产品ID获取默认配置
     */
    ConfigurationResponse getDefaultConfigurationByProductId(Long productId);

    /**
     * 根据客户ID和产品ID获取配置
     */
    ConfigurationResponse getConfigurationByCustomerAndProduct(Long customerId, Long productId);

    /**
     * 批量验证配置
     */
    void batchValidateConfigurations(java.util.List<Long> configurationIds);

    /**
     * 导出配置
     */
    String exportConfiguration(Long id, String format);

    /**
     * 导入配置
     */
    Long importConfiguration(String configurationData, String format);

    /**
     * 获取配置历史版本
     */
    java.util.List<ConfigurationResponse> getConfigurationVersions(Long id);

    /**
     * 恢复配置版本
     */
    void restoreConfigurationVersion(Long id, String version);
}
