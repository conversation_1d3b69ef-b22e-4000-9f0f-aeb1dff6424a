[{"/Users/<USER>/Desktop/Link_CPQ/src/index.tsx": "1", "/Users/<USER>/Desktop/Link_CPQ/src/App.tsx": "2", "/Users/<USER>/Desktop/Link_CPQ/src/store/index.ts": "3", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/configurationSlice.ts": "4", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/authSlice.ts": "5", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/appSlice.ts": "6", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts": "7", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/productSlice.ts": "8", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/pricingSlice.ts": "9", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/bundleSlice.ts": "10", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts": "11", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/customerSlice.ts": "12", "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductListPage.tsx": "13", "/Users/<USER>/Desktop/Link_CPQ/src/pages/configurator/ConfiguratorPage.tsx": "14", "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductDetailPage.tsx": "15", "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteListPage.tsx": "16", "/Users/<USER>/Desktop/Link_CPQ/src/pages/pricing/PricingPage.tsx": "17", "/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx": "18", "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteDetailPage.tsx": "19", "/Users/<USER>/Desktop/Link_CPQ/src/pages/analytics/AnalyticsPage.tsx": "20", "/Users/<USER>/Desktop/Link_CPQ/src/pages/users/UserManagementPage.tsx": "21", "/Users/<USER>/Desktop/Link_CPQ/src/pages/bundles/BundleListPage.tsx": "22", "/Users/<USER>/Desktop/Link_CPQ/src/pages/integration/IntegrationPage.tsx": "23", "/Users/<USER>/Desktop/Link_CPQ/src/pages/auth/LoginPage.tsx": "24", "/Users/<USER>/Desktop/Link_CPQ/src/pages/customer-portal/CustomerPortalPage.tsx": "25", "/Users/<USER>/Desktop/Link_CPQ/src/components/auth/ProtectedRoute.tsx": "26", "/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx": "27", "/Users/<USER>/Desktop/Link_CPQ/src/services/api.ts": "28", "/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts": "29", "/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx": "30", "/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftConfigurations.ts": "31", "/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftPricingRules.ts": "32", "/Users/<USER>/Desktop/Link_CPQ/src/components/SoftwareQuoteConfigurator.tsx": "33", "/Users/<USER>/Desktop/Link_CPQ/src/data/softwareQuoteData.ts": "34", "/Users/<USER>/Desktop/Link_CPQ/src/components/TeslaStyleConfigurator.tsx": "35", "/Users/<USER>/Desktop/Link_CPQ/src/data/industryRoleConfig.ts": "36"}, {"size": 737, "mtime": 1756710889520, "results": "37", "hashOfConfig": "38"}, {"size": 4352, "mtime": 1756710942524, "results": "39", "hashOfConfig": "38"}, {"size": 1392, "mtime": 1756710729169, "results": "40", "hashOfConfig": "38"}, {"size": 8013, "mtime": 1756710809372, "results": "41", "hashOfConfig": "38"}, {"size": 3043, "mtime": 1756711926739, "results": "42", "hashOfConfig": "38"}, {"size": 1680, "mtime": 1756710740234, "results": "43", "hashOfConfig": "38"}, {"size": 1426, "mtime": 1756773512873, "results": "44", "hashOfConfig": "38"}, {"size": 7204, "mtime": 1756712562295, "results": "45", "hashOfConfig": "38"}, {"size": 2231, "mtime": 1756710843637, "results": "46", "hashOfConfig": "38"}, {"size": 4367, "mtime": 1756710828742, "results": "47", "hashOfConfig": "38"}, {"size": 2369, "mtime": 1756773549424, "results": "48", "hashOfConfig": "38"}, {"size": 1600, "mtime": 1756710868561, "results": "49", "hashOfConfig": "38"}, {"size": 15848, "mtime": 1756712686665, "results": "50", "hashOfConfig": "38"}, {"size": 20083, "mtime": 1756712542993, "results": "51", "hashOfConfig": "38"}, {"size": 14706, "mtime": 1756712675458, "results": "52", "hashOfConfig": "38"}, {"size": 11394, "mtime": 1756711539322, "results": "53", "hashOfConfig": "38"}, {"size": 20495, "mtime": 1756732043374, "results": "54", "hashOfConfig": "38"}, {"size": 14346, "mtime": 1756789360641, "results": "55", "hashOfConfig": "38"}, {"size": 15974, "mtime": 1756712711336, "results": "56", "hashOfConfig": "38"}, {"size": 12235, "mtime": 1756712452761, "results": "57", "hashOfConfig": "38"}, {"size": 13398, "mtime": 1756711727782, "results": "58", "hashOfConfig": "38"}, {"size": 20379, "mtime": 1756711319330, "results": "59", "hashOfConfig": "38"}, {"size": 16898, "mtime": 1756712650697, "results": "60", "hashOfConfig": "38"}, {"size": 4587, "mtime": 1756711012632, "results": "61", "hashOfConfig": "38"}, {"size": 21504, "mtime": 1756712614664, "results": "62", "hashOfConfig": "38"}, {"size": 805, "mtime": 1756710991231, "results": "63", "hashOfConfig": "38"}, {"size": 5960, "mtime": 1756785483030, "results": "64", "hashOfConfig": "38"}, {"size": 11935, "mtime": 1756777973019, "results": "65", "hashOfConfig": "38"}, {"size": 16122, "mtime": 1756787922420, "results": "66", "hashOfConfig": "38"}, {"size": 14971, "mtime": 1756785575901, "results": "67", "hashOfConfig": "38"}, {"size": 9308, "mtime": 1756784834255, "results": "68", "hashOfConfig": "38"}, {"size": 6515, "mtime": 1756784790848, "results": "69", "hashOfConfig": "38"}, {"size": 16156, "mtime": 1756788175304, "results": "70", "hashOfConfig": "38"}, {"size": 13546, "mtime": 1756787987597, "results": "71", "hashOfConfig": "38"}, {"size": 18064, "mtime": 1756789343854, "results": "72", "hashOfConfig": "38"}, {"size": 11420, "mtime": 1756789096163, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4u5c16", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Link_CPQ/src/index.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/App.tsx", ["182"], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/index.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/configurationSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/appSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/productSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/pricingSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/bundleSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/customerSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductListPage.tsx", ["183", "184"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/configurator/ConfiguratorPage.tsx", ["185", "186", "187", "188", "189", "190", "191"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductDetailPage.tsx", ["192", "193"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteListPage.tsx", ["194", "195"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/pricing/PricingPage.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx", ["196"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteDetailPage.tsx", ["197", "198", "199", "200"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/analytics/AnalyticsPage.tsx", ["201", "202", "203"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/users/UserManagementPage.tsx", ["204", "205", "206"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/bundles/BundleListPage.tsx", ["207"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/integration/IntegrationPage.tsx", ["208", "209", "210", "211", "212", "213", "214"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/auth/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/customer-portal/CustomerPortalPage.tsx", ["215", "216", "217", "218", "219", "220", "221", "222", "223", "224"], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/services/api.ts", ["225", "226"], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftConfigurations.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftPricingRules.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/SoftwareQuoteConfigurator.tsx", ["227", "228"], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/softwareQuoteData.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/TeslaStyleConfigurator.tsx", ["229", "230", "231", "232"], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/industryRoleConfig.ts", [], [], {"ruleId": "233", "severity": 1, "message": "234", "line": 26, "column": 9, "nodeType": "235", "messageId": "236", "endLine": 26, "endColumn": 16}, {"ruleId": "233", "severity": 1, "message": "237", "line": 24, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 24, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "238", "line": 43, "column": 11, "nodeType": "235", "messageId": "236", "endLine": 43, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "239", "line": 1, "column": 27, "nodeType": "235", "messageId": "236", "endLine": 1, "endColumn": 36}, {"ruleId": "233", "severity": 1, "message": "240", "line": 13, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 13, "endColumn": 10}, {"ruleId": "233", "severity": 1, "message": "241", "line": 35, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 35, "endColumn": 28}, {"ruleId": "233", "severity": 1, "message": "242", "line": 42, "column": 26, "nodeType": "235", "messageId": "236", "endLine": 42, "endColumn": 40}, {"ruleId": "233", "severity": 1, "message": "243", "line": 47, "column": 9, "nodeType": "235", "messageId": "236", "endLine": 47, "endColumn": 15}, {"ruleId": "233", "severity": 1, "message": "244", "line": 138, "column": 11, "nodeType": "235", "messageId": "236", "endLine": 138, "endColumn": 20}, {"ruleId": "233", "severity": 1, "message": "245", "line": 139, "column": 9, "nodeType": "235", "messageId": "236", "endLine": 139, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "246", "line": 43, "column": 27, "nodeType": "235", "messageId": "236", "endLine": 43, "endColumn": 34}, {"ruleId": "233", "severity": 1, "message": "247", "line": 232, "column": 13, "nodeType": "235", "messageId": "236", "endLine": 232, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "248", "line": 18, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 18, "endColumn": 11}, {"ruleId": "233", "severity": 1, "message": "249", "line": 43, "column": 11, "nodeType": "235", "messageId": "236", "endLine": 43, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "250", "line": 13, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 13, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "239", "line": 1, "column": 17, "nodeType": "235", "messageId": "236", "endLine": 1, "endColumn": 26}, {"ruleId": "233", "severity": 1, "message": "251", "line": 20, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 20, "endColumn": 8}, {"ruleId": "233", "severity": 1, "message": "252", "line": 30, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 30, "endColumn": 22}, {"ruleId": "233", "severity": 1, "message": "247", "line": 208, "column": 13, "nodeType": "235", "messageId": "236", "endLine": 208, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "253", "line": 33, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 33, "endColumn": 11}, {"ruleId": "233", "severity": 1, "message": "254", "line": 48, "column": 11, "nodeType": "235", "messageId": "236", "endLine": 48, "endColumn": 24}, {"ruleId": "233", "severity": 1, "message": "246", "line": 48, "column": 26, "nodeType": "235", "messageId": "236", "endLine": 48, "endColumn": 33}, {"ruleId": "233", "severity": 1, "message": "255", "line": 18, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 18, "endColumn": 9}, {"ruleId": "233", "severity": 1, "message": "256", "line": 38, "column": 10, "nodeType": "235", "messageId": "236", "endLine": 38, "endColumn": 22}, {"ruleId": "233", "severity": 1, "message": "247", "line": 254, "column": 13, "nodeType": "235", "messageId": "236", "endLine": 254, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "257", "line": 44, "column": 11, "nodeType": "235", "messageId": "236", "endLine": 44, "endColumn": 18}, {"ruleId": "233", "severity": 1, "message": "258", "line": 26, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 26, "endColumn": 18}, {"ruleId": "233", "severity": 1, "message": "252", "line": 27, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 27, "endColumn": 22}, {"ruleId": "233", "severity": 1, "message": "241", "line": 28, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 28, "endColumn": 28}, {"ruleId": "233", "severity": 1, "message": "259", "line": 32, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 32, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "260", "line": 34, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 34, "endColumn": 22}, {"ruleId": "233", "severity": 1, "message": "261", "line": 44, "column": 10, "nodeType": "235", "messageId": "236", "endLine": 44, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "247", "line": 316, "column": 13, "nodeType": "235", "messageId": "236", "endLine": 316, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "239", "line": 1, "column": 27, "nodeType": "235", "messageId": "236", "endLine": 1, "endColumn": 36}, {"ruleId": "233", "severity": 1, "message": "248", "line": 18, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 18, "endColumn": 11}, {"ruleId": "233", "severity": 1, "message": "262", "line": 30, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 30, "endColumn": 18}, {"ruleId": "233", "severity": 1, "message": "263", "line": 31, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 31, "endColumn": 15}, {"ruleId": "233", "severity": 1, "message": "237", "line": 32, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 32, "endColumn": 17}, {"ruleId": "233", "severity": 1, "message": "264", "line": 39, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 39, "endColumn": 15}, {"ruleId": "233", "severity": 1, "message": "258", "line": 42, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 42, "endColumn": 18}, {"ruleId": "233", "severity": 1, "message": "265", "line": 43, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 43, "endColumn": 15}, {"ruleId": "233", "severity": 1, "message": "266", "line": 73, "column": 10, "nodeType": "235", "messageId": "236", "endLine": 73, "endColumn": 25}, {"ruleId": "233", "severity": 1, "message": "267", "line": 216, "column": 9, "nodeType": "235", "messageId": "236", "endLine": 216, "endColumn": 32}, {"ruleId": "233", "severity": 1, "message": "268", "line": 10, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 10, "endColumn": 30}, {"ruleId": "233", "severity": 1, "message": "269", "line": 232, "column": 7, "nodeType": "235", "messageId": "236", "endLine": 232, "endColumn": 20}, {"ruleId": "233", "severity": 1, "message": "251", "line": 15, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 15, "endColumn": 8}, {"ruleId": "270", "severity": 1, "message": "271", "line": 83, "column": 6, "nodeType": "272", "endLine": 83, "endColumn": 31, "suggestions": "273"}, {"ruleId": "233", "severity": 1, "message": "274", "line": 10, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 10, "endColumn": 8}, {"ruleId": "233", "severity": 1, "message": "275", "line": 11, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 11, "endColumn": 11}, {"ruleId": "233", "severity": 1, "message": "240", "line": 18, "column": 3, "nodeType": "235", "messageId": "236", "endLine": 18, "endColumn": 10}, {"ruleId": "233", "severity": 1, "message": "276", "line": 33, "column": 70, "nodeType": "235", "messageId": "236", "endLine": 33, "endColumn": 79}, "@typescript-eslint/no-unused-vars", "'Content' is assigned a value but never used.", "Identifier", "unusedVar", "'SearchOutlined' is defined but never used.", "'products' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Tooltip' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'useAppSelector' is defined but never used.", "'Option' is assigned a value but never used.", "'productId' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'values' is assigned a value but never used.", "'Progress' is defined but never used.", "'quotes' is assigned a value but never used.", "'RocketOutlined' is defined but never used.", "'Alert' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'BarChart' is defined but never used.", "'dashboardData' is assigned a value but never used.", "'Switch' is defined but never used.", "'selectedUser' is assigned a value but never used.", "'bundles' is assigned a value but never used.", "'SettingOutlined' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'PauseCircleOutlined' is defined but never used.", "'modalMode' is assigned a value but never used.", "'MessageOutlined' is defined but never used.", "'StarOutlined' is defined but never used.", "'LikeOutlined' is defined but never used.", "'GiftOutlined' is defined but never used.", "'selectedProduct' is assigned a value but never used.", "'handleSaveConfiguration' is assigned a value but never used.", "'createMockPaginatedResponse' is defined but never used.", "'isDevelopment' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'configuration.versionId'. Either include it or remove the dependency array.", "ArrayExpression", ["277"], "'Radio' is defined but never used.", "'Checkbox' is defined but never used.", "'PainPoint' is defined but never used.", {"desc": "278", "fix": "279"}, "Update the dependencies array to be: [configuration.productId, configuration.versionId]", {"range": "280", "text": "281"}, [2187, 2212], "[configuration.productId, configuration.versionId]"]