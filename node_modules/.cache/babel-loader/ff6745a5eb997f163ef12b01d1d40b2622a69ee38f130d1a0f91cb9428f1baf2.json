{"ast": null, "code": "var coreJsData = require('./_coreJsData');\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = function () {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? 'Symbol(src)_1.' + uid : '';\n}();\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && maskSrcKey in func;\n}\nmodule.exports = isMasked;", "map": {"version": 3, "names": ["coreJsData", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uid", "exec", "keys", "IE_PROTO", "isMasked", "func", "module", "exports"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/lodash/_isMasked.js"], "sourcesContent": ["var coreJsData = require('./_coreJsData');\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nmodule.exports = isMasked;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIC,UAAU,GAAI,YAAW;EAC3B,IAAIC,GAAG,GAAG,QAAQ,CAACC,IAAI,CAACJ,UAAU,IAAIA,UAAU,CAACK,IAAI,IAAIL,UAAU,CAACK,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC;EACxF,OAAOH,GAAG,GAAI,gBAAgB,GAAGA,GAAG,GAAI,EAAE;AAC5C,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAO,CAAC,CAACN,UAAU,IAAKA,UAAU,IAAIM,IAAK;AAC7C;AAEAC,MAAM,CAACC,OAAO,GAAGH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}