{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/components/SoftwareQuoteConfigurator.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Typography, Space, Divider, Form, Select, InputNumber, Checkbox, Table, Statistic, Steps, Radio, Tag, Tooltip } from 'antd';\nimport { CalculatorOutlined, FileTextOutlined, CheckCircleOutlined, InfoCircleOutlined, DownloadOutlined, SendOutlined } from '@ant-design/icons';\nimport { allSoftwareProducts, calculateSoftwareQuote } from '@/data/softwareQuoteData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  Step\n} = Steps;\nconst SoftwareQuoteConfigurator = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [configuration, setConfiguration] = useState({\n    productId: '',\n    versionId: '',\n    userCount: 1,\n    selectedModules: [],\n    selectedServices: [],\n    deploymentId: '',\n    supportId: '',\n    servicePeriod: 1\n  });\n  const [quoteResult, setQuoteResult] = useState(null);\n  useEffect(() => {\n    if (configuration.productId) {\n      const product = allSoftwareProducts.find(p => p.id === configuration.productId);\n      setSelectedProduct(product || null);\n\n      // 设置默认值\n      if (product && !configuration.versionId) {\n        const recommendedVersion = product.versions.find(v => v.recommended) || product.versions[0];\n        const defaultDeployment = product.deploymentOptions[0];\n        const defaultSupport = product.supportLevels[0];\n        const requiredModules = product.modules.filter(m => m.required).map(m => m.id);\n        setConfiguration(prev => ({\n          ...prev,\n          versionId: recommendedVersion.id,\n          deploymentId: defaultDeployment.id,\n          supportId: defaultSupport.id,\n          selectedModules: requiredModules\n        }));\n      }\n    }\n  }, [configuration.productId]);\n  useEffect(() => {\n    if (selectedProduct && configuration.versionId && configuration.deploymentId && configuration.supportId) {\n      try {\n        const result = calculateSoftwareQuote(configuration.productId, configuration.versionId, configuration.userCount, configuration.selectedModules, configuration.selectedServices, configuration.deploymentId, configuration.supportId, configuration.servicePeriod);\n        setQuoteResult(result);\n      } catch (error) {\n        console.error('报价计算错误:', error);\n      }\n    }\n  }, [selectedProduct, configuration]);\n  const handleNext = () => {\n    setCurrentStep(prev => Math.min(prev + 1, 3));\n  };\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 0));\n  };\n  const handleProductSelect = productId => {\n    setConfiguration(prev => ({\n      ...prev,\n      productId,\n      versionId: '',\n      selectedModules: [],\n      selectedServices: [],\n      deploymentId: '',\n      supportId: ''\n    }));\n  };\n  const handleModuleChange = moduleIds => {\n    if (!selectedProduct) return;\n\n    // 确保必需模块始终被选中\n    const requiredModules = selectedProduct.modules.filter(m => m.required).map(m => m.id);\n    const allSelected = Array.from(new Set([...requiredModules, ...moduleIds]));\n    setConfiguration(prev => ({\n      ...prev,\n      selectedModules: allSelected\n    }));\n  };\n  const renderProductSelection = () => /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u9009\\u62E9\\u4EA7\\u54C1\",\n    size: \"small\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: allSoftwareProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          size: \"small\",\n          className: configuration.productId === product.id ? 'selected-card' : '',\n          onClick: () => handleProductSelect(product.id),\n          style: {\n            border: configuration.productId === product.id ? '2px solid #1890ff' : '1px solid #d9d9d9'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            ellipsis: {\n              rows: 2\n            },\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: product.targetCustomer.map(target => /*#__PURE__*/_jsxDEV(Tag, {\n              children: target\n            }, target, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u8D77\\u4EF7: \\xA5\", product.basePrice, \"/\\u7528\\u6237/\\u5E74\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n  const renderVersionSelection = () => {\n    if (!selectedProduct) return null;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9009\\u62E9\\u7248\\u672C\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n        value: configuration.versionId,\n        onChange: e => setConfiguration(prev => ({\n          ...prev,\n          versionId: e.target.value\n        })),\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: selectedProduct.versions.map(version => /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              size: \"small\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(Radio, {\n                value: version.id,\n                children: /*#__PURE__*/_jsxDEV(Title, {\n                  level: 5,\n                  children: [version.name, version.recommended && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"gold\",\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: \"\\u63A8\\u8350\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 47\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: version.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u6700\\u5927\\u7528\\u6237\\u6570: \", version.maxUsers]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: [\"\\u4EF7\\u683C: \\xA5\", (selectedProduct.basePrice * version.priceMultiplier).toFixed(0), \"/\\u7528\\u6237/\\u5E74\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)\n          }, version.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this);\n  };\n  const renderConfiguration = () => {\n    var _selectedProduct$vers;\n    if (!selectedProduct) return null;\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u7840\\u914D\\u7F6E\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7528\\u6237\\u6570\\u91CF\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: ((_selectedProduct$vers = selectedProduct.versions.find(v => v.id === configuration.versionId)) === null || _selectedProduct$vers === void 0 ? void 0 : _selectedProduct$vers.maxUsers) || 999,\n                value: configuration.userCount,\n                onChange: value => setConfiguration(prev => ({\n                  ...prev,\n                  userCount: value || 1\n                })),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u671F\\u9650\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: configuration.servicePeriod,\n                onChange: value => setConfiguration(prev => ({\n                  ...prev,\n                  servicePeriod: value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"1\\u5E74\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 2,\n                  children: \"2\\u5E74\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 3,\n                  children: \"3\\u5E74\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 5,\n                  children: \"5\\u5E74\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u529F\\u80FD\\u6A21\\u5757\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: configuration.selectedModules,\n            onChange: handleModuleChange,\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: selectedProduct.modules.map(module => /*#__PURE__*/_jsxDEV(Col, {\n                span: 24,\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: module.id,\n                  disabled: module.required,\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: module.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this), module.required && /*#__PURE__*/_jsxDEV(Tag, {\n                      size: \"small\",\n                      color: \"red\",\n                      children: \"\\u5FC5\\u9700\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: module.description,\n                      children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    marginLeft: 24\n                  },\n                  children: module.price > 0 && (module.priceType === 'per_user' ? `¥${module.price}/用户/年` : `¥${module.price}/年`)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, module.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u90E8\\u7F72\\u65B9\\u5F0F\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: configuration.deploymentId,\n            onChange: e => setConfiguration(prev => ({\n              ...prev,\n              deploymentId: e.target.value\n            })),\n            children: selectedProduct.deploymentOptions.map(option => /*#__PURE__*/_jsxDEV(Radio, {\n              value: option.id,\n              style: {\n                display: 'block',\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: option.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: option.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this), option.priceImpact > 0 && /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: [\"+\\xA5\", option.priceImpact, \"/\\u5E74\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)\n            }, option.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6280\\u672F\\u652F\\u6301\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: configuration.supportId,\n            onChange: e => setConfiguration(prev => ({\n              ...prev,\n              supportId: e.target.value\n            })),\n            children: selectedProduct.supportLevels.map(level => /*#__PURE__*/_jsxDEV(Radio, {\n              value: level.id,\n              style: {\n                display: 'block',\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: level.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: level.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u54CD\\u5E94\\u65F6\\u95F4: \", level.responseTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this), level.price > 0 && /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: [\"+\\xA5\", level.price, \"/\\u5E74\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)\n            }, level.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5B9E\\u65BD\\u670D\\u52A1\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: configuration.selectedServices,\n            onChange: serviceIds => setConfiguration(prev => ({\n              ...prev,\n              selectedServices: serviceIds\n            })),\n            children: selectedProduct.services.map(service => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: service.id,\n              style: {\n                display: 'block',\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: service.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: service.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5468\\u671F: \", service.duration]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: [\"\\xA5\", service.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)\n            }, service.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this);\n  };\n  const renderQuoteResult = () => {\n    if (!quoteResult) return null;\n    const columns = [{\n      title: '项目',\n      dataIndex: 'item',\n      key: 'item'\n    }, {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity'\n    }, {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit'\n    }, {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      render: price => `¥${price.toLocaleString()}`\n    }, {\n      title: '期限',\n      dataIndex: 'period',\n      key: 'period',\n      render: period => `${period}年`\n    }, {\n      title: '金额',\n      dataIndex: 'amount',\n      key: 'amount',\n      render: amount => `¥${amount.toLocaleString()}`\n    }];\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u62A5\\u4EF7\\u7ED3\\u679C\",\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8F6F\\u4EF6\\u8D39\\u7528\",\n            value: quoteResult.baseAmount,\n            precision: 0,\n            prefix: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6A21\\u5757\\u8D39\\u7528\",\n            value: quoteResult.moduleAmount,\n            precision: 0,\n            prefix: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u670D\\u52A1\\u8D39\\u7528\",\n            value: quoteResult.serviceAmount + quoteResult.deploymentAmount + quoteResult.supportAmount,\n            precision: 0,\n            prefix: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA1\",\n            value: quoteResult.totalAmount,\n            precision: 0,\n            prefix: \"\\xA5\",\n            valueStyle: {\n              color: '#cf1322',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: quoteResult.breakdown,\n        pagination: false,\n        size: \"small\",\n        rowKey: \"item\",\n        summary: () => /*#__PURE__*/_jsxDEV(Table.Summary.Row, {\n          children: [/*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n            index: 0,\n            colSpan: 5,\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u603B\\u8BA1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n            index: 1,\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: [\"\\xA5\", quoteResult.totalAmount.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 40\n          }, this),\n          children: \"\\u751F\\u6210\\u62A5\\u4EF7\\u5355\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FAPDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 25\n          }, this),\n          children: \"\\u53D1\\u9001\\u5BA2\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this);\n  };\n  const steps = [{\n    title: '选择产品',\n    content: renderProductSelection()\n  }, {\n    title: '选择版本',\n    content: renderVersionSelection()\n  }, {\n    title: '配置选项',\n    content: renderConfiguration()\n  }, {\n    title: '生成报价',\n    content: renderQuoteResult()\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(CalculatorOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u91D1\\u8776\\u8F6F\\u4EF6\\u62A5\\u4EF7\\u914D\\u7F6E\\u5668\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Steps, {\n        current: currentStep,\n        style: {\n          marginBottom: 24\n        },\n        children: steps.map(item => /*#__PURE__*/_jsxDEV(Step, {\n          title: item.title\n        }, item.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          minHeight: '500px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handlePrev,\n            children: \"\\u4E0A\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), currentStep < steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleNext,\n            disabled: currentStep === 0 && !configuration.productId || currentStep === 1 && !configuration.versionId,\n            children: \"\\u4E0B\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), currentStep === steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 44\n            }, this),\n            children: \"\\u786E\\u8BA4\\u62A5\\u4EF7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 462,\n    columnNumber: 5\n  }, this);\n};\n_s(SoftwareQuoteConfigurator, \"JoVKUdUxtgIdSo378WzcVbqghBA=\");\n_c = SoftwareQuoteConfigurator;\nexport default SoftwareQuoteConfigurator;\nvar _c;\n$RefreshReg$(_c, \"SoftwareQuoteConfigurator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Typography", "Space", "Divider", "Form", "Select", "InputNumber", "Checkbox", "Table", "Statistic", "Steps", "Radio", "Tag", "<PERSON><PERSON><PERSON>", "CalculatorOutlined", "FileTextOutlined", "CheckCircleOutlined", "InfoCircleOutlined", "DownloadOutlined", "SendOutlined", "allSoftwareProducts", "calculateSoftwareQuote", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Option", "Step", "SoftwareQuoteConfigurator", "_s", "currentStep", "setCurrentStep", "selectedProduct", "setSelectedProduct", "configuration", "setConfiguration", "productId", "versionId", "userCount", "selectedModules", "selectedServices", "deploymentId", "supportId", "servicePeriod", "quoteResult", "setQuoteResult", "product", "find", "p", "id", "recommendedVersion", "versions", "v", "recommended", "defaultDeployment", "deploymentOptions", "defaultSupport", "supportLevels", "requiredModules", "modules", "filter", "m", "required", "map", "prev", "result", "error", "console", "handleNext", "Math", "min", "handlePrev", "max", "handleProductSelect", "handleModuleChange", "moduleIds", "allSelected", "Array", "from", "Set", "renderProductSelection", "title", "size", "children", "gutter", "span", "hoverable", "className", "onClick", "style", "border", "level", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "rows", "description", "wrap", "targetCustomer", "target", "strong", "basePrice", "renderVersionSelection", "Group", "value", "onChange", "e", "version", "marginBottom", "color", "marginLeft", "type", "maxUsers", "priceMultiplier", "toFixed", "renderConfiguration", "_selectedProduct$vers", "layout", "<PERSON><PERSON>", "label", "width", "module", "disabled", "price", "priceType", "option", "display", "direction", "priceImpact", "responseTime", "serviceIds", "services", "service", "duration", "renderQuoteResult", "columns", "dataIndex", "key", "render", "toLocaleString", "period", "amount", "baseAmount", "precision", "prefix", "moduleAmount", "serviceAmount", "deploymentAmount", "supportAmount", "totalAmount", "valueStyle", "fontWeight", "dataSource", "breakdown", "pagination", "<PERSON><PERSON><PERSON>", "summary", "Summary", "Cell", "index", "colSpan", "icon", "steps", "content", "padding", "current", "item", "minHeight", "textAlign", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/components/SoftwareQuoteConfigurator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  Form,\n  Select,\n  InputNumber,\n  Checkbox,\n  Table,\n  Alert,\n  Statistic,\n  Steps,\n  Radio,\n  Tag,\n  Tooltip,\n} from 'antd';\nimport {\n  CalculatorOutlined,\n  FileTextOutlined,\n  CheckCircleOutlined,\n  InfoCircleOutlined,\n  DownloadOutlined,\n  SendOutlined,\n} from '@ant-design/icons';\nimport { allSoftwareProducts, calculateSoftwareQuote, SoftwareProduct } from '@/data/softwareQuoteData';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { Step } = Steps;\n\ninterface QuoteConfiguration {\n  productId: string;\n  versionId: string;\n  userCount: number;\n  selectedModules: string[];\n  selectedServices: string[];\n  deploymentId: string;\n  supportId: string;\n  servicePeriod: number;\n}\n\nconst SoftwareQuoteConfigurator: React.FC = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [selectedProduct, setSelectedProduct] = useState<SoftwareProduct | null>(null);\n  const [configuration, setConfiguration] = useState<QuoteConfiguration>({\n    productId: '',\n    versionId: '',\n    userCount: 1,\n    selectedModules: [],\n    selectedServices: [],\n    deploymentId: '',\n    supportId: '',\n    servicePeriod: 1,\n  });\n  const [quoteResult, setQuoteResult] = useState<any>(null);\n\n  useEffect(() => {\n    if (configuration.productId) {\n      const product = allSoftwareProducts.find(p => p.id === configuration.productId);\n      setSelectedProduct(product || null);\n      \n      // 设置默认值\n      if (product && !configuration.versionId) {\n        const recommendedVersion = product.versions.find(v => v.recommended) || product.versions[0];\n        const defaultDeployment = product.deploymentOptions[0];\n        const defaultSupport = product.supportLevels[0];\n        const requiredModules = product.modules.filter(m => m.required).map(m => m.id);\n        \n        setConfiguration(prev => ({\n          ...prev,\n          versionId: recommendedVersion.id,\n          deploymentId: defaultDeployment.id,\n          supportId: defaultSupport.id,\n          selectedModules: requiredModules,\n        }));\n      }\n    }\n  }, [configuration.productId]);\n\n  useEffect(() => {\n    if (selectedProduct && configuration.versionId && configuration.deploymentId && configuration.supportId) {\n      try {\n        const result = calculateSoftwareQuote(\n          configuration.productId,\n          configuration.versionId,\n          configuration.userCount,\n          configuration.selectedModules,\n          configuration.selectedServices,\n          configuration.deploymentId,\n          configuration.supportId,\n          configuration.servicePeriod\n        );\n        setQuoteResult(result);\n      } catch (error) {\n        console.error('报价计算错误:', error);\n      }\n    }\n  }, [selectedProduct, configuration]);\n\n  const handleNext = () => {\n    setCurrentStep(prev => Math.min(prev + 1, 3));\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 0));\n  };\n\n  const handleProductSelect = (productId: string) => {\n    setConfiguration(prev => ({\n      ...prev,\n      productId,\n      versionId: '',\n      selectedModules: [],\n      selectedServices: [],\n      deploymentId: '',\n      supportId: '',\n    }));\n  };\n\n  const handleModuleChange = (moduleIds: string[]) => {\n    if (!selectedProduct) return;\n    \n    // 确保必需模块始终被选中\n    const requiredModules = selectedProduct.modules.filter(m => m.required).map(m => m.id);\n    const allSelected = Array.from(new Set([...requiredModules, ...moduleIds]));\n    \n    setConfiguration(prev => ({\n      ...prev,\n      selectedModules: allSelected,\n    }));\n  };\n\n  const renderProductSelection = () => (\n    <Card title=\"选择产品\" size=\"small\">\n      <Row gutter={16}>\n        {allSoftwareProducts.map(product => (\n          <Col span={12} key={product.id}>\n            <Card\n              hoverable\n              size=\"small\"\n              className={configuration.productId === product.id ? 'selected-card' : ''}\n              onClick={() => handleProductSelect(product.id)}\n              style={{\n                border: configuration.productId === product.id ? '2px solid #1890ff' : '1px solid #d9d9d9',\n              }}\n            >\n              <Title level={5}>{product.name}</Title>\n              <Paragraph ellipsis={{ rows: 2 }}>{product.description}</Paragraph>\n              <Space wrap>\n                {product.targetCustomer.map(target => (\n                  <Tag key={target}>{target}</Tag>\n                ))}\n              </Space>\n              <Divider />\n              <Text strong>起价: ¥{product.basePrice}/用户/年</Text>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n    </Card>\n  );\n\n  const renderVersionSelection = () => {\n    if (!selectedProduct) return null;\n\n    return (\n      <Card title=\"选择版本\" size=\"small\">\n        <Radio.Group\n          value={configuration.versionId}\n          onChange={(e) => setConfiguration(prev => ({ ...prev, versionId: e.target.value }))}\n        >\n          <Row gutter={16}>\n            {selectedProduct.versions.map(version => (\n              <Col span={8} key={version.id}>\n                <Card size=\"small\" style={{ marginBottom: 16 }}>\n                  <Radio value={version.id}>\n                    <Title level={5}>\n                      {version.name}\n                      {version.recommended && <Tag color=\"gold\" style={{ marginLeft: 8 }}>推荐</Tag>}\n                    </Title>\n                  </Radio>\n                  <Paragraph>{version.description}</Paragraph>\n                  <Text type=\"secondary\">最大用户数: {version.maxUsers}</Text>\n                  <Divider />\n                  <Text strong>\n                    价格: ¥{(selectedProduct.basePrice * version.priceMultiplier).toFixed(0)}/用户/年\n                  </Text>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n        </Radio.Group>\n      </Card>\n    );\n  };\n\n  const renderConfiguration = () => {\n    if (!selectedProduct) return null;\n\n    return (\n      <Row gutter={16}>\n        <Col span={12}>\n          <Card title=\"基础配置\" size=\"small\" style={{ marginBottom: 16 }}>\n            <Form layout=\"vertical\">\n              <Form.Item label=\"用户数量\">\n                <InputNumber\n                  min={1}\n                  max={selectedProduct.versions.find(v => v.id === configuration.versionId)?.maxUsers || 999}\n                  value={configuration.userCount}\n                  onChange={(value) => setConfiguration(prev => ({ ...prev, userCount: value || 1 }))}\n                  style={{ width: '100%' }}\n                />\n              </Form.Item>\n              <Form.Item label=\"服务期限\">\n                <Select\n                  value={configuration.servicePeriod}\n                  onChange={(value) => setConfiguration(prev => ({ ...prev, servicePeriod: value }))}\n                >\n                  <Option value={1}>1年</Option>\n                  <Option value={2}>2年</Option>\n                  <Option value={3}>3年</Option>\n                  <Option value={5}>5年</Option>\n                </Select>\n              </Form.Item>\n            </Form>\n          </Card>\n\n          <Card title=\"功能模块\" size=\"small\" style={{ marginBottom: 16 }}>\n            <Checkbox.Group\n              value={configuration.selectedModules}\n              onChange={handleModuleChange}\n            >\n              <Row>\n                {selectedProduct.modules.map(module => (\n                  <Col span={24} key={module.id} style={{ marginBottom: 8 }}>\n                    <Checkbox\n                      value={module.id}\n                      disabled={module.required}\n                    >\n                      <Space>\n                        <Text strong>{module.name}</Text>\n                        {module.required && <Tag size=\"small\" color=\"red\">必需</Tag>}\n                        <Tooltip title={module.description}>\n                          <InfoCircleOutlined />\n                        </Tooltip>\n                      </Space>\n                    </Checkbox>\n                    <br />\n                    <Text type=\"secondary\" style={{ marginLeft: 24 }}>\n                      {module.price > 0 && (\n                        module.priceType === 'per_user' \n                          ? `¥${module.price}/用户/年` \n                          : `¥${module.price}/年`\n                      )}\n                    </Text>\n                  </Col>\n                ))}\n              </Row>\n            </Checkbox.Group>\n          </Card>\n        </Col>\n\n        <Col span={12}>\n          <Card title=\"部署方式\" size=\"small\" style={{ marginBottom: 16 }}>\n            <Radio.Group\n              value={configuration.deploymentId}\n              onChange={(e) => setConfiguration(prev => ({ ...prev, deploymentId: e.target.value }))}\n            >\n              {selectedProduct.deploymentOptions.map(option => (\n                <Radio key={option.id} value={option.id} style={{ display: 'block', marginBottom: 8 }}>\n                  <Space direction=\"vertical\" size=\"small\">\n                    <Text strong>{option.name}</Text>\n                    <Text type=\"secondary\">{option.description}</Text>\n                    {option.priceImpact > 0 && (\n                      <Text type=\"warning\">+¥{option.priceImpact}/年</Text>\n                    )}\n                  </Space>\n                </Radio>\n              ))}\n            </Radio.Group>\n          </Card>\n\n          <Card title=\"技术支持\" size=\"small\" style={{ marginBottom: 16 }}>\n            <Radio.Group\n              value={configuration.supportId}\n              onChange={(e) => setConfiguration(prev => ({ ...prev, supportId: e.target.value }))}\n            >\n              {selectedProduct.supportLevels.map(level => (\n                <Radio key={level.id} value={level.id} style={{ display: 'block', marginBottom: 8 }}>\n                  <Space direction=\"vertical\" size=\"small\">\n                    <Text strong>{level.name}</Text>\n                    <Text type=\"secondary\">{level.description}</Text>\n                    <Text type=\"secondary\">响应时间: {level.responseTime}</Text>\n                    {level.price > 0 && (\n                      <Text type=\"warning\">+¥{level.price}/年</Text>\n                    )}\n                  </Space>\n                </Radio>\n              ))}\n            </Radio.Group>\n          </Card>\n\n          <Card title=\"实施服务\" size=\"small\">\n            <Checkbox.Group\n              value={configuration.selectedServices}\n              onChange={(serviceIds) => setConfiguration(prev => ({ ...prev, selectedServices: serviceIds }))}\n            >\n              {selectedProduct.services.map(service => (\n                <Checkbox key={service.id} value={service.id} style={{ display: 'block', marginBottom: 8 }}>\n                  <Space direction=\"vertical\" size=\"small\">\n                    <Text strong>{service.name}</Text>\n                    <Text type=\"secondary\">{service.description}</Text>\n                    <Text type=\"secondary\">周期: {service.duration}</Text>\n                    <Text type=\"warning\">¥{service.price}</Text>\n                  </Space>\n                </Checkbox>\n              ))}\n            </Checkbox.Group>\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  const renderQuoteResult = () => {\n    if (!quoteResult) return null;\n\n    const columns = [\n      {\n        title: '项目',\n        dataIndex: 'item',\n        key: 'item',\n      },\n      {\n        title: '数量',\n        dataIndex: 'quantity',\n        key: 'quantity',\n      },\n      {\n        title: '单位',\n        dataIndex: 'unit',\n        key: 'unit',\n      },\n      {\n        title: '单价',\n        dataIndex: 'unitPrice',\n        key: 'unitPrice',\n        render: (price: number) => `¥${price.toLocaleString()}`,\n      },\n      {\n        title: '期限',\n        dataIndex: 'period',\n        key: 'period',\n        render: (period: number) => `${period}年`,\n      },\n      {\n        title: '金额',\n        dataIndex: 'amount',\n        key: 'amount',\n        render: (amount: number) => `¥${amount.toLocaleString()}`,\n      },\n    ];\n\n    return (\n      <Card title=\"报价结果\" size=\"small\">\n        <Row gutter={16} style={{ marginBottom: 24 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"软件费用\"\n              value={quoteResult.baseAmount}\n              precision={0}\n              prefix=\"¥\"\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"模块费用\"\n              value={quoteResult.moduleAmount}\n              precision={0}\n              prefix=\"¥\"\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"服务费用\"\n              value={quoteResult.serviceAmount + quoteResult.deploymentAmount + quoteResult.supportAmount}\n              precision={0}\n              prefix=\"¥\"\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总计\"\n              value={quoteResult.totalAmount}\n              precision={0}\n              prefix=\"¥\"\n              valueStyle={{ color: '#cf1322', fontWeight: 'bold' }}\n            />\n          </Col>\n        </Row>\n\n        <Table\n          columns={columns}\n          dataSource={quoteResult.breakdown}\n          pagination={false}\n          size=\"small\"\n          rowKey=\"item\"\n          summary={() => (\n            <Table.Summary.Row>\n              <Table.Summary.Cell index={0} colSpan={5}>\n                <Text strong>总计</Text>\n              </Table.Summary.Cell>\n              <Table.Summary.Cell index={1}>\n                <Text strong>¥{quoteResult.totalAmount.toLocaleString()}</Text>\n              </Table.Summary.Cell>\n            </Table.Summary.Row>\n          )}\n        />\n\n        <Divider />\n\n        <Space>\n          <Button type=\"primary\" icon={<FileTextOutlined />}>\n            生成报价单\n          </Button>\n          <Button icon={<DownloadOutlined />}>\n            导出PDF\n          </Button>\n          <Button icon={<SendOutlined />}>\n            发送客户\n          </Button>\n        </Space>\n      </Card>\n    );\n  };\n\n  const steps = [\n    {\n      title: '选择产品',\n      content: renderProductSelection(),\n    },\n    {\n      title: '选择版本',\n      content: renderVersionSelection(),\n    },\n    {\n      title: '配置选项',\n      content: renderConfiguration(),\n    },\n    {\n      title: '生成报价',\n      content: renderQuoteResult(),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card\n        title={\n          <Space>\n            <CalculatorOutlined />\n            <span>金蝶软件报价配置器</span>\n          </Space>\n        }\n      >\n        <Steps current={currentStep} style={{ marginBottom: 24 }}>\n          {steps.map(item => (\n            <Step key={item.title} title={item.title} />\n          ))}\n        </Steps>\n\n        <div style={{ minHeight: '500px' }}>\n          {steps[currentStep].content}\n        </div>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Space>\n            {currentStep > 0 && (\n              <Button onClick={handlePrev}>\n                上一步\n              </Button>\n            )}\n            {currentStep < steps.length - 1 && (\n              <Button\n                type=\"primary\"\n                onClick={handleNext}\n                disabled={\n                  (currentStep === 0 && !configuration.productId) ||\n                  (currentStep === 1 && !configuration.versionId)\n                }\n              >\n                下一步\n              </Button>\n            )}\n            {currentStep === steps.length - 1 && (\n              <Button type=\"primary\" icon={<CheckCircleOutlined />}>\n                确认报价\n              </Button>\n            )}\n          </Space>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default SoftwareQuoteConfigurator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,KAAK,EAELC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,OAAO,QACF,MAAM;AACb,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,mBAAmB,EAAEC,sBAAsB,QAAyB,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExG,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGzB,UAAU;AAC7C,MAAM;EAAE0B;AAAO,CAAC,GAAGtB,MAAM;AACzB,MAAM;EAAEuB;AAAK,CAAC,GAAGlB,KAAK;AAatB,MAAMmB,yBAAmC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAqB;IACrE0C,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CAAC;IACZC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAM,IAAI,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,IAAIuC,aAAa,CAACE,SAAS,EAAE;MAC3B,MAAMU,OAAO,GAAG3B,mBAAmB,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKf,aAAa,CAACE,SAAS,CAAC;MAC/EH,kBAAkB,CAACa,OAAO,IAAI,IAAI,CAAC;;MAEnC;MACA,IAAIA,OAAO,IAAI,CAACZ,aAAa,CAACG,SAAS,EAAE;QACvC,MAAMa,kBAAkB,GAAGJ,OAAO,CAACK,QAAQ,CAACJ,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,IAAIP,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC;QAC3F,MAAMG,iBAAiB,GAAGR,OAAO,CAACS,iBAAiB,CAAC,CAAC,CAAC;QACtD,MAAMC,cAAc,GAAGV,OAAO,CAACW,aAAa,CAAC,CAAC,CAAC;QAC/C,MAAMC,eAAe,GAAGZ,OAAO,CAACa,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACZ,EAAE,CAAC;QAE9Ed,gBAAgB,CAAC6B,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP3B,SAAS,EAAEa,kBAAkB,CAACD,EAAE;UAChCR,YAAY,EAAEa,iBAAiB,CAACL,EAAE;UAClCP,SAAS,EAAEc,cAAc,CAACP,EAAE;UAC5BV,eAAe,EAAEmB;QACnB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC,EAAE,CAACxB,aAAa,CAACE,SAAS,CAAC,CAAC;EAE7BzC,SAAS,CAAC,MAAM;IACd,IAAIqC,eAAe,IAAIE,aAAa,CAACG,SAAS,IAAIH,aAAa,CAACO,YAAY,IAAIP,aAAa,CAACQ,SAAS,EAAE;MACvG,IAAI;QACF,MAAMuB,MAAM,GAAG7C,sBAAsB,CACnCc,aAAa,CAACE,SAAS,EACvBF,aAAa,CAACG,SAAS,EACvBH,aAAa,CAACI,SAAS,EACvBJ,aAAa,CAACK,eAAe,EAC7BL,aAAa,CAACM,gBAAgB,EAC9BN,aAAa,CAACO,YAAY,EAC1BP,aAAa,CAACQ,SAAS,EACvBR,aAAa,CAACS,aAChB,CAAC;QACDE,cAAc,CAACoB,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC,EAAE,CAAClC,eAAe,EAAEE,aAAa,CAAC,CAAC;EAEpC,MAAMkC,UAAU,GAAGA,CAAA,KAAM;IACvBrC,cAAc,CAACiC,IAAI,IAAIK,IAAI,CAACC,GAAG,CAACN,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvBxC,cAAc,CAACiC,IAAI,IAAIK,IAAI,CAACG,GAAG,CAACR,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMS,mBAAmB,GAAIrC,SAAiB,IAAK;IACjDD,gBAAgB,CAAC6B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP5B,SAAS;MACTC,SAAS,EAAE,EAAE;MACbE,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgC,kBAAkB,GAAIC,SAAmB,IAAK;IAClD,IAAI,CAAC3C,eAAe,EAAE;;IAEtB;IACA,MAAM0B,eAAe,GAAG1B,eAAe,CAAC2B,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACZ,EAAE,CAAC;IACtF,MAAM2B,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC,GAAGrB,eAAe,EAAE,GAAGiB,SAAS,CAAC,CAAC,CAAC;IAE3ExC,gBAAgB,CAAC6B,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPzB,eAAe,EAAEqC;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,sBAAsB,GAAGA,CAAA,kBAC7B1D,OAAA,CAAC1B,IAAI;IAACqF,KAAK,EAAC,0BAAM;IAACC,IAAI,EAAC,OAAO;IAAAC,QAAA,eAC7B7D,OAAA,CAACzB,GAAG;MAACuF,MAAM,EAAE,EAAG;MAAAD,QAAA,EACbhE,mBAAmB,CAAC4C,GAAG,CAACjB,OAAO,iBAC9BxB,OAAA,CAACxB,GAAG;QAACuF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ7D,OAAA,CAAC1B,IAAI;UACH0F,SAAS;UACTJ,IAAI,EAAC,OAAO;UACZK,SAAS,EAAErD,aAAa,CAACE,SAAS,KAAKU,OAAO,CAACG,EAAE,GAAG,eAAe,GAAG,EAAG;UACzEuC,OAAO,EAAEA,CAAA,KAAMf,mBAAmB,CAAC3B,OAAO,CAACG,EAAE,CAAE;UAC/CwC,KAAK,EAAE;YACLC,MAAM,EAAExD,aAAa,CAACE,SAAS,KAAKU,OAAO,CAACG,EAAE,GAAG,mBAAmB,GAAG;UACzE,CAAE;UAAAkC,QAAA,gBAEF7D,OAAA,CAACC,KAAK;YAACoE,KAAK,EAAE,CAAE;YAAAR,QAAA,EAAErC,OAAO,CAAC8C;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC1E,OAAA,CAACG,SAAS;YAACwE,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAE,CAAE;YAAAf,QAAA,EAAErC,OAAO,CAACqD;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnE1E,OAAA,CAACrB,KAAK;YAACmG,IAAI;YAAAjB,QAAA,EACRrC,OAAO,CAACuD,cAAc,CAACtC,GAAG,CAACuC,MAAM,iBAChChF,OAAA,CAACX,GAAG;cAAAwE,QAAA,EAAemB;YAAM,GAAfA,MAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACR1E,OAAA,CAACpB,OAAO;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACX1E,OAAA,CAACE,IAAI;YAAC+E,MAAM;YAAApB,QAAA,GAAC,oBAAK,EAACrC,OAAO,CAAC0D,SAAS,EAAC,sBAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC,GAnBWlD,OAAO,CAACG,EAAE;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBzB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACP;EAED,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACzE,eAAe,EAAE,OAAO,IAAI;IAEjC,oBACEV,OAAA,CAAC1B,IAAI;MAACqF,KAAK,EAAC,0BAAM;MAACC,IAAI,EAAC,OAAO;MAAAC,QAAA,eAC7B7D,OAAA,CAACZ,KAAK,CAACgG,KAAK;QACVC,KAAK,EAAEzE,aAAa,CAACG,SAAU;QAC/BuE,QAAQ,EAAGC,CAAC,IAAK1E,gBAAgB,CAAC6B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE3B,SAAS,EAAEwE,CAAC,CAACP,MAAM,CAACK;QAAM,CAAC,CAAC,CAAE;QAAAxB,QAAA,eAEpF7D,OAAA,CAACzB,GAAG;UAACuF,MAAM,EAAE,EAAG;UAAAD,QAAA,EACbnD,eAAe,CAACmB,QAAQ,CAACY,GAAG,CAAC+C,OAAO,iBACnCxF,OAAA,CAACxB,GAAG;YAACuF,IAAI,EAAE,CAAE;YAAAF,QAAA,eACX7D,OAAA,CAAC1B,IAAI;cAACsF,IAAI,EAAC,OAAO;cAACO,KAAK,EAAE;gBAAEsB,YAAY,EAAE;cAAG,CAAE;cAAA5B,QAAA,gBAC7C7D,OAAA,CAACZ,KAAK;gBAACiG,KAAK,EAAEG,OAAO,CAAC7D,EAAG;gBAAAkC,QAAA,eACvB7D,OAAA,CAACC,KAAK;kBAACoE,KAAK,EAAE,CAAE;kBAAAR,QAAA,GACb2B,OAAO,CAAClB,IAAI,EACZkB,OAAO,CAACzD,WAAW,iBAAI/B,OAAA,CAACX,GAAG;oBAACqG,KAAK,EAAC,MAAM;oBAACvB,KAAK,EAAE;sBAAEwB,UAAU,EAAE;oBAAE,CAAE;oBAAA9B,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACR1E,OAAA,CAACG,SAAS;gBAAA0D,QAAA,EAAE2B,OAAO,CAACX;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5C1E,OAAA,CAACE,IAAI;gBAAC0F,IAAI,EAAC,WAAW;gBAAA/B,QAAA,GAAC,kCAAO,EAAC2B,OAAO,CAACK,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD1E,OAAA,CAACpB,OAAO;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACE,IAAI;gBAAC+E,MAAM;gBAAApB,QAAA,GAAC,oBACN,EAAC,CAACnD,eAAe,CAACwE,SAAS,GAAGM,OAAO,CAACM,eAAe,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,sBACzE;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAdUc,OAAO,CAAC7D,EAAE;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAexB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX,CAAC;EAED,MAAMsB,mBAAmB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,IAAI,CAACvF,eAAe,EAAE,OAAO,IAAI;IAEjC,oBACEV,OAAA,CAACzB,GAAG;MAACuF,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACd7D,OAAA,CAACxB,GAAG;QAACuF,IAAI,EAAE,EAAG;QAAAF,QAAA,gBACZ7D,OAAA,CAAC1B,IAAI;UAACqF,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAA5B,QAAA,eAC1D7D,OAAA,CAACnB,IAAI;YAACqH,MAAM,EAAC,UAAU;YAAArC,QAAA,gBACrB7D,OAAA,CAACnB,IAAI,CAACsH,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,eACrB7D,OAAA,CAACjB,WAAW;gBACViE,GAAG,EAAE,CAAE;gBACPE,GAAG,EAAE,EAAA+C,qBAAA,GAAAvF,eAAe,CAACmB,QAAQ,CAACJ,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKf,aAAa,CAACG,SAAS,CAAC,cAAAkF,qBAAA,uBAApEA,qBAAA,CAAsEJ,QAAQ,KAAI,GAAI;gBAC3FR,KAAK,EAAEzE,aAAa,CAACI,SAAU;gBAC/BsE,QAAQ,EAAGD,KAAK,IAAKxE,gBAAgB,CAAC6B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE1B,SAAS,EAAEqE,KAAK,IAAI;gBAAE,CAAC,CAAC,CAAE;gBACpFlB,KAAK,EAAE;kBAAEkC,KAAK,EAAE;gBAAO;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ1E,OAAA,CAACnB,IAAI,CAACsH,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,eACrB7D,OAAA,CAAClB,MAAM;gBACLuG,KAAK,EAAEzE,aAAa,CAACS,aAAc;gBACnCiE,QAAQ,EAAGD,KAAK,IAAKxE,gBAAgB,CAAC6B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAErB,aAAa,EAAEgE;gBAAM,CAAC,CAAC,CAAE;gBAAAxB,QAAA,gBAEnF7D,OAAA,CAACI,MAAM;kBAACiF,KAAK,EAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B1E,OAAA,CAACI,MAAM;kBAACiF,KAAK,EAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B1E,OAAA,CAACI,MAAM;kBAACiF,KAAK,EAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B1E,OAAA,CAACI,MAAM;kBAACiF,KAAK,EAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP1E,OAAA,CAAC1B,IAAI;UAACqF,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAA5B,QAAA,eAC1D7D,OAAA,CAAChB,QAAQ,CAACoG,KAAK;YACbC,KAAK,EAAEzE,aAAa,CAACK,eAAgB;YACrCqE,QAAQ,EAAElC,kBAAmB;YAAAS,QAAA,eAE7B7D,OAAA,CAACzB,GAAG;cAAAsF,QAAA,EACDnD,eAAe,CAAC2B,OAAO,CAACI,GAAG,CAAC6D,MAAM,iBACjCtG,OAAA,CAACxB,GAAG;gBAACuF,IAAI,EAAE,EAAG;gBAAiBI,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBACxD7D,OAAA,CAAChB,QAAQ;kBACPqG,KAAK,EAAEiB,MAAM,CAAC3E,EAAG;kBACjB4E,QAAQ,EAAED,MAAM,CAAC9D,QAAS;kBAAAqB,QAAA,eAE1B7D,OAAA,CAACrB,KAAK;oBAAAkF,QAAA,gBACJ7D,OAAA,CAACE,IAAI;sBAAC+E,MAAM;sBAAApB,QAAA,EAAEyC,MAAM,CAAChC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChC4B,MAAM,CAAC9D,QAAQ,iBAAIxC,OAAA,CAACX,GAAG;sBAACuE,IAAI,EAAC,OAAO;sBAAC8B,KAAK,EAAC,KAAK;sBAAA7B,QAAA,EAAC;oBAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1D1E,OAAA,CAACV,OAAO;sBAACqE,KAAK,EAAE2C,MAAM,CAACzB,WAAY;sBAAAhB,QAAA,eACjC7D,OAAA,CAACN,kBAAkB;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACX1E,OAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAACzB,KAAK,EAAE;oBAAEwB,UAAU,EAAE;kBAAG,CAAE;kBAAA9B,QAAA,EAC9CyC,MAAM,CAACE,KAAK,GAAG,CAAC,KACfF,MAAM,CAACG,SAAS,KAAK,UAAU,GAC3B,IAAIH,MAAM,CAACE,KAAK,OAAO,GACvB,IAAIF,MAAM,CAACE,KAAK,IAAI;gBACzB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GApBW4B,MAAM,CAAC3E,EAAE;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBxB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN1E,OAAA,CAACxB,GAAG;QAACuF,IAAI,EAAE,EAAG;QAAAF,QAAA,gBACZ7D,OAAA,CAAC1B,IAAI;UAACqF,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAA5B,QAAA,eAC1D7D,OAAA,CAACZ,KAAK,CAACgG,KAAK;YACVC,KAAK,EAAEzE,aAAa,CAACO,YAAa;YAClCmE,QAAQ,EAAGC,CAAC,IAAK1E,gBAAgB,CAAC6B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEvB,YAAY,EAAEoE,CAAC,CAACP,MAAM,CAACK;YAAM,CAAC,CAAC,CAAE;YAAAxB,QAAA,EAEtFnD,eAAe,CAACuB,iBAAiB,CAACQ,GAAG,CAACiE,MAAM,iBAC3C1G,OAAA,CAACZ,KAAK;cAAiBiG,KAAK,EAAEqB,MAAM,CAAC/E,EAAG;cAACwC,KAAK,EAAE;gBAAEwC,OAAO,EAAE,OAAO;gBAAElB,YAAY,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACpF7D,OAAA,CAACrB,KAAK;gBAACiI,SAAS,EAAC,UAAU;gBAAChD,IAAI,EAAC,OAAO;gBAAAC,QAAA,gBACtC7D,OAAA,CAACE,IAAI;kBAAC+E,MAAM;kBAAApB,QAAA,EAAE6C,MAAM,CAACpC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjC1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAAA/B,QAAA,EAAE6C,MAAM,CAAC7B;gBAAW;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACjDgC,MAAM,CAACG,WAAW,GAAG,CAAC,iBACrB7G,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,SAAS;kBAAA/B,QAAA,GAAC,OAAE,EAAC6C,MAAM,CAACG,WAAW,EAAC,SAAE;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC,GAPEgC,MAAM,CAAC/E,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQd,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEP1E,OAAA,CAAC1B,IAAI;UAACqF,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAA5B,QAAA,eAC1D7D,OAAA,CAACZ,KAAK,CAACgG,KAAK;YACVC,KAAK,EAAEzE,aAAa,CAACQ,SAAU;YAC/BkE,QAAQ,EAAGC,CAAC,IAAK1E,gBAAgB,CAAC6B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEtB,SAAS,EAAEmE,CAAC,CAACP,MAAM,CAACK;YAAM,CAAC,CAAC,CAAE;YAAAxB,QAAA,EAEnFnD,eAAe,CAACyB,aAAa,CAACM,GAAG,CAAC4B,KAAK,iBACtCrE,OAAA,CAACZ,KAAK;cAAgBiG,KAAK,EAAEhB,KAAK,CAAC1C,EAAG;cAACwC,KAAK,EAAE;gBAAEwC,OAAO,EAAE,OAAO;gBAAElB,YAAY,EAAE;cAAE,CAAE;cAAA5B,QAAA,eAClF7D,OAAA,CAACrB,KAAK;gBAACiI,SAAS,EAAC,UAAU;gBAAChD,IAAI,EAAC,OAAO;gBAAAC,QAAA,gBACtC7D,OAAA,CAACE,IAAI;kBAAC+E,MAAM;kBAAApB,QAAA,EAAEQ,KAAK,CAACC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChC1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAAA/B,QAAA,EAAEQ,KAAK,CAACQ;gBAAW;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjD1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAAA/B,QAAA,GAAC,4BAAM,EAACQ,KAAK,CAACyC,YAAY;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvDL,KAAK,CAACmC,KAAK,GAAG,CAAC,iBACdxG,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,SAAS;kBAAA/B,QAAA,GAAC,OAAE,EAACQ,KAAK,CAACmC,KAAK,EAAC,SAAE;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC,GAREL,KAAK,CAAC1C,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASb,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEP1E,OAAA,CAAC1B,IAAI;UAACqF,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAC,OAAO;UAAAC,QAAA,eAC7B7D,OAAA,CAAChB,QAAQ,CAACoG,KAAK;YACbC,KAAK,EAAEzE,aAAa,CAACM,gBAAiB;YACtCoE,QAAQ,EAAGyB,UAAU,IAAKlG,gBAAgB,CAAC6B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAExB,gBAAgB,EAAE6F;YAAW,CAAC,CAAC,CAAE;YAAAlD,QAAA,EAE/FnD,eAAe,CAACsG,QAAQ,CAACvE,GAAG,CAACwE,OAAO,iBACnCjH,OAAA,CAAChB,QAAQ;cAAkBqG,KAAK,EAAE4B,OAAO,CAACtF,EAAG;cAACwC,KAAK,EAAE;gBAAEwC,OAAO,EAAE,OAAO;gBAAElB,YAAY,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACzF7D,OAAA,CAACrB,KAAK;gBAACiI,SAAS,EAAC,UAAU;gBAAChD,IAAI,EAAC,OAAO;gBAAAC,QAAA,gBACtC7D,OAAA,CAACE,IAAI;kBAAC+E,MAAM;kBAAApB,QAAA,EAAEoD,OAAO,CAAC3C;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClC1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAAA/B,QAAA,EAAEoD,OAAO,CAACpC;gBAAW;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,WAAW;kBAAA/B,QAAA,GAAC,gBAAI,EAACoD,OAAO,CAACC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpD1E,OAAA,CAACE,IAAI;kBAAC0F,IAAI,EAAC,SAAS;kBAAA/B,QAAA,GAAC,MAAC,EAACoD,OAAO,CAACT,KAAK;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC,GANKuC,OAAO,CAACtF,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMyC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC7F,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAM8F,OAAO,GAAG,CACd;MACEzD,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,MAAM;MACjBC,GAAG,EAAE;IACP,CAAC,EACD;MACE3D,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,UAAU;MACrBC,GAAG,EAAE;IACP,CAAC,EACD;MACE3D,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,MAAM;MACjBC,GAAG,EAAE;IACP,CAAC,EACD;MACE3D,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,WAAW;MACtBC,GAAG,EAAE,WAAW;MAChBC,MAAM,EAAGf,KAAa,IAAK,IAAIA,KAAK,CAACgB,cAAc,CAAC,CAAC;IACvD,CAAC,EACD;MACE7D,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,QAAQ;MACnBC,GAAG,EAAE,QAAQ;MACbC,MAAM,EAAGE,MAAc,IAAK,GAAGA,MAAM;IACvC,CAAC,EACD;MACE9D,KAAK,EAAE,IAAI;MACX0D,SAAS,EAAE,QAAQ;MACnBC,GAAG,EAAE,QAAQ;MACbC,MAAM,EAAGG,MAAc,IAAK,IAAIA,MAAM,CAACF,cAAc,CAAC,CAAC;IACzD,CAAC,CACF;IAED,oBACExH,OAAA,CAAC1B,IAAI;MAACqF,KAAK,EAAC,0BAAM;MAACC,IAAI,EAAC,OAAO;MAAAC,QAAA,gBAC7B7D,OAAA,CAACzB,GAAG;QAACuF,MAAM,EAAE,EAAG;QAACK,KAAK,EAAE;UAAEsB,YAAY,EAAE;QAAG,CAAE;QAAA5B,QAAA,gBAC3C7D,OAAA,CAACxB,GAAG;UAACuF,IAAI,EAAE,CAAE;UAAAF,QAAA,eACX7D,OAAA,CAACd,SAAS;YACRyE,KAAK,EAAC,0BAAM;YACZ0B,KAAK,EAAE/D,WAAW,CAACqG,UAAW;YAC9BC,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC;UAAG;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1E,OAAA,CAACxB,GAAG;UAACuF,IAAI,EAAE,CAAE;UAAAF,QAAA,eACX7D,OAAA,CAACd,SAAS;YACRyE,KAAK,EAAC,0BAAM;YACZ0B,KAAK,EAAE/D,WAAW,CAACwG,YAAa;YAChCF,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC;UAAG;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1E,OAAA,CAACxB,GAAG;UAACuF,IAAI,EAAE,CAAE;UAAAF,QAAA,eACX7D,OAAA,CAACd,SAAS;YACRyE,KAAK,EAAC,0BAAM;YACZ0B,KAAK,EAAE/D,WAAW,CAACyG,aAAa,GAAGzG,WAAW,CAAC0G,gBAAgB,GAAG1G,WAAW,CAAC2G,aAAc;YAC5FL,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC;UAAG;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1E,OAAA,CAACxB,GAAG;UAACuF,IAAI,EAAE,CAAE;UAAAF,QAAA,eACX7D,OAAA,CAACd,SAAS;YACRyE,KAAK,EAAC,cAAI;YACV0B,KAAK,EAAE/D,WAAW,CAAC4G,WAAY;YAC/BN,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,MAAG;YACVM,UAAU,EAAE;cAAEzC,KAAK,EAAE,SAAS;cAAE0C,UAAU,EAAE;YAAO;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1E,OAAA,CAACf,KAAK;QACJmI,OAAO,EAAEA,OAAQ;QACjBiB,UAAU,EAAE/G,WAAW,CAACgH,SAAU;QAClCC,UAAU,EAAE,KAAM;QAClB3E,IAAI,EAAC,OAAO;QACZ4E,MAAM,EAAC,MAAM;QACbC,OAAO,EAAEA,CAAA,kBACPzI,OAAA,CAACf,KAAK,CAACyJ,OAAO,CAACnK,GAAG;UAAAsF,QAAA,gBAChB7D,OAAA,CAACf,KAAK,CAACyJ,OAAO,CAACC,IAAI;YAACC,KAAK,EAAE,CAAE;YAACC,OAAO,EAAE,CAAE;YAAAhF,QAAA,eACvC7D,OAAA,CAACE,IAAI;cAAC+E,MAAM;cAAApB,QAAA,EAAC;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACrB1E,OAAA,CAACf,KAAK,CAACyJ,OAAO,CAACC,IAAI;YAACC,KAAK,EAAE,CAAE;YAAA/E,QAAA,eAC3B7D,OAAA,CAACE,IAAI;cAAC+E,MAAM;cAAApB,QAAA,GAAC,MAAC,EAACvC,WAAW,CAAC4G,WAAW,CAACV,cAAc,CAAC,CAAC;YAAA;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACnB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEF1E,OAAA,CAACpB,OAAO;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX1E,OAAA,CAACrB,KAAK;QAAAkF,QAAA,gBACJ7D,OAAA,CAACvB,MAAM;UAACmH,IAAI,EAAC,SAAS;UAACkD,IAAI,eAAE9I,OAAA,CAACR,gBAAgB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAb,QAAA,EAAC;QAEnD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1E,OAAA,CAACvB,MAAM;UAACqK,IAAI,eAAE9I,OAAA,CAACL,gBAAgB;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAb,QAAA,EAAC;QAEpC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1E,OAAA,CAACvB,MAAM;UAACqK,IAAI,eAAE9I,OAAA,CAACJ,YAAY;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAb,QAAA,EAAC;QAEhC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEX,CAAC;EAED,MAAMqE,KAAK,GAAG,CACZ;IACEpF,KAAK,EAAE,MAAM;IACbqF,OAAO,EAAEtF,sBAAsB,CAAC;EAClC,CAAC,EACD;IACEC,KAAK,EAAE,MAAM;IACbqF,OAAO,EAAE7D,sBAAsB,CAAC;EAClC,CAAC,EACD;IACExB,KAAK,EAAE,MAAM;IACbqF,OAAO,EAAEhD,mBAAmB,CAAC;EAC/B,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACbqF,OAAO,EAAE7B,iBAAiB,CAAC;EAC7B,CAAC,CACF;EAED,oBACEnH,OAAA;IAAKmE,KAAK,EAAE;MAAE8E,OAAO,EAAE;IAAO,CAAE;IAAApF,QAAA,eAC9B7D,OAAA,CAAC1B,IAAI;MACHqF,KAAK,eACH3D,OAAA,CAACrB,KAAK;QAAAkF,QAAA,gBACJ7D,OAAA,CAACT,kBAAkB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtB1E,OAAA;UAAA6D,QAAA,EAAM;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACR;MAAAb,QAAA,gBAED7D,OAAA,CAACb,KAAK;QAAC+J,OAAO,EAAE1I,WAAY;QAAC2D,KAAK,EAAE;UAAEsB,YAAY,EAAE;QAAG,CAAE;QAAA5B,QAAA,EACtDkF,KAAK,CAACtG,GAAG,CAAC0G,IAAI,iBACbnJ,OAAA,CAACK,IAAI;UAAkBsD,KAAK,EAAEwF,IAAI,CAACxF;QAAM,GAA9BwF,IAAI,CAACxF,KAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAsB,CAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAER1E,OAAA;QAAKmE,KAAK,EAAE;UAAEiF,SAAS,EAAE;QAAQ,CAAE;QAAAvF,QAAA,EAChCkF,KAAK,CAACvI,WAAW,CAAC,CAACwI;MAAO;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEN1E,OAAA,CAACpB,OAAO;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX1E,OAAA;QAAKmE,KAAK,EAAE;UAAEkF,SAAS,EAAE;QAAS,CAAE;QAAAxF,QAAA,eAClC7D,OAAA,CAACrB,KAAK;UAAAkF,QAAA,GACHrD,WAAW,GAAG,CAAC,iBACdR,OAAA,CAACvB,MAAM;YAACyF,OAAO,EAAEjB,UAAW;YAAAY,QAAA,EAAC;UAE7B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EACAlE,WAAW,GAAGuI,KAAK,CAACO,MAAM,GAAG,CAAC,iBAC7BtJ,OAAA,CAACvB,MAAM;YACLmH,IAAI,EAAC,SAAS;YACd1B,OAAO,EAAEpB,UAAW;YACpByD,QAAQ,EACL/F,WAAW,KAAK,CAAC,IAAI,CAACI,aAAa,CAACE,SAAS,IAC7CN,WAAW,KAAK,CAAC,IAAI,CAACI,aAAa,CAACG,SACtC;YAAA8C,QAAA,EACF;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EACAlE,WAAW,KAAKuI,KAAK,CAACO,MAAM,GAAG,CAAC,iBAC/BtJ,OAAA,CAACvB,MAAM;YAACmH,IAAI,EAAC,SAAS;YAACkD,IAAI,eAAE9I,OAAA,CAACP,mBAAmB;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAb,QAAA,EAAC;UAEtD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnE,EAAA,CAjdID,yBAAmC;AAAAiJ,EAAA,GAAnCjJ,yBAAmC;AAmdzC,eAAeA,yBAAyB;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}