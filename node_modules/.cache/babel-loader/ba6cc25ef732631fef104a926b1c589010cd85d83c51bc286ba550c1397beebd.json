{"ast": null, "code": "// 金蝶产品配置数据\n\n// 金蝶云星辰配置方案\nexport const kisCloudConfigurations = [{\n  id: 'kis-cloud-starter',\n  productId: '1',\n  productName: '金蝶云星辰标准版',\n  productSku: 'KIS-CLOUD-STD',\n  configurationName: '初创企业套餐',\n  description: '适合刚起步的小微企业，提供基础财务管理功能',\n  specifications: {\n    '用户数量': {\n      value: '1用户',\n      options: ['1用户', '3用户', '5用户'],\n      priceImpact: 0,\n      required: true\n    },\n    '账套数量': {\n      value: '1个账套',\n      options: ['1个账套'],\n      priceImpact: 0,\n      required: true\n    },\n    '存储空间': {\n      value: '5GB',\n      options: ['5GB', '10GB', '20GB'],\n      priceImpact: 0,\n      required: true\n    },\n    '服务期限': {\n      value: '1年',\n      options: ['1年', '2年', '3年'],\n      priceImpact: 0,\n      required: true\n    },\n    '技术支持': {\n      value: '标准支持',\n      options: ['标准支持', '高级支持'],\n      priceImpact: 0,\n      required: true\n    }\n  },\n  basePrice: 2980,\n  recommendedFor: ['初创企业', '个体工商户', '1-3人小团队'],\n  features: ['基础总账管理', '简单报表', '凭证录入', '账簿查询', '移动端查看'],\n  limitations: ['不支持多账套', '报表功能有限', '不支持自定义字段'],\n  supportLevel: '工作日在线支持',\n  deploymentType: '云端部署',\n  category: '财务软件'\n}, {\n  id: 'kis-cloud-standard',\n  productId: '1',\n  productName: '金蝶云星辰标准版',\n  productSku: 'KIS-CLOUD-STD',\n  configurationName: '标准企业套餐',\n  description: '适合成长期小微企业，提供完整的财务管理功能',\n  specifications: {\n    '用户数量': {\n      value: '3用户',\n      options: ['1用户', '3用户', '5用户', '10用户'],\n      priceImpact: 0,\n      required: true\n    },\n    '账套数量': {\n      value: '1个账套',\n      options: ['1个账套', '3个账套'],\n      priceImpact: 0,\n      required: true\n    },\n    '存储空间': {\n      value: '10GB',\n      options: ['5GB', '10GB', '20GB', '50GB'],\n      priceImpact: 0,\n      required: true\n    },\n    '服务期限': {\n      value: '1年',\n      options: ['1年', '2年', '3年'],\n      priceImpact: 0,\n      required: true\n    },\n    '技术支持': {\n      value: '标准支持',\n      options: ['标准支持', '高级支持', '专属支持'],\n      priceImpact: 0,\n      required: true\n    }\n  },\n  basePrice: 3980,\n  recommendedFor: ['小微企业', '成长期企业', '3-10人团队'],\n  features: ['完整总账管理', '应收应付管理', '固定资产管理', '出纳管理', '财务报表', '现金流量表', '移动应用支持', '数据备份'],\n  supportLevel: '工作日在线支持 + 电话支持',\n  deploymentType: '云端部署',\n  category: '财务软件'\n}];\n\n// 金蝶精斗云配置方案\nexport const jdyConfigurations = [{\n  id: 'jdy-basic',\n  productId: '2',\n  productName: '金蝶精斗云专业版',\n  productSku: 'JDY-PRO-001',\n  configurationName: '基础管理套餐',\n  description: '适合需要进销存管理的小企业',\n  specifications: {\n    '用户数量': {\n      value: '3用户',\n      options: ['3用户', '5用户', '10用户', '20用户'],\n      priceImpact: 0,\n      required: true\n    },\n    '功能模块': {\n      value: '进销存',\n      options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n      priceImpact: 0,\n      required: true\n    },\n    '存储空间': {\n      value: '10GB',\n      options: ['10GB', '20GB', '50GB', '100GB'],\n      priceImpact: 0,\n      required: true\n    },\n    'API调用': {\n      value: '1000次/月',\n      options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n      priceImpact: 0,\n      required: false\n    }\n  },\n  basePrice: 4980,\n  recommendedFor: ['贸易企业', '零售企业', '小型制造企业'],\n  features: ['采购管理', '销售管理', '库存管理', '基础财务', '报表分析', '移动应用'],\n  supportLevel: '工作日在线支持',\n  deploymentType: '云端部署',\n  category: '企业管理软件'\n}, {\n  id: 'jdy-professional',\n  productId: '2',\n  productName: '金蝶精斗云专业版',\n  productSku: 'JDY-PRO-001',\n  configurationName: '专业管理套餐',\n  description: '适合需要一体化管理的中小企业',\n  specifications: {\n    '用户数量': {\n      value: '5用户',\n      options: ['3用户', '5用户', '10用户', '20用户'],\n      priceImpact: 0,\n      required: true\n    },\n    '功能模块': {\n      value: '进销存+财务+CRM',\n      options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n      priceImpact: 0,\n      required: true\n    },\n    '存储空间': {\n      value: '20GB',\n      options: ['10GB', '20GB', '50GB', '100GB'],\n      priceImpact: 0,\n      required: true\n    },\n    'API调用': {\n      value: '5000次/月',\n      options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n      priceImpact: 0,\n      required: false\n    }\n  },\n  basePrice: 6980,\n  recommendedFor: ['成长期企业', '多业务企业', '需要客户管理的企业'],\n  features: ['完整进销存管理', '财务一体化', 'CRM客户管理', '销售漏斗', '业务报表', '工作流审批', '移动办公', 'API接口'],\n  supportLevel: '工作日在线支持 + 电话支持',\n  deploymentType: '云端部署',\n  category: '企业管理软件'\n}];\n\n// K/3 Cloud配置方案\nexport const k3CloudConfigurations = [{\n  id: 'k3-cloud-standard',\n  productId: '4',\n  productName: '金蝶K/3 Cloud',\n  productSku: 'K3-CLOUD-001',\n  configurationName: '标准ERP套餐',\n  description: '适合中型企业的全面ERP解决方案',\n  specifications: {\n    '用户数量': {\n      value: '50用户',\n      options: ['50用户', '100用户', '200用户', '500用户', '不限'],\n      priceImpact: 0,\n      required: true\n    },\n    '部署方式': {\n      value: '云端部署',\n      options: ['云端部署', '本地部署', '混合部署'],\n      priceImpact: 0,\n      required: true\n    },\n    '功能模块': {\n      value: '财务+供应链',\n      options: ['财务', '财务+供应链', '财务+供应链+生产', '全模块'],\n      priceImpact: 0,\n      required: true\n    },\n    '数据存储': {\n      value: '500GB',\n      options: ['500GB', '1TB', '2TB', '5TB', '不限'],\n      priceImpact: 0,\n      required: true\n    }\n  },\n  basePrice: 58000,\n  recommendedFor: ['中型企业', '制造企业', '集团企业'],\n  features: ['全面财务管理', '供应链管理', '生产制造', '人力资源', '商业智能', '移动应用', '开放平台', '数据分析'],\n  supportLevel: '7x24小时专属支持',\n  deploymentType: '云端/本地/混合',\n  category: 'ERP系统'\n}];\n\n// 合并所有配置\nexport const allKingsoftConfigurations = [...kisCloudConfigurations, ...jdyConfigurations, ...k3CloudConfigurations];\n\n// 根据需求推荐配置\nexport function recommendConfiguration(companySize, industry, budget, requirements) {\n  return allKingsoftConfigurations.filter(config => {\n    // 根据预算筛选\n    if (config.basePrice > budget * 1.2) return false;\n\n    // 根据公司规模筛选\n    const sizeMatch = config.recommendedFor.some(rec => rec.includes(companySize) || companySize === '小微企业' && rec.includes('小') || companySize === '中型企业' && rec.includes('中') || companySize === '大型企业' && rec.includes('大'));\n    if (!sizeMatch) return false;\n\n    // 根据功能需求筛选\n    const featureMatch = requirements.some(req => config.features.some(feature => feature.toLowerCase().includes(req.toLowerCase())));\n    return featureMatch;\n  }).sort((a, b) => Math.abs(a.basePrice - budget) - Math.abs(b.basePrice - budget));\n}", "map": {"version": 3, "names": ["kisCloudConfigurations", "id", "productId", "productName", "productSku", "configurationName", "description", "specifications", "value", "options", "priceImpact", "required", "basePrice", "recommendedFor", "features", "limitations", "supportLevel", "deploymentType", "category", "jdyConfigurations", "k3CloudConfigurations", "allKingsoftConfigurations", "recommendConfiguration", "companySize", "industry", "budget", "requirements", "filter", "config", "sizeMatch", "some", "rec", "includes", "featureMatch", "req", "feature", "toLowerCase", "sort", "a", "b", "Math", "abs"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftConfigurations.ts"], "sourcesContent": ["// 金蝶产品配置数据\nexport interface KingsoftConfiguration {\n  id: string;\n  productId: string;\n  productName: string;\n  productSku: string;\n  configurationName: string;\n  description: string;\n  specifications: {\n    [key: string]: {\n      value: string;\n      options: string[];\n      priceImpact: number; // 价格影响（正数为加价，负数为减价）\n      required: boolean;\n    };\n  };\n  basePrice: number;\n  recommendedFor: string[];\n  features: string[];\n  limitations?: string[];\n  supportLevel: string;\n  deploymentType: string;\n  category: string;\n}\n\n// 金蝶云星辰配置方案\nexport const kisCloudConfigurations: KingsoftConfiguration[] = [\n  {\n    id: 'kis-cloud-starter',\n    productId: '1',\n    productName: '金蝶云星辰标准版',\n    productSku: 'KIS-CLOUD-STD',\n    configurationName: '初创企业套餐',\n    description: '适合刚起步的小微企业，提供基础财务管理功能',\n    specifications: {\n      '用户数量': {\n        value: '1用户',\n        options: ['1用户', '3用户', '5用户'],\n        priceImpact: 0,\n        required: true,\n      },\n      '账套数量': {\n        value: '1个账套',\n        options: ['1个账套'],\n        priceImpact: 0,\n        required: true,\n      },\n      '存储空间': {\n        value: '5GB',\n        options: ['5GB', '10GB', '20GB'],\n        priceImpact: 0,\n        required: true,\n      },\n      '服务期限': {\n        value: '1年',\n        options: ['1年', '2年', '3年'],\n        priceImpact: 0,\n        required: true,\n      },\n      '技术支持': {\n        value: '标准支持',\n        options: ['标准支持', '高级支持'],\n        priceImpact: 0,\n        required: true,\n      },\n    },\n    basePrice: 2980,\n    recommendedFor: ['初创企业', '个体工商户', '1-3人小团队'],\n    features: [\n      '基础总账管理',\n      '简单报表',\n      '凭证录入',\n      '账簿查询',\n      '移动端查看',\n    ],\n    limitations: [\n      '不支持多账套',\n      '报表功能有限',\n      '不支持自定义字段',\n    ],\n    supportLevel: '工作日在线支持',\n    deploymentType: '云端部署',\n    category: '财务软件',\n  },\n  {\n    id: 'kis-cloud-standard',\n    productId: '1',\n    productName: '金蝶云星辰标准版',\n    productSku: 'KIS-CLOUD-STD',\n    configurationName: '标准企业套餐',\n    description: '适合成长期小微企业，提供完整的财务管理功能',\n    specifications: {\n      '用户数量': {\n        value: '3用户',\n        options: ['1用户', '3用户', '5用户', '10用户'],\n        priceImpact: 0,\n        required: true,\n      },\n      '账套数量': {\n        value: '1个账套',\n        options: ['1个账套', '3个账套'],\n        priceImpact: 0,\n        required: true,\n      },\n      '存储空间': {\n        value: '10GB',\n        options: ['5GB', '10GB', '20GB', '50GB'],\n        priceImpact: 0,\n        required: true,\n      },\n      '服务期限': {\n        value: '1年',\n        options: ['1年', '2年', '3年'],\n        priceImpact: 0,\n        required: true,\n      },\n      '技术支持': {\n        value: '标准支持',\n        options: ['标准支持', '高级支持', '专属支持'],\n        priceImpact: 0,\n        required: true,\n      },\n    },\n    basePrice: 3980,\n    recommendedFor: ['小微企业', '成长期企业', '3-10人团队'],\n    features: [\n      '完整总账管理',\n      '应收应付管理',\n      '固定资产管理',\n      '出纳管理',\n      '财务报表',\n      '现金流量表',\n      '移动应用支持',\n      '数据备份',\n    ],\n    supportLevel: '工作日在线支持 + 电话支持',\n    deploymentType: '云端部署',\n    category: '财务软件',\n  },\n];\n\n// 金蝶精斗云配置方案\nexport const jdyConfigurations: KingsoftConfiguration[] = [\n  {\n    id: 'jdy-basic',\n    productId: '2',\n    productName: '金蝶精斗云专业版',\n    productSku: 'JDY-PRO-001',\n    configurationName: '基础管理套餐',\n    description: '适合需要进销存管理的小企业',\n    specifications: {\n      '用户数量': {\n        value: '3用户',\n        options: ['3用户', '5用户', '10用户', '20用户'],\n        priceImpact: 0,\n        required: true,\n      },\n      '功能模块': {\n        value: '进销存',\n        options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n        priceImpact: 0,\n        required: true,\n      },\n      '存储空间': {\n        value: '10GB',\n        options: ['10GB', '20GB', '50GB', '100GB'],\n        priceImpact: 0,\n        required: true,\n      },\n      'API调用': {\n        value: '1000次/月',\n        options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n        priceImpact: 0,\n        required: false,\n      },\n    },\n    basePrice: 4980,\n    recommendedFor: ['贸易企业', '零售企业', '小型制造企业'],\n    features: [\n      '采购管理',\n      '销售管理',\n      '库存管理',\n      '基础财务',\n      '报表分析',\n      '移动应用',\n    ],\n    supportLevel: '工作日在线支持',\n    deploymentType: '云端部署',\n    category: '企业管理软件',\n  },\n  {\n    id: 'jdy-professional',\n    productId: '2',\n    productName: '金蝶精斗云专业版',\n    productSku: 'JDY-PRO-001',\n    configurationName: '专业管理套餐',\n    description: '适合需要一体化管理的中小企业',\n    specifications: {\n      '用户数量': {\n        value: '5用户',\n        options: ['3用户', '5用户', '10用户', '20用户'],\n        priceImpact: 0,\n        required: true,\n      },\n      '功能模块': {\n        value: '进销存+财务+CRM',\n        options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n        priceImpact: 0,\n        required: true,\n      },\n      '存储空间': {\n        value: '20GB',\n        options: ['10GB', '20GB', '50GB', '100GB'],\n        priceImpact: 0,\n        required: true,\n      },\n      'API调用': {\n        value: '5000次/月',\n        options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n        priceImpact: 0,\n        required: false,\n      },\n    },\n    basePrice: 6980,\n    recommendedFor: ['成长期企业', '多业务企业', '需要客户管理的企业'],\n    features: [\n      '完整进销存管理',\n      '财务一体化',\n      'CRM客户管理',\n      '销售漏斗',\n      '业务报表',\n      '工作流审批',\n      '移动办公',\n      'API接口',\n    ],\n    supportLevel: '工作日在线支持 + 电话支持',\n    deploymentType: '云端部署',\n    category: '企业管理软件',\n  },\n];\n\n// K/3 Cloud配置方案\nexport const k3CloudConfigurations: KingsoftConfiguration[] = [\n  {\n    id: 'k3-cloud-standard',\n    productId: '4',\n    productName: '金蝶K/3 Cloud',\n    productSku: 'K3-CLOUD-001',\n    configurationName: '标准ERP套餐',\n    description: '适合中型企业的全面ERP解决方案',\n    specifications: {\n      '用户数量': {\n        value: '50用户',\n        options: ['50用户', '100用户', '200用户', '500用户', '不限'],\n        priceImpact: 0,\n        required: true,\n      },\n      '部署方式': {\n        value: '云端部署',\n        options: ['云端部署', '本地部署', '混合部署'],\n        priceImpact: 0,\n        required: true,\n      },\n      '功能模块': {\n        value: '财务+供应链',\n        options: ['财务', '财务+供应链', '财务+供应链+生产', '全模块'],\n        priceImpact: 0,\n        required: true,\n      },\n      '数据存储': {\n        value: '500GB',\n        options: ['500GB', '1TB', '2TB', '5TB', '不限'],\n        priceImpact: 0,\n        required: true,\n      },\n    },\n    basePrice: 58000,\n    recommendedFor: ['中型企业', '制造企业', '集团企业'],\n    features: [\n      '全面财务管理',\n      '供应链管理',\n      '生产制造',\n      '人力资源',\n      '商业智能',\n      '移动应用',\n      '开放平台',\n      '数据分析',\n    ],\n    supportLevel: '7x24小时专属支持',\n    deploymentType: '云端/本地/混合',\n    category: 'ERP系统',\n  },\n];\n\n// 合并所有配置\nexport const allKingsoftConfigurations: KingsoftConfiguration[] = [\n  ...kisCloudConfigurations,\n  ...jdyConfigurations,\n  ...k3CloudConfigurations,\n];\n\n// 根据需求推荐配置\nexport function recommendConfiguration(\n  companySize: string,\n  industry: string,\n  budget: number,\n  requirements: string[]\n): KingsoftConfiguration[] {\n  return allKingsoftConfigurations.filter(config => {\n    // 根据预算筛选\n    if (config.basePrice > budget * 1.2) return false;\n    \n    // 根据公司规模筛选\n    const sizeMatch = config.recommendedFor.some(rec => \n      rec.includes(companySize) || \n      (companySize === '小微企业' && rec.includes('小')) ||\n      (companySize === '中型企业' && rec.includes('中')) ||\n      (companySize === '大型企业' && rec.includes('大'))\n    );\n    \n    if (!sizeMatch) return false;\n    \n    // 根据功能需求筛选\n    const featureMatch = requirements.some(req => \n      config.features.some(feature => \n        feature.toLowerCase().includes(req.toLowerCase())\n      )\n    );\n    \n    return featureMatch;\n  }).sort((a, b) => Math.abs(a.basePrice - budget) - Math.abs(b.basePrice - budget));\n}\n"], "mappings": "AAAA;;AAyBA;AACA,OAAO,MAAMA,sBAA+C,GAAG,CAC7D;EACEC,EAAE,EAAE,mBAAmB;EACvBC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,UAAU;EACvBC,UAAU,EAAE,eAAe;EAC3BC,iBAAiB,EAAE,QAAQ;EAC3BC,WAAW,EAAE,uBAAuB;EACpCC,cAAc,EAAE;IACd,MAAM,EAAE;MACNC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,CAAC;MACjBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MAChCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3BC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MACzBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;EAC5CC,QAAQ,EAAE,CACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACR;EACDC,WAAW,EAAE,CACX,QAAQ,EACR,QAAQ,EACR,UAAU,CACX;EACDC,YAAY,EAAE,SAAS;EACvBC,cAAc,EAAE,MAAM;EACtBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEjB,EAAE,EAAE,oBAAoB;EACxBC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,UAAU;EACvBC,UAAU,EAAE,eAAe;EAC3BC,iBAAiB,EAAE,QAAQ;EAC3BC,WAAW,EAAE,uBAAuB;EACpCC,cAAc,EAAE;IACd,MAAM,EAAE;MACNC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;MACtCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MACzBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MACxCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3BC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MACjCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;EAC5CC,QAAQ,EAAE,CACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,CACP;EACDE,YAAY,EAAE,gBAAgB;EAC9BC,cAAc,EAAE,MAAM;EACtBC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,iBAA0C,GAAG,CACxD;EACElB,EAAE,EAAE,WAAW;EACfC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,UAAU;EACvBC,UAAU,EAAE,aAAa;EACzBC,iBAAiB,EAAE,QAAQ;EAC3BC,WAAW,EAAE,eAAe;EAC5BC,cAAc,EAAE;IACd,MAAM,EAAE;MACNC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MACvCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC;MAChDC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;MAC1CC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO,EAAE;MACPH,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;MACjDC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;EAC1CC,QAAQ,EAAE,CACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EACDE,YAAY,EAAE,SAAS;EACvBC,cAAc,EAAE,MAAM;EACtBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEjB,EAAE,EAAE,kBAAkB;EACtBC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,UAAU;EACvBC,UAAU,EAAE,aAAa;EACzBC,iBAAiB,EAAE,QAAQ;EAC3BC,WAAW,EAAE,gBAAgB;EAC7BC,cAAc,EAAE;IACd,MAAM,EAAE;MACNC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MACvCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC;MAChDC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;MAC1CC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO,EAAE;MACPH,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;MACjDC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;EAC/CC,QAAQ,EAAE,CACR,SAAS,EACT,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR;EACDE,YAAY,EAAE,gBAAgB;EAC9BC,cAAc,EAAE,MAAM;EACtBC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,OAAO,MAAME,qBAA8C,GAAG,CAC5D;EACEnB,EAAE,EAAE,mBAAmB;EACvBC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,iBAAiB,EAAE,SAAS;EAC5BC,WAAW,EAAE,kBAAkB;EAC/BC,cAAc,EAAE;IACd,MAAM,EAAE;MACNC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;MAClDC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MACjCC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC;MAC7CC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAM,EAAE;MACNH,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7CC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE,KAAK;EAChBC,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACxCC,QAAQ,EAAE,CACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EACDE,YAAY,EAAE,YAAY;EAC1BC,cAAc,EAAE,UAAU;EAC1BC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,OAAO,MAAMG,yBAAkD,GAAG,CAChE,GAAGrB,sBAAsB,EACzB,GAAGmB,iBAAiB,EACpB,GAAGC,qBAAqB,CACzB;;AAED;AACA,OAAO,SAASE,sBAAsBA,CACpCC,WAAmB,EACnBC,QAAgB,EAChBC,MAAc,EACdC,YAAsB,EACG;EACzB,OAAOL,yBAAyB,CAACM,MAAM,CAACC,MAAM,IAAI;IAChD;IACA,IAAIA,MAAM,CAAChB,SAAS,GAAGa,MAAM,GAAG,GAAG,EAAE,OAAO,KAAK;;IAEjD;IACA,MAAMI,SAAS,GAAGD,MAAM,CAACf,cAAc,CAACiB,IAAI,CAACC,GAAG,IAC9CA,GAAG,CAACC,QAAQ,CAACT,WAAW,CAAC,IACxBA,WAAW,KAAK,MAAM,IAAIQ,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAE,IAC5CT,WAAW,KAAK,MAAM,IAAIQ,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAE,IAC5CT,WAAW,KAAK,MAAM,IAAIQ,GAAG,CAACC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IAED,IAAI,CAACH,SAAS,EAAE,OAAO,KAAK;;IAE5B;IACA,MAAMI,YAAY,GAAGP,YAAY,CAACI,IAAI,CAACI,GAAG,IACxCN,MAAM,CAACd,QAAQ,CAACgB,IAAI,CAACK,OAAO,IAC1BA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACJ,QAAQ,CAACE,GAAG,CAACE,WAAW,CAAC,CAAC,CAClD,CACF,CAAC;IAED,OAAOH,YAAY;EACrB,CAAC,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC1B,SAAS,GAAGa,MAAM,CAAC,GAAGe,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC3B,SAAS,GAAGa,MAAM,CAAC,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}