{"ast": null, "code": "import ascending from \"./ascending.js\";\nexport default function least(values) {\n  let compare = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ascending;\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? ascending(value, minValue) < 0 : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["ascending", "least", "values", "compare", "arguments", "length", "undefined", "min", "defined", "minValue", "element", "value"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/d3-array/src/least.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,SAASC,KAAKA,CAACC,MAAM,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGJ,SAAS;EACvD,IAAIO,GAAG;EACP,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIL,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,IAAII,QAAQ;IACZ,KAAK,MAAMC,OAAO,IAAIR,MAAM,EAAE;MAC5B,MAAMS,KAAK,GAAGR,OAAO,CAACO,OAAO,CAAC;MAC9B,IAAIF,OAAO,GACLR,SAAS,CAACW,KAAK,EAAEF,QAAQ,CAAC,GAAG,CAAC,GAC9BT,SAAS,CAACW,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACnCJ,GAAG,GAAGG,OAAO;QACbD,QAAQ,GAAGE,KAAK;QAChBH,OAAO,GAAG,IAAI;MAChB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMG,KAAK,IAAIT,MAAM,EAAE;MAC1B,IAAIM,OAAO,GACLL,OAAO,CAACQ,KAAK,EAAEJ,GAAG,CAAC,GAAG,CAAC,GACvBJ,OAAO,CAACQ,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACjCJ,GAAG,GAAGI,KAAK;QACXH,OAAO,GAAG,IAAI;MAChB;IACF;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}