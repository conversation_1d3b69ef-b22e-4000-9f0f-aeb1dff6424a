{"ast": null, "code": "// 金蝶产品定价规则配置\n\n// 金蝶云星辰定价规则\nexport const kisCloudPricingRules = [{\n  id: 'kis-volume-1',\n  name: '云星辰批量采购折扣',\n  description: '购买5套以上享受8.5折优惠',\n  productSku: 'KIS-CLOUD-STD',\n  ruleType: 'volume_discount',\n  conditions: {\n    minQuantity: 5,\n    maxQuantity: 9\n  },\n  discount: {\n    type: 'percentage',\n    value: 15 // 8.5折 = 15%折扣\n  },\n  priority: 1,\n  isActive: true,\n  validFrom: '2024-01-01'\n}, {\n  id: 'kis-volume-2',\n  name: '云星辰大批量采购折扣',\n  description: '购买10套以上享受8折优惠',\n  productSku: 'KIS-CLOUD-STD',\n  ruleType: 'volume_discount',\n  conditions: {\n    minQuantity: 10\n  },\n  discount: {\n    type: 'percentage',\n    value: 20 // 8折 = 20%折扣\n  },\n  priority: 2,\n  isActive: true,\n  validFrom: '2024-01-01'\n}, {\n  id: 'kis-renewal-1',\n  name: '云星辰续费优惠',\n  description: '老客户续费享受9折优惠',\n  productSku: 'KIS-CLOUD-STD',\n  ruleType: 'renewal',\n  conditions: {\n    isRenewal: true\n  },\n  discount: {\n    type: 'percentage',\n    value: 10 // 9折 = 10%折扣\n  },\n  priority: 3,\n  isActive: true,\n  validFrom: '2024-01-01'\n}, {\n  id: 'kis-partner-1',\n  name: '渠道伙伴价格',\n  description: '认证渠道伙伴享受7折价格',\n  productSku: 'KIS-CLOUD-STD',\n  ruleType: 'channel_partner',\n  conditions: {\n    partnerLevel: ['certified_partner', 'gold_partner']\n  },\n  discount: {\n    type: 'percentage',\n    value: 30 // 7折 = 30%折扣\n  },\n  priority: 4,\n  isActive: true,\n  validFrom: '2024-01-01'\n}];\n\n// 金蝶精斗云定价规则\nexport const jdyPricingRules = [{\n  id: 'jdy-volume-1',\n  name: '精斗云批量采购折扣',\n  description: '购买3套以上享受9折优惠',\n  productSku: 'JDY-PRO-001',\n  ruleType: 'volume_discount',\n  conditions: {\n    minQuantity: 3,\n    maxQuantity: 9\n  },\n  discount: {\n    type: 'percentage',\n    value: 10 // 9折 = 10%折扣\n  },\n  priority: 1,\n  isActive: true,\n  validFrom: '2024-01-01'\n}, {\n  id: 'jdy-industry-1',\n  name: '制造业解决方案加价',\n  description: '制造业定制解决方案加价20%',\n  productSku: 'JDY-PRO-001',\n  ruleType: 'industry_solution',\n  conditions: {\n    industry: ['manufacturing']\n  },\n  discount: {\n    type: 'percentage',\n    value: -20 // 加价20%\n  },\n  priority: 5,\n  isActive: true,\n  validFrom: '2024-01-01'\n}, {\n  id: 'jdy-industry-2',\n  name: '零售业解决方案加价',\n  description: '零售业定制解决方案加价15%',\n  productSku: 'JDY-PRO-001',\n  ruleType: 'industry_solution',\n  conditions: {\n    industry: ['retail']\n  },\n  discount: {\n    type: 'percentage',\n    value: -15 // 加价15%\n  },\n  priority: 6,\n  isActive: true,\n  validFrom: '2024-01-01'\n}];\n\n// K/3 Cloud定价规则\nexport const k3CloudPricingRules = [{\n  id: 'k3-enterprise-1',\n  name: 'K/3 Cloud企业级折扣',\n  description: '大型企业客户享受特殊折扣',\n  productSku: 'K3-CLOUD-001',\n  ruleType: 'customer_tier',\n  conditions: {\n    customerTier: ['platinum', 'enterprise']\n  },\n  discount: {\n    type: 'percentage',\n    value: 15 // 8.5折\n  },\n  priority: 1,\n  isActive: true,\n  validFrom: '2024-01-01'\n}];\n\n// 合并所有定价规则\nexport const allKingsoftPricingRules = [...kisCloudPricingRules, ...jdyPricingRules, ...k3CloudPricingRules];\n\n// 定价计算函数\nexport function calculateKingsoftPrice(basePrice, productSku, quantity = 1, customerTier = 'standard', isRenewal = false, partnerLevel, industry) {\n  const applicableRules = allKingsoftPricingRules.filter(rule => rule.productSku === productSku && rule.isActive).filter(rule => {\n    // 检查数量条件\n    if (rule.conditions.minQuantity && quantity < rule.conditions.minQuantity) return false;\n    if (rule.conditions.maxQuantity && quantity > rule.conditions.maxQuantity) return false;\n\n    // 检查客户等级条件\n    if (rule.conditions.customerTier && !rule.conditions.customerTier.includes(customerTier)) return false;\n\n    // 检查续费条件\n    if (rule.conditions.isRenewal !== undefined && rule.conditions.isRenewal !== isRenewal) return false;\n\n    // 检查渠道伙伴条件\n    if (rule.conditions.partnerLevel && (!partnerLevel || !rule.conditions.partnerLevel.includes(partnerLevel))) return false;\n\n    // 检查行业条件\n    if (rule.conditions.industry && (!industry || !rule.conditions.industry.includes(industry))) return false;\n    return true;\n  }).sort((a, b) => a.priority - b.priority);\n  const originalPrice = basePrice * quantity;\n  let finalPrice = originalPrice;\n  let totalDiscount = 0;\n\n  // 应用定价规则\n  applicableRules.forEach(rule => {\n    if (rule.discount.type === 'percentage') {\n      const discountAmount = originalPrice * (rule.discount.value / 100);\n      finalPrice -= discountAmount;\n      totalDiscount += discountAmount;\n    } else if (rule.discount.type === 'fixed_amount') {\n      finalPrice -= rule.discount.value * quantity;\n      totalDiscount += rule.discount.value * quantity;\n    }\n  });\n\n  // 确保最终价格不为负数\n  finalPrice = Math.max(finalPrice, 0);\n  return {\n    originalPrice,\n    finalPrice: Math.round(finalPrice * 100) / 100,\n    // 保留两位小数\n    totalDiscount: Math.round(totalDiscount * 100) / 100,\n    appliedRules: applicableRules\n  };\n}", "map": {"version": 3, "names": ["kisCloudPricingRules", "id", "name", "description", "productSku", "ruleType", "conditions", "minQuantity", "maxQuantity", "discount", "type", "value", "priority", "isActive", "validFrom", "isRenewal", "partnerLevel", "jdyPricingRules", "industry", "k3CloudPricingRules", "customerTier", "allKingsoftPricingRules", "calculateKingsoftPrice", "basePrice", "quantity", "applicableRules", "filter", "rule", "includes", "undefined", "sort", "a", "b", "originalPrice", "finalPrice", "totalDiscount", "for<PERSON>ach", "discountAmount", "Math", "max", "round", "appliedRules"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/kingsoftPricingRules.ts"], "sourcesContent": ["// 金蝶产品定价规则配置\nexport interface KingsoftPricingRule {\n  id: string;\n  name: string;\n  description: string;\n  productSku: string;\n  ruleType: 'volume_discount' | 'customer_tier' | 'renewal' | 'channel_partner' | 'industry_solution';\n  conditions: {\n    minQuantity?: number;\n    maxQuantity?: number;\n    customerTier?: string[];\n    isRenewal?: boolean;\n    partnerLevel?: string[];\n    industry?: string[];\n  };\n  discount: {\n    type: 'percentage' | 'fixed_amount';\n    value: number;\n  };\n  priority: number;\n  isActive: boolean;\n  validFrom: string;\n  validTo?: string;\n}\n\n// 金蝶云星辰定价规则\nexport const kisCloudPricingRules: KingsoftPricingRule[] = [\n  {\n    id: 'kis-volume-1',\n    name: '云星辰批量采购折扣',\n    description: '购买5套以上享受8.5折优惠',\n    productSku: 'KIS-CLOUD-STD',\n    ruleType: 'volume_discount',\n    conditions: {\n      minQuantity: 5,\n      maxQuantity: 9,\n    },\n    discount: {\n      type: 'percentage',\n      value: 15, // 8.5折 = 15%折扣\n    },\n    priority: 1,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n  {\n    id: 'kis-volume-2',\n    name: '云星辰大批量采购折扣',\n    description: '购买10套以上享受8折优惠',\n    productSku: 'KIS-CLOUD-STD',\n    ruleType: 'volume_discount',\n    conditions: {\n      minQuantity: 10,\n    },\n    discount: {\n      type: 'percentage',\n      value: 20, // 8折 = 20%折扣\n    },\n    priority: 2,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n  {\n    id: 'kis-renewal-1',\n    name: '云星辰续费优惠',\n    description: '老客户续费享受9折优惠',\n    productSku: 'KIS-CLOUD-STD',\n    ruleType: 'renewal',\n    conditions: {\n      isRenewal: true,\n    },\n    discount: {\n      type: 'percentage',\n      value: 10, // 9折 = 10%折扣\n    },\n    priority: 3,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n  {\n    id: 'kis-partner-1',\n    name: '渠道伙伴价格',\n    description: '认证渠道伙伴享受7折价格',\n    productSku: 'KIS-CLOUD-STD',\n    ruleType: 'channel_partner',\n    conditions: {\n      partnerLevel: ['certified_partner', 'gold_partner'],\n    },\n    discount: {\n      type: 'percentage',\n      value: 30, // 7折 = 30%折扣\n    },\n    priority: 4,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n];\n\n// 金蝶精斗云定价规则\nexport const jdyPricingRules: KingsoftPricingRule[] = [\n  {\n    id: 'jdy-volume-1',\n    name: '精斗云批量采购折扣',\n    description: '购买3套以上享受9折优惠',\n    productSku: 'JDY-PRO-001',\n    ruleType: 'volume_discount',\n    conditions: {\n      minQuantity: 3,\n      maxQuantity: 9,\n    },\n    discount: {\n      type: 'percentage',\n      value: 10, // 9折 = 10%折扣\n    },\n    priority: 1,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n  {\n    id: 'jdy-industry-1',\n    name: '制造业解决方案加价',\n    description: '制造业定制解决方案加价20%',\n    productSku: 'JDY-PRO-001',\n    ruleType: 'industry_solution',\n    conditions: {\n      industry: ['manufacturing'],\n    },\n    discount: {\n      type: 'percentage',\n      value: -20, // 加价20%\n    },\n    priority: 5,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n  {\n    id: 'jdy-industry-2',\n    name: '零售业解决方案加价',\n    description: '零售业定制解决方案加价15%',\n    productSku: 'JDY-PRO-001',\n    ruleType: 'industry_solution',\n    conditions: {\n      industry: ['retail'],\n    },\n    discount: {\n      type: 'percentage',\n      value: -15, // 加价15%\n    },\n    priority: 6,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n];\n\n// K/3 Cloud定价规则\nexport const k3CloudPricingRules: KingsoftPricingRule[] = [\n  {\n    id: 'k3-enterprise-1',\n    name: 'K/3 Cloud企业级折扣',\n    description: '大型企业客户享受特殊折扣',\n    productSku: 'K3-CLOUD-001',\n    ruleType: 'customer_tier',\n    conditions: {\n      customerTier: ['platinum', 'enterprise'],\n    },\n    discount: {\n      type: 'percentage',\n      value: 15, // 8.5折\n    },\n    priority: 1,\n    isActive: true,\n    validFrom: '2024-01-01',\n  },\n];\n\n// 合并所有定价规则\nexport const allKingsoftPricingRules: KingsoftPricingRule[] = [\n  ...kisCloudPricingRules,\n  ...jdyPricingRules,\n  ...k3CloudPricingRules,\n];\n\n// 定价计算函数\nexport function calculateKingsoftPrice(\n  basePrice: number,\n  productSku: string,\n  quantity: number = 1,\n  customerTier: string = 'standard',\n  isRenewal: boolean = false,\n  partnerLevel?: string,\n  industry?: string\n): {\n  originalPrice: number;\n  finalPrice: number;\n  totalDiscount: number;\n  appliedRules: KingsoftPricingRule[];\n} {\n  const applicableRules = allKingsoftPricingRules\n    .filter(rule => rule.productSku === productSku && rule.isActive)\n    .filter(rule => {\n      // 检查数量条件\n      if (rule.conditions.minQuantity && quantity < rule.conditions.minQuantity) return false;\n      if (rule.conditions.maxQuantity && quantity > rule.conditions.maxQuantity) return false;\n      \n      // 检查客户等级条件\n      if (rule.conditions.customerTier && !rule.conditions.customerTier.includes(customerTier)) return false;\n      \n      // 检查续费条件\n      if (rule.conditions.isRenewal !== undefined && rule.conditions.isRenewal !== isRenewal) return false;\n      \n      // 检查渠道伙伴条件\n      if (rule.conditions.partnerLevel && (!partnerLevel || !rule.conditions.partnerLevel.includes(partnerLevel))) return false;\n      \n      // 检查行业条件\n      if (rule.conditions.industry && (!industry || !rule.conditions.industry.includes(industry))) return false;\n      \n      return true;\n    })\n    .sort((a, b) => a.priority - b.priority);\n\n  const originalPrice = basePrice * quantity;\n  let finalPrice = originalPrice;\n  let totalDiscount = 0;\n\n  // 应用定价规则\n  applicableRules.forEach(rule => {\n    if (rule.discount.type === 'percentage') {\n      const discountAmount = originalPrice * (rule.discount.value / 100);\n      finalPrice -= discountAmount;\n      totalDiscount += discountAmount;\n    } else if (rule.discount.type === 'fixed_amount') {\n      finalPrice -= rule.discount.value * quantity;\n      totalDiscount += rule.discount.value * quantity;\n    }\n  });\n\n  // 确保最终价格不为负数\n  finalPrice = Math.max(finalPrice, 0);\n\n  return {\n    originalPrice,\n    finalPrice: Math.round(finalPrice * 100) / 100, // 保留两位小数\n    totalDiscount: Math.round(totalDiscount * 100) / 100,\n    appliedRules: applicableRules,\n  };\n}\n"], "mappings": "AAAA;;AAyBA;AACA,OAAO,MAAMA,oBAA2C,GAAG,CACzD;EACEC,EAAE,EAAE,cAAc;EAClBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,gBAAgB;EAC7BC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE;IACVC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEb,EAAE,EAAE,cAAc;EAClBC,IAAI,EAAE,YAAY;EAClBC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE;IACVC,WAAW,EAAE;EACf,CAAC;EACDE,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEb,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,SAAS;EACnBC,UAAU,EAAE;IACVS,SAAS,EAAE;EACb,CAAC;EACDN,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEb,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,cAAc;EAC3BC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE;IACVU,YAAY,EAAE,CAAC,mBAAmB,EAAE,cAAc;EACpD,CAAC;EACDP,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMG,eAAsC,GAAG,CACpD;EACEhB,EAAE,EAAE,cAAc;EAClBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,cAAc;EAC3BC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE;IACVC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEb,EAAE,EAAE,gBAAgB;EACpBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,gBAAgB;EAC7BC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE;IACVY,QAAQ,EAAE,CAAC,eAAe;EAC5B,CAAC;EACDT,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CAAC,EAAE,CAAE;EACd,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,EACD;EACEb,EAAE,EAAE,gBAAgB;EACpBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,gBAAgB;EAC7BC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE;IACVY,QAAQ,EAAE,CAAC,QAAQ;EACrB,CAAC;EACDT,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CAAC,EAAE,CAAE;EACd,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMK,mBAA0C,GAAG,CACxD;EACElB,EAAE,EAAE,iBAAiB;EACrBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,cAAc;EAC3BC,UAAU,EAAE,cAAc;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE;IACVc,YAAY,EAAE,CAAC,UAAU,EAAE,YAAY;EACzC,CAAC;EACDX,QAAQ,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE,CAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMO,uBAA8C,GAAG,CAC5D,GAAGrB,oBAAoB,EACvB,GAAGiB,eAAe,EAClB,GAAGE,mBAAmB,CACvB;;AAED;AACA,OAAO,SAASG,sBAAsBA,CACpCC,SAAiB,EACjBnB,UAAkB,EAClBoB,QAAgB,GAAG,CAAC,EACpBJ,YAAoB,GAAG,UAAU,EACjCL,SAAkB,GAAG,KAAK,EAC1BC,YAAqB,EACrBE,QAAiB,EAMjB;EACA,MAAMO,eAAe,GAAGJ,uBAAuB,CAC5CK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvB,UAAU,KAAKA,UAAU,IAAIuB,IAAI,CAACd,QAAQ,CAAC,CAC/Da,MAAM,CAACC,IAAI,IAAI;IACd;IACA,IAAIA,IAAI,CAACrB,UAAU,CAACC,WAAW,IAAIiB,QAAQ,GAAGG,IAAI,CAACrB,UAAU,CAACC,WAAW,EAAE,OAAO,KAAK;IACvF,IAAIoB,IAAI,CAACrB,UAAU,CAACE,WAAW,IAAIgB,QAAQ,GAAGG,IAAI,CAACrB,UAAU,CAACE,WAAW,EAAE,OAAO,KAAK;;IAEvF;IACA,IAAImB,IAAI,CAACrB,UAAU,CAACc,YAAY,IAAI,CAACO,IAAI,CAACrB,UAAU,CAACc,YAAY,CAACQ,QAAQ,CAACR,YAAY,CAAC,EAAE,OAAO,KAAK;;IAEtG;IACA,IAAIO,IAAI,CAACrB,UAAU,CAACS,SAAS,KAAKc,SAAS,IAAIF,IAAI,CAACrB,UAAU,CAACS,SAAS,KAAKA,SAAS,EAAE,OAAO,KAAK;;IAEpG;IACA,IAAIY,IAAI,CAACrB,UAAU,CAACU,YAAY,KAAK,CAACA,YAAY,IAAI,CAACW,IAAI,CAACrB,UAAU,CAACU,YAAY,CAACY,QAAQ,CAACZ,YAAY,CAAC,CAAC,EAAE,OAAO,KAAK;;IAEzH;IACA,IAAIW,IAAI,CAACrB,UAAU,CAACY,QAAQ,KAAK,CAACA,QAAQ,IAAI,CAACS,IAAI,CAACrB,UAAU,CAACY,QAAQ,CAACU,QAAQ,CAACV,QAAQ,CAAC,CAAC,EAAE,OAAO,KAAK;IAEzG,OAAO,IAAI;EACb,CAAC,CAAC,CACDY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,QAAQ,GAAGoB,CAAC,CAACpB,QAAQ,CAAC;EAE1C,MAAMqB,aAAa,GAAGV,SAAS,GAAGC,QAAQ;EAC1C,IAAIU,UAAU,GAAGD,aAAa;EAC9B,IAAIE,aAAa,GAAG,CAAC;;EAErB;EACAV,eAAe,CAACW,OAAO,CAACT,IAAI,IAAI;IAC9B,IAAIA,IAAI,CAAClB,QAAQ,CAACC,IAAI,KAAK,YAAY,EAAE;MACvC,MAAM2B,cAAc,GAAGJ,aAAa,IAAIN,IAAI,CAAClB,QAAQ,CAACE,KAAK,GAAG,GAAG,CAAC;MAClEuB,UAAU,IAAIG,cAAc;MAC5BF,aAAa,IAAIE,cAAc;IACjC,CAAC,MAAM,IAAIV,IAAI,CAAClB,QAAQ,CAACC,IAAI,KAAK,cAAc,EAAE;MAChDwB,UAAU,IAAIP,IAAI,CAAClB,QAAQ,CAACE,KAAK,GAAGa,QAAQ;MAC5CW,aAAa,IAAIR,IAAI,CAAClB,QAAQ,CAACE,KAAK,GAAGa,QAAQ;IACjD;EACF,CAAC,CAAC;;EAEF;EACAU,UAAU,GAAGI,IAAI,CAACC,GAAG,CAACL,UAAU,EAAE,CAAC,CAAC;EAEpC,OAAO;IACLD,aAAa;IACbC,UAAU,EAAEI,IAAI,CAACE,KAAK,CAACN,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;IAAE;IAChDC,aAAa,EAAEG,IAAI,CAACE,KAAK,CAACL,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;IACpDM,YAAY,EAAEhB;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}