{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport { ascendingDefined, compareDefined } from \"./sort.js\";\nexport default function rank(values) {\n  let valueof = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ascending;\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}", "map": {"version": 3, "names": ["ascending", "ascendingDefined", "compareDefined", "rank", "values", "valueof", "arguments", "length", "undefined", "Symbol", "iterator", "TypeError", "V", "Array", "from", "R", "Float64Array", "map", "compareIndex", "i", "j", "k", "r", "Uint32Array", "_", "sort", "for<PERSON>ach", "c", "NaN"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/d3-array/src/rank.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport {ascendingDefined, compareDefined} from \"./sort.js\";\n\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,SAAQC,gBAAgB,EAAEC,cAAc,QAAO,WAAW;AAE1D,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,SAAS;EACtD,IAAI,OAAOI,MAAM,CAACK,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAIC,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACV,MAAM,CAAC;EAC1B,MAAMW,CAAC,GAAG,IAAIC,YAAY,CAACJ,CAAC,CAACL,MAAM,CAAC;EACpC,IAAIF,OAAO,CAACE,MAAM,KAAK,CAAC,EAAEK,CAAC,GAAGA,CAAC,CAACK,GAAG,CAACZ,OAAO,CAAC,EAAEA,OAAO,GAAGL,SAAS;EACjE,MAAMkB,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKf,OAAO,CAACO,CAAC,CAACO,CAAC,CAAC,EAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC;EAClD,IAAIC,CAAC,EAAEC,CAAC;EACRlB,MAAM,GAAGmB,WAAW,CAACT,IAAI,CAACF,CAAC,EAAE,CAACY,CAAC,EAAEL,CAAC,KAAKA,CAAC,CAAC;EACzC;EACAf,MAAM,CAACqB,IAAI,CAACpB,OAAO,KAAKL,SAAS,GAAG,CAACmB,CAAC,EAAEC,CAAC,KAAKnB,gBAAgB,CAACW,CAAC,CAACO,CAAC,CAAC,EAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC,GAAGlB,cAAc,CAACgB,YAAY,CAAC,CAAC;EAC1Gd,MAAM,CAACsB,OAAO,CAAC,CAACN,CAAC,EAAED,CAAC,KAAK;IACrB,MAAMQ,CAAC,GAAGT,YAAY,CAACE,CAAC,EAAEC,CAAC,KAAKb,SAAS,GAAGY,CAAC,GAAGC,CAAC,CAAC;IAClD,IAAIM,CAAC,IAAI,CAAC,EAAE;MACV,IAAIN,CAAC,KAAKb,SAAS,IAAImB,CAAC,GAAG,CAAC,EAAEN,CAAC,GAAGD,CAAC,EAAEE,CAAC,GAAGH,CAAC;MAC1CJ,CAAC,CAACK,CAAC,CAAC,GAAGE,CAAC;IACV,CAAC,MAAM;MACLP,CAAC,CAACK,CAAC,CAAC,GAAGQ,GAAG;IACZ;EACF,CAAC,CAAC;EACJ,OAAOb,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}