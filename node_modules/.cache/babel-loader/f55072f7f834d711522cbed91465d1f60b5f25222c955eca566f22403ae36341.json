{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { mockApiService } from '@/services/api';\nconst initialState = {\n  quotes: [],\n  currentQuote: null,\n  loading: false,\n  error: null,\n  pagination: {\n    page: 1,\n    pageSize: 20,\n    total: 0,\n    totalPages: 0\n  },\n  shareLink: null,\n  analytics: null\n};\nexport const fetchQuotes = createAsyncThunk('quote/fetchQuotes', async params => {\n  const response = await mockApiService.getQuotes(params);\n  return response;\n});\nexport const createQuote = createAsyncThunk('quote/createQuote', async data => {\n  // 模拟创建报价\n  return {\n    ...data,\n    id: Date.now().toString()\n  };\n});\nexport const shareQuote = createAsyncThunk('quote/shareQuote', async id => {\n  const response = await mockApiService.shareQuote(id);\n  return response.data;\n});\nconst quoteSlice = createSlice({\n  name: 'quote',\n  initialState,\n  reducers: {\n    clearCurrentQuote: state => {\n      state.currentQuote = null;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    clearShareLink: state => {\n      state.shareLink = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchQuotes.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchQuotes.fulfilled, (state, action) => {\n      state.loading = false;\n      state.quotes = action.payload.data;\n    }).addCase(fetchQuotes.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || '获取报价列表失败';\n    }).addCase(createQuote.fulfilled, (state, action) => {\n      state.quotes.unshift(action.payload);\n    }).addCase(shareQuote.fulfilled, (state, action) => {\n      state.shareLink = action.payload;\n    });\n  }\n});\nexport const {\n  clearCurrentQuote,\n  clearError,\n  clearShareLink\n} = quoteSlice.actions;\nexport default quoteSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "mockApiService", "initialState", "quotes", "currentQuote", "loading", "error", "pagination", "page", "pageSize", "total", "totalPages", "shareLink", "analytics", "fetchQuotes", "params", "response", "getQuotes", "createQuote", "data", "id", "Date", "now", "toString", "shareQuote", "quoteSlice", "name", "reducers", "clearCurrentQuote", "state", "clearError", "clearShareLink", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "message", "unshift", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { Quote, PaginationParams } from '@/types';\nimport { mockApiService } from '@/services/api';\n\ninterface QuoteState {\n  quotes: Quote[];\n  currentQuote: Quote | null;\n  loading: boolean;\n  error: string | null;\n  pagination: {\n    page: number;\n    pageSize: number;\n    total: number;\n    totalPages: number;\n  };\n  shareLink: string | null;\n  analytics: any;\n}\n\nconst initialState: QuoteState = {\n  quotes: [],\n  currentQuote: null,\n  loading: false,\n  error: null,\n  pagination: {\n    page: 1,\n    pageSize: 20,\n    total: 0,\n    totalPages: 0,\n  },\n  shareLink: null,\n  analytics: null,\n};\n\nexport const fetchQuotes = createAsyncThunk(\n  'quote/fetchQuotes',\n  async (params: PaginationParams) => {\n    const response = await mockApiService.getQuotes(params);\n    return response;\n  }\n);\n\nexport const createQuote = createAsyncThunk(\n  'quote/createQuote',\n  async (data: Partial<Quote>) => {\n    // 模拟创建报价\n    return { ...data, id: Date.now().toString() } as Quote;\n  }\n);\n\nexport const shareQuote = createAsyncThunk(\n  'quote/shareQuote',\n  async (id: string) => {\n    const response = await mockApiService.shareQuote(id);\n    return response.data;\n  }\n);\n\nconst quoteSlice = createSlice({\n  name: 'quote',\n  initialState,\n  reducers: {\n    clearCurrentQuote: (state) => {\n      state.currentQuote = null;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    clearShareLink: (state) => {\n      state.shareLink = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchQuotes.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchQuotes.fulfilled, (state, action) => {\n        state.loading = false;\n        state.quotes = action.payload.data;\n      })\n      .addCase(fetchQuotes.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || '获取报价列表失败';\n      })\n      .addCase(createQuote.fulfilled, (state, action) => {\n        state.quotes.unshift(action.payload);\n      })\n      .addCase(shareQuote.fulfilled, (state, action) => {\n        state.shareLink = action.payload;\n      });\n  },\n});\n\nexport const { clearCurrentQuote, clearError, clearShareLink } = quoteSlice.actions;\nexport default quoteSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAEhE,SAASC,cAAc,QAAQ,gBAAgB;AAiB/C,MAAMC,YAAwB,GAAG;EAC/BC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE;EACd,CAAC;EACDC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGd,gBAAgB,CACzC,mBAAmB,EACnB,MAAOe,MAAwB,IAAK;EAClC,MAAMC,QAAQ,GAAG,MAAMf,cAAc,CAACgB,SAAS,CAACF,MAAM,CAAC;EACvD,OAAOC,QAAQ;AACjB,CACF,CAAC;AAED,OAAO,MAAME,WAAW,GAAGlB,gBAAgB,CACzC,mBAAmB,EACnB,MAAOmB,IAAoB,IAAK;EAC9B;EACA,OAAO;IAAE,GAAGA,IAAI;IAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC;EAAE,CAAC;AAC/C,CACF,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGxB,gBAAgB,CACxC,kBAAkB,EAClB,MAAOoB,EAAU,IAAK;EACpB,MAAMJ,QAAQ,GAAG,MAAMf,cAAc,CAACuB,UAAU,CAACJ,EAAE,CAAC;EACpD,OAAOJ,QAAQ,CAACG,IAAI;AACtB,CACF,CAAC;AAED,MAAMM,UAAU,GAAG1B,WAAW,CAAC;EAC7B2B,IAAI,EAAE,OAAO;EACbxB,YAAY;EACZyB,QAAQ,EAAE;IACRC,iBAAiB,EAAGC,KAAK,IAAK;MAC5BA,KAAK,CAACzB,YAAY,GAAG,IAAI;IAC3B,CAAC;IACD0B,UAAU,EAAGD,KAAK,IAAK;MACrBA,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDyB,cAAc,EAAGF,KAAK,IAAK;MACzBA,KAAK,CAACjB,SAAS,GAAG,IAAI;IACxB;EACF,CAAC;EACDoB,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACpB,WAAW,CAACqB,OAAO,EAAGN,KAAK,IAAK;MACvCA,KAAK,CAACxB,OAAO,GAAG,IAAI;MACpBwB,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD4B,OAAO,CAACpB,WAAW,CAACsB,SAAS,EAAE,CAACP,KAAK,EAAEQ,MAAM,KAAK;MACjDR,KAAK,CAACxB,OAAO,GAAG,KAAK;MACrBwB,KAAK,CAAC1B,MAAM,GAAGkC,MAAM,CAACC,OAAO,CAACnB,IAAI;IACpC,CAAC,CAAC,CACDe,OAAO,CAACpB,WAAW,CAACyB,QAAQ,EAAE,CAACV,KAAK,EAAEQ,MAAM,KAAK;MAChDR,KAAK,CAACxB,OAAO,GAAG,KAAK;MACrBwB,KAAK,CAACvB,KAAK,GAAG+B,MAAM,CAAC/B,KAAK,CAACkC,OAAO,IAAI,UAAU;IAClD,CAAC,CAAC,CACDN,OAAO,CAAChB,WAAW,CAACkB,SAAS,EAAE,CAACP,KAAK,EAAEQ,MAAM,KAAK;MACjDR,KAAK,CAAC1B,MAAM,CAACsC,OAAO,CAACJ,MAAM,CAACC,OAAO,CAAC;IACtC,CAAC,CAAC,CACDJ,OAAO,CAACV,UAAU,CAACY,SAAS,EAAE,CAACP,KAAK,EAAEQ,MAAM,KAAK;MAChDR,KAAK,CAACjB,SAAS,GAAGyB,MAAM,CAACC,OAAO;IAClC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEV,iBAAiB;EAAEE,UAAU;EAAEC;AAAe,CAAC,GAAGN,UAAU,CAACiB,OAAO;AACnF,eAAejB,UAAU,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}