{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Row, Col, Card, Statistic, Typography, Space, Button, Table, Tag, Progress } from 'antd';\nimport { ArrowUpOutlined, ArrowDownOutlined, FileTextOutlined, DollarOutlined, UserOutlined, RiseOutlined, EyeOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { useAppDispatch, useAppSelector } from '@/store';\nimport { fetchDashboardData } from '@/store/slices/analyticsSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst DashboardPage = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    dashboardData,\n    loading\n  } = useAppSelector(state => state.analytics);\n  const [kingsoftModalVisible, setKingsoftModalVisible] = useState(false);\n\n  // 使用数据\n  console.log('Dashboard data:', dashboardData, 'Loading:', loading);\n  useEffect(() => {\n    dispatch(fetchDashboardData());\n  }, [dispatch]);\n\n  // 模拟数据\n  const salesTrendData = [{\n    month: '1月',\n    sales: 65000,\n    quotes: 120\n  }, {\n    month: '2月',\n    sales: 78000,\n    quotes: 145\n  }, {\n    month: '3月',\n    sales: 92000,\n    quotes: 168\n  }, {\n    month: '4月',\n    sales: 85000,\n    quotes: 155\n  }, {\n    month: '5月',\n    sales: 110000,\n    quotes: 198\n  }, {\n    month: '6月',\n    sales: 125000,\n    quotes: 220\n  }];\n  const productPerformanceData = [{\n    name: '服务器配置',\n    value: 35,\n    color: '#1890ff'\n  }, {\n    name: '网络设备',\n    value: 25,\n    color: '#52c41a'\n  }, {\n    name: '存储系统',\n    value: 20,\n    color: '#faad14'\n  }, {\n    name: '安全设备',\n    value: 15,\n    color: '#f5222d'\n  }, {\n    name: '其他',\n    value: 5,\n    color: '#722ed1'\n  }];\n  const recentQuotesData = [{\n    key: '1',\n    quoteNumber: 'Q-2024-001',\n    customer: '阿里巴巴集团',\n    amount: 125000,\n    status: 'pending',\n    createdAt: '2024-01-15'\n  }, {\n    key: '2',\n    quoteNumber: 'Q-2024-002',\n    customer: '腾讯科技',\n    amount: 89000,\n    status: 'approved',\n    createdAt: '2024-01-14'\n  }, {\n    key: '3',\n    quoteNumber: 'Q-2024-003',\n    customer: '字节跳动',\n    amount: 156000,\n    status: 'sent',\n    createdAt: '2024-01-13'\n  }, {\n    key: '4',\n    quoteNumber: 'Q-2024-004',\n    customer: '美团',\n    amount: 78000,\n    status: 'draft',\n    createdAt: '2024-01-12'\n  }];\n  const topProductsData = [{\n    name: 'Dell PowerEdge R750',\n    sales: 45,\n    revenue: 450000\n  }, {\n    name: 'Cisco Catalyst 9300',\n    sales: 38,\n    revenue: 380000\n  }, {\n    name: 'HPE ProLiant DL380',\n    sales: 32,\n    revenue: 320000\n  }, {\n    name: 'Juniper EX4300',\n    sales: 28,\n    revenue: 280000\n  }, {\n    name: 'NetApp FAS2750',\n    sales: 25,\n    revenue: 250000\n  }];\n  const quotesColumns = [{\n    title: '报价编号',\n    dataIndex: 'quoteNumber',\n    key: 'quoteNumber',\n    render: text => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      style: {\n        padding: 0,\n        height: 'auto'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '客户',\n    dataIndex: 'customer',\n    key: 'customer'\n  }, {\n    title: '金额',\n    dataIndex: 'amount',\n    key: 'amount',\n    render: amount => `¥${amount.toLocaleString()}`\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const statusMap = {\n        draft: {\n          color: 'default',\n          text: '草稿'\n        },\n        pending: {\n          color: 'processing',\n          text: '待审批'\n        },\n        sent: {\n          color: 'warning',\n          text: '已发送'\n        },\n        approved: {\n          color: 'success',\n          text: '已批准'\n        },\n        rejected: {\n          color: 'error',\n          text: '已拒绝'\n        }\n      };\n      const config = statusMap[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: config.color,\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: () => /*#__PURE__*/_jsxDEV(Space, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 50\n        }, this),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: \"\\u4EEA\\u8868\\u677F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\\u8FD9\\u91CC\\u662F\\u60A8\\u7684\\u4E1A\\u52A1\\u6982\\u89C8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u62A5\\u4EF7\\u6570\",\n            value: 1250,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 23\n            }, this),\n            suffix: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: 12,\n                color: '#52c41a'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ArrowUpOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), \" 12%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6536\\u5165\",\n            value: 2500000,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 23\n            }, this),\n            precision: 0,\n            formatter: value => `¥${value === null || value === void 0 ? void 0 : value.toLocaleString()}`,\n            suffix: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: 12,\n                color: '#52c41a'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ArrowUpOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), \" 8%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8F6C\\u5316\\u7387\",\n            value: 35.2,\n            prefix: /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 23\n            }, this),\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u5BA2\\u6237\",\n            value: 186,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 23\n            }, this),\n            suffix: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: 12,\n                color: '#f5222d'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ArrowDownOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), \" 2%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u9500\\u552E\\u8D8B\\u52BF\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 37\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: salesTrendData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"sales\",\n                stroke: \"#1890ff\",\n                strokeWidth: 2,\n                name: \"\\u9500\\u552E\\u989D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"quotes\",\n                stroke: \"#52c41a\",\n                strokeWidth: 2,\n                name: \"\\u62A5\\u4EF7\\u6570\\u91CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4EA7\\u54C1\\u6027\\u80FD\\u5206\\u5E03\",\n          children: [/*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: productPerformanceData,\n                cx: \"50%\",\n                cy: \"50%\",\n                innerRadius: 60,\n                outerRadius: 100,\n                paddingAngle: 5,\n                dataKey: \"value\",\n                children: productPerformanceData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: productPerformanceData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                marginBottom: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: 12,\n                  height: 12,\n                  backgroundColor: item.color,\n                  borderRadius: 2,\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  fontSize: 12\n                },\n                children: [item.name, \": \", item.value, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u62A5\\u4EF7\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 20\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: quotesColumns,\n            dataSource: recentQuotesData,\n            pagination: false,\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u4EA7\\u54C1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: topProductsData.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  marginBottom: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    fontSize: 12\n                  },\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: [product.sales, \"\\u53F0\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: product.sales / 50 * 100,\n                showInfo: false,\n                strokeColor: \"#1890ff\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 11\n                },\n                children: [\"\\u6536\\u5165: \\xA5\", product.revenue.toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"Gzd3MGNTSaLh8bTNngOwmP2wGUE=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Row", "Col", "Card", "Statistic", "Typography", "Space", "<PERSON><PERSON>", "Table", "Tag", "Progress", "ArrowUpOutlined", "ArrowDownOutlined", "FileTextOutlined", "DollarOutlined", "UserOutlined", "RiseOutlined", "EyeOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "useAppDispatch", "useAppSelector", "fetchDashboardData", "jsxDEV", "_jsxDEV", "Title", "Text", "DashboardPage", "_s", "dispatch", "dashboardData", "loading", "state", "analytics", "kingsoftModalVisible", "setKingsoftModalVisible", "console", "log", "salesTrendData", "month", "sales", "quotes", "productPerformanceData", "name", "value", "color", "recentQuotesData", "key", "quoteNumber", "customer", "amount", "status", "createdAt", "topProductsData", "revenue", "quotesColumns", "title", "dataIndex", "render", "text", "type", "style", "padding", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "statusMap", "draft", "pending", "sent", "approved", "rejected", "config", "size", "icon", "className", "marginBottom", "level", "gutter", "xs", "sm", "lg", "prefix", "suffix", "fontSize", "precision", "formatter", "valueStyle", "extra", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "strokeWidth", "cx", "cy", "innerRadius", "outerRadius", "paddingAngle", "map", "entry", "index", "fill", "marginTop", "item", "display", "alignItems", "backgroundColor", "borderRadius", "marginRight", "columns", "dataSource", "pagination", "product", "justifyContent", "strong", "percent", "showInfo", "strokeColor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Row, Col, Card, Statistic, Typography, Space, Button, Table, Tag, Progress, Modal } from 'antd';\nimport {\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n  FileTextOutlined,\n  DollarOutlined,\n  UserOutlined,\n  RiseOutlined,\n  EyeOutlined,\n  CloudOutlined,\n  AppstoreOutlined,\n} from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { useAppDispatch, useAppSelector } from '@/store';\nimport { fetchDashboardData } from '@/store/slices/analyticsSlice';\nimport KingsoftProductShowcase from '@/components/KingsoftProductShowcase';\n\nconst { Title, Text } = Typography;\n\nconst DashboardPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { dashboardData, loading } = useAppSelector((state) => state.analytics);\n  const [kingsoftModalVisible, setKingsoftModalVisible] = useState(false);\n\n  // 使用数据\n  console.log('Dashboard data:', dashboardData, 'Loading:', loading);\n\n  useEffect(() => {\n    dispatch(fetchDashboardData());\n  }, [dispatch]);\n\n  // 模拟数据\n  const salesTrendData = [\n    { month: '1月', sales: 65000, quotes: 120 },\n    { month: '2月', sales: 78000, quotes: 145 },\n    { month: '3月', sales: 92000, quotes: 168 },\n    { month: '4月', sales: 85000, quotes: 155 },\n    { month: '5月', sales: 110000, quotes: 198 },\n    { month: '6月', sales: 125000, quotes: 220 },\n  ];\n\n  const productPerformanceData = [\n    { name: '服务器配置', value: 35, color: '#1890ff' },\n    { name: '网络设备', value: 25, color: '#52c41a' },\n    { name: '存储系统', value: 20, color: '#faad14' },\n    { name: '安全设备', value: 15, color: '#f5222d' },\n    { name: '其他', value: 5, color: '#722ed1' },\n  ];\n\n  const recentQuotesData = [\n    {\n      key: '1',\n      quoteNumber: 'Q-2024-001',\n      customer: '阿里巴巴集团',\n      amount: 125000,\n      status: 'pending',\n      createdAt: '2024-01-15',\n    },\n    {\n      key: '2',\n      quoteNumber: 'Q-2024-002',\n      customer: '腾讯科技',\n      amount: 89000,\n      status: 'approved',\n      createdAt: '2024-01-14',\n    },\n    {\n      key: '3',\n      quoteNumber: 'Q-2024-003',\n      customer: '字节跳动',\n      amount: 156000,\n      status: 'sent',\n      createdAt: '2024-01-13',\n    },\n    {\n      key: '4',\n      quoteNumber: 'Q-2024-004',\n      customer: '美团',\n      amount: 78000,\n      status: 'draft',\n      createdAt: '2024-01-12',\n    },\n  ];\n\n  const topProductsData = [\n    { name: 'Dell PowerEdge R750', sales: 45, revenue: 450000 },\n    { name: 'Cisco Catalyst 9300', sales: 38, revenue: 380000 },\n    { name: 'HPE ProLiant DL380', sales: 32, revenue: 320000 },\n    { name: 'Juniper EX4300', sales: 28, revenue: 280000 },\n    { name: 'NetApp FAS2750', sales: 25, revenue: 250000 },\n  ];\n\n  const quotesColumns = [\n    {\n      title: '报价编号',\n      dataIndex: 'quoteNumber',\n      key: 'quoteNumber',\n      render: (text: string) => <Button type=\"link\" style={{ padding: 0, height: 'auto' }}>{text}</Button>,\n    },\n    {\n      title: '客户',\n      dataIndex: 'customer',\n      key: 'customer',\n    },\n    {\n      title: '金额',\n      dataIndex: 'amount',\n      key: 'amount',\n      render: (amount: number) => `¥${amount.toLocaleString()}`,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const statusMap = {\n          draft: { color: 'default', text: '草稿' },\n          pending: { color: 'processing', text: '待审批' },\n          sent: { color: 'warning', text: '已发送' },\n          approved: { color: 'success', text: '已批准' },\n          rejected: { color: 'error', text: '已拒绝' },\n        };\n        const config = statusMap[status as keyof typeof statusMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: () => (\n        <Space>\n          <Button type=\"link\" size=\"small\" icon={<EyeOutlined />}>\n            查看\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"dashboard-page\">\n      <div style={{ marginBottom: 24 }}>\n        <Title level={2}>仪表板</Title>\n        <Text type=\"secondary\">欢迎回来，这里是您的业务概览</Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总报价数\"\n              value={1250}\n              prefix={<FileTextOutlined />}\n              suffix={\n                <span style={{ fontSize: 12, color: '#52c41a' }}>\n                  <ArrowUpOutlined /> 12%\n                </span>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总收入\"\n              value={2500000}\n              prefix={<DollarOutlined />}\n              precision={0}\n              formatter={(value) => `¥${value?.toLocaleString()}`}\n              suffix={\n                <span style={{ fontSize: 12, color: '#52c41a' }}>\n                  <ArrowUpOutlined /> 8%\n                </span>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"转化率\"\n              value={35.2}\n              prefix={<RiseOutlined />}\n              suffix=\"%\"\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"活跃客户\"\n              value={186}\n              prefix={<UserOutlined />}\n              suffix={\n                <span style={{ fontSize: 12, color: '#f5222d' }}>\n                  <ArrowDownOutlined /> 2%\n                </span>\n              }\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        {/* 销售趋势图 */}\n        <Col xs={24} lg={16}>\n          <Card title=\"销售趋势\" extra={<Button type=\"link\">查看详情</Button>}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={salesTrendData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"sales\" \n                  stroke=\"#1890ff\" \n                  strokeWidth={2}\n                  name=\"销售额\"\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"quotes\" \n                  stroke=\"#52c41a\" \n                  strokeWidth={2}\n                  name=\"报价数量\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n\n        {/* 产品性能分布 */}\n        <Col xs={24} lg={8}>\n          <Card title=\"产品性能分布\">\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={productPerformanceData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  innerRadius={60}\n                  outerRadius={100}\n                  paddingAngle={5}\n                  dataKey=\"value\"\n                >\n                  {productPerformanceData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n            <div style={{ marginTop: 16 }}>\n              {productPerformanceData.map((item, index) => (\n                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>\n                  <div\n                    style={{\n                      width: 12,\n                      height: 12,\n                      backgroundColor: item.color,\n                      borderRadius: 2,\n                      marginRight: 8,\n                    }}\n                  />\n                  <Text style={{ fontSize: 12 }}>{item.name}: {item.value}%</Text>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\n        {/* 最近报价 */}\n        <Col xs={24} lg={16}>\n          <Card \n            title=\"最近报价\" \n            extra={<Button type=\"link\">查看全部</Button>}\n          >\n            <Table\n              columns={quotesColumns}\n              dataSource={recentQuotesData}\n              pagination={false}\n              size=\"small\"\n            />\n          </Card>\n        </Col>\n\n        {/* 热门产品 */}\n        <Col xs={24} lg={8}>\n          <Card title=\"热门产品\">\n            <div>\n              {topProductsData.map((product, index) => (\n                <div key={index} style={{ marginBottom: 16 }}>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>\n                    <Text strong style={{ fontSize: 12 }}>{product.name}</Text>\n                    <Text type=\"secondary\" style={{ fontSize: 12 }}>{product.sales}台</Text>\n                  </div>\n                  <Progress \n                    percent={(product.sales / 50) * 100} \n                    showInfo={false} \n                    strokeColor=\"#1890ff\"\n                    size=\"small\"\n                  />\n                  <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                    收入: ¥{product.revenue.toLocaleString()}\n                  </Text>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,QAAe,MAAM;AACxG,SACEC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,WAAW,QAGN,mBAAmB;AAC1B,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AAC1H,SAASC,cAAc,EAAEC,cAAc,QAAQ,SAAS;AACxD,SAASC,kBAAkB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGnE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG7B,UAAU;AAElC,MAAM8B,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGT,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEU,aAAa;IAAEC;EAAQ,CAAC,GAAGV,cAAc,CAAEW,KAAK,IAAKA,KAAK,CAACC,SAAS,CAAC;EAC7E,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA4C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,aAAa,EAAE,UAAU,EAAEC,OAAO,CAAC;EAElExC,SAAS,CAAC,MAAM;IACdsC,QAAQ,CAACP,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC,EAAE,CAACO,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMS,cAAc,GAAG,CACrB;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC1C;IAAEF,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC1C;IAAEF,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC1C;IAAEF,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC1C;IAAEF,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3C;IAAEF,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAC,CAC5C;EAED,MAAMC,sBAAsB,GAAG,CAC7B;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7C;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7C;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC3C;EAED,MAAMC,gBAAgB,GAAG,CACvB;IACEC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE;EACb,CAAC,CACF;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEV,IAAI,EAAE,qBAAqB;IAAEH,KAAK,EAAE,EAAE;IAAEc,OAAO,EAAE;EAAO,CAAC,EAC3D;IAAEX,IAAI,EAAE,qBAAqB;IAAEH,KAAK,EAAE,EAAE;IAAEc,OAAO,EAAE;EAAO,CAAC,EAC3D;IAAEX,IAAI,EAAE,oBAAoB;IAAEH,KAAK,EAAE,EAAE;IAAEc,OAAO,EAAE;EAAO,CAAC,EAC1D;IAAEX,IAAI,EAAE,gBAAgB;IAAEH,KAAK,EAAE,EAAE;IAAEc,OAAO,EAAE;EAAO,CAAC,EACtD;IAAEX,IAAI,EAAE,gBAAgB;IAAEH,KAAK,EAAE,EAAE;IAAEc,OAAO,EAAE;EAAO,CAAC,CACvD;EAED,MAAMC,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBV,GAAG,EAAE,aAAa;IAClBW,MAAM,EAAGC,IAAY,iBAAKnC,OAAA,CAACzB,MAAM;MAAC6D,IAAI,EAAC,MAAM;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,EAAEL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS;EACrG,CAAC,EACD;IACEZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBV,GAAG,EAAE;EACP,CAAC,EACD;IACES,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBV,GAAG,EAAE,QAAQ;IACbW,MAAM,EAAGR,MAAc,IAAK,IAAIA,MAAM,CAACmB,cAAc,CAAC,CAAC;EACzD,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBV,GAAG,EAAE,QAAQ;IACbW,MAAM,EAAGP,MAAc,IAAK;MAC1B,MAAMmB,SAAS,GAAG;QAChBC,KAAK,EAAE;UAAE1B,KAAK,EAAE,SAAS;UAAEc,IAAI,EAAE;QAAK,CAAC;QACvCa,OAAO,EAAE;UAAE3B,KAAK,EAAE,YAAY;UAAEc,IAAI,EAAE;QAAM,CAAC;QAC7Cc,IAAI,EAAE;UAAE5B,KAAK,EAAE,SAAS;UAAEc,IAAI,EAAE;QAAM,CAAC;QACvCe,QAAQ,EAAE;UAAE7B,KAAK,EAAE,SAAS;UAAEc,IAAI,EAAE;QAAM,CAAC;QAC3CgB,QAAQ,EAAE;UAAE9B,KAAK,EAAE,OAAO;UAAEc,IAAI,EAAE;QAAM;MAC1C,CAAC;MACD,MAAMiB,MAAM,GAAGN,SAAS,CAACnB,MAAM,CAA2B;MAC1D,oBAAO3B,OAAA,CAACvB,GAAG;QAAC4C,KAAK,EAAE+B,MAAM,CAAC/B,KAAM;QAAAmB,QAAA,EAAEY,MAAM,CAACjB;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtD;EACF,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBV,GAAG,EAAE;EACP,CAAC,EACD;IACES,KAAK,EAAE,IAAI;IACXT,GAAG,EAAE,QAAQ;IACbW,MAAM,EAAEA,CAAA,kBACNlC,OAAA,CAAC1B,KAAK;MAAAkE,QAAA,eACJxC,OAAA,CAACzB,MAAM;QAAC6D,IAAI,EAAC,MAAM;QAACiB,IAAI,EAAC,OAAO;QAACC,IAAI,eAAEtD,OAAA,CAACf,WAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACE5C,OAAA;IAAKuD,SAAS,EAAC,gBAAgB;IAAAf,QAAA,gBAC7BxC,OAAA;MAAKqC,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG,CAAE;MAAAhB,QAAA,gBAC/BxC,OAAA,CAACC,KAAK;QAACwD,KAAK,EAAE,CAAE;QAAAjB,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5B5C,OAAA,CAACE,IAAI;QAACkC,IAAI,EAAC,WAAW;QAAAI,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,eAGN5C,OAAA,CAAC/B,GAAG;MAACyF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrB,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG,CAAE;MAAAhB,QAAA,gBACjDxC,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACzBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAAC5B,SAAS;YACR4D,KAAK,EAAC,0BAAM;YACZZ,KAAK,EAAE,IAAK;YACZ0C,MAAM,eAAE9D,OAAA,CAACnB,gBAAgB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BmB,MAAM,eACJ/D,OAAA;cAAMqC,KAAK,EAAE;gBAAE2B,QAAQ,EAAE,EAAE;gBAAE3C,KAAK,EAAE;cAAU,CAAE;cAAAmB,QAAA,gBAC9CxC,OAAA,CAACrB,eAAe;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QACrB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACzBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAAC5B,SAAS;YACR4D,KAAK,EAAC,oBAAK;YACXZ,KAAK,EAAE,OAAQ;YACf0C,MAAM,eAAE9D,OAAA,CAAClB,cAAc;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BqB,SAAS,EAAE,CAAE;YACbC,SAAS,EAAG9C,KAAK,IAAK,IAAIA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,cAAc,CAAC,CAAC,EAAG;YACpDkB,MAAM,eACJ/D,OAAA;cAAMqC,KAAK,EAAE;gBAAE2B,QAAQ,EAAE,EAAE;gBAAE3C,KAAK,EAAE;cAAU,CAAE;cAAAmB,QAAA,gBAC9CxC,OAAA,CAACrB,eAAe;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,OACrB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACzBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAAC5B,SAAS;YACR4D,KAAK,EAAC,oBAAK;YACXZ,KAAK,EAAE,IAAK;YACZ0C,MAAM,eAAE9D,OAAA,CAAChB,YAAY;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBmB,MAAM,EAAC,GAAG;YACVI,UAAU,EAAE;cAAE9C,KAAK,EAAE;YAAU;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACzBxC,OAAA,CAAC7B,IAAI;UAAAqE,QAAA,eACHxC,OAAA,CAAC5B,SAAS;YACR4D,KAAK,EAAC,0BAAM;YACZZ,KAAK,EAAE,GAAI;YACX0C,MAAM,eAAE9D,OAAA,CAACjB,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBmB,MAAM,eACJ/D,OAAA;cAAMqC,KAAK,EAAE;gBAAE2B,QAAQ,EAAE,EAAE;gBAAE3C,KAAK,EAAE;cAAU,CAAE;cAAAmB,QAAA,gBAC9CxC,OAAA,CAACpB,iBAAiB;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,OACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5C,OAAA,CAAC/B,GAAG;MAACyF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAlB,QAAA,gBAEpBxC,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAArB,QAAA,eAClBxC,OAAA,CAAC7B,IAAI;UAAC6D,KAAK,EAAC,0BAAM;UAACoC,KAAK,eAAEpE,OAAA,CAACzB,MAAM;YAAC6D,IAAI,EAAC,MAAM;YAAAI,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAE;UAAAJ,QAAA,eAC1DxC,OAAA,CAACR,mBAAmB;YAAC6E,KAAK,EAAC,MAAM;YAAC9B,MAAM,EAAE,GAAI;YAAAC,QAAA,eAC5CxC,OAAA,CAACd,SAAS;cAACoF,IAAI,EAAExD,cAAe;cAAA0B,QAAA,gBAC9BxC,OAAA,CAACV,aAAa;gBAACiF,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC5C,OAAA,CAACZ,KAAK;gBAACoF,OAAO,EAAC;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB5C,OAAA,CAACX,KAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT5C,OAAA,CAACT,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5C,OAAA,CAACb,IAAI;gBACHiD,IAAI,EAAC,UAAU;gBACfoC,OAAO,EAAC,OAAO;gBACfC,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfvD,IAAI,EAAC;cAAK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACF5C,OAAA,CAACb,IAAI;gBACHiD,IAAI,EAAC,UAAU;gBACfoC,OAAO,EAAC,QAAQ;gBAChBC,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfvD,IAAI,EAAC;cAAM;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5C,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAArB,QAAA,eACjBxC,OAAA,CAAC7B,IAAI;UAAC6D,KAAK,EAAC,sCAAQ;UAAAQ,QAAA,gBAClBxC,OAAA,CAACR,mBAAmB;YAAC6E,KAAK,EAAC,MAAM;YAAC9B,MAAM,EAAE,GAAI;YAAAC,QAAA,eAC5CxC,OAAA,CAACP,QAAQ;cAAA+C,QAAA,gBACPxC,OAAA,CAACN,GAAG;gBACF4E,IAAI,EAAEpD,sBAAuB;gBAC7ByD,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,WAAW,EAAE,EAAG;gBAChBC,WAAW,EAAE,GAAI;gBACjBC,YAAY,EAAE,CAAE;gBAChBP,OAAO,EAAC,OAAO;gBAAAhC,QAAA,EAEdtB,sBAAsB,CAAC8D,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvClF,OAAA,CAACL,IAAI;kBAAuBwF,IAAI,EAAEF,KAAK,CAAC5D;gBAAM,GAAnC,QAAQ6D,KAAK,EAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5C,OAAA,CAACT,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACtB5C,OAAA;YAAKqC,KAAK,EAAE;cAAE+C,SAAS,EAAE;YAAG,CAAE;YAAA5C,QAAA,EAC3BtB,sBAAsB,CAAC8D,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBACtClF,OAAA;cAAiBqC,KAAK,EAAE;gBAAEiD,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE/B,YAAY,EAAE;cAAE,CAAE;cAAAhB,QAAA,gBACjFxC,OAAA;gBACEqC,KAAK,EAAE;kBACLgC,KAAK,EAAE,EAAE;kBACT9B,MAAM,EAAE,EAAE;kBACViD,eAAe,EAAEH,IAAI,CAAChE,KAAK;kBAC3BoE,YAAY,EAAE,CAAC;kBACfC,WAAW,EAAE;gBACf;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5C,OAAA,CAACE,IAAI;gBAACmC,KAAK,EAAE;kBAAE2B,QAAQ,EAAE;gBAAG,CAAE;gBAAAxB,QAAA,GAAE6C,IAAI,CAAClE,IAAI,EAAC,IAAE,EAACkE,IAAI,CAACjE,KAAK,EAAC,GAAC;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAVxDsC,KAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5C,OAAA,CAAC/B,GAAG;MAACyF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrB,KAAK,EAAE;QAAE+C,SAAS,EAAE;MAAG,CAAE;MAAA5C,QAAA,gBAE9CxC,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAArB,QAAA,eAClBxC,OAAA,CAAC7B,IAAI;UACH6D,KAAK,EAAC,0BAAM;UACZoC,KAAK,eAAEpE,OAAA,CAACzB,MAAM;YAAC6D,IAAI,EAAC,MAAM;YAAAI,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAE;UAAAJ,QAAA,eAEzCxC,OAAA,CAACxB,KAAK;YACJmH,OAAO,EAAE5D,aAAc;YACvB6D,UAAU,EAAEtE,gBAAiB;YAC7BuE,UAAU,EAAE,KAAM;YAClBxC,IAAI,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5C,OAAA,CAAC9B,GAAG;QAACyF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAArB,QAAA,eACjBxC,OAAA,CAAC7B,IAAI;UAAC6D,KAAK,EAAC,0BAAM;UAAAQ,QAAA,eAChBxC,OAAA;YAAAwC,QAAA,EACGX,eAAe,CAACmD,GAAG,CAAC,CAACc,OAAO,EAAEZ,KAAK,kBAClClF,OAAA;cAAiBqC,KAAK,EAAE;gBAAEmB,YAAY,EAAE;cAAG,CAAE;cAAAhB,QAAA,gBAC3CxC,OAAA;gBAAKqC,KAAK,EAAE;kBAAEiD,OAAO,EAAE,MAAM;kBAAES,cAAc,EAAE,eAAe;kBAAEvC,YAAY,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,gBAChFxC,OAAA,CAACE,IAAI;kBAAC8F,MAAM;kBAAC3D,KAAK,EAAE;oBAAE2B,QAAQ,EAAE;kBAAG,CAAE;kBAAAxB,QAAA,EAAEsD,OAAO,CAAC3E;gBAAI;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3D5C,OAAA,CAACE,IAAI;kBAACkC,IAAI,EAAC,WAAW;kBAACC,KAAK,EAAE;oBAAE2B,QAAQ,EAAE;kBAAG,CAAE;kBAAAxB,QAAA,GAAEsD,OAAO,CAAC9E,KAAK,EAAC,QAAC;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN5C,OAAA,CAACtB,QAAQ;gBACPuH,OAAO,EAAGH,OAAO,CAAC9E,KAAK,GAAG,EAAE,GAAI,GAAI;gBACpCkF,QAAQ,EAAE,KAAM;gBAChBC,WAAW,EAAC,SAAS;gBACrB9C,IAAI,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACF5C,OAAA,CAACE,IAAI;gBAACkC,IAAI,EAAC,WAAW;gBAACC,KAAK,EAAE;kBAAE2B,QAAQ,EAAE;gBAAG,CAAE;gBAAAxB,QAAA,GAAC,oBACzC,EAACsD,OAAO,CAAChE,OAAO,CAACe,cAAc,CAAC,CAAC;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA,GAbCsC,KAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAhTID,aAAuB;EAAA,QACVP,cAAc,EACIC,cAAc;AAAA;AAAAuG,EAAA,GAF7CjG,aAAuB;AAkT7B,eAAeA,aAAa;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}