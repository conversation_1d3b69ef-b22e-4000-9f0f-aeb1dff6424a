{"ast": null, "code": "import { createBeginDrag } from './beginDrag.js';\nimport { createDrop } from './drop.js';\nimport { createEndDrag } from './endDrag.js';\nimport { createHover } from './hover.js';\nimport { createPublishDragSource } from './publishDragSource.js';\nexport * from './types.js';\nexport function createDragDropActions(manager) {\n  return {\n    beginDrag: createBeginDrag(manager),\n    publishDragSource: createPublishDragSource(manager),\n    hover: createHover(manager),\n    drop: createDrop(manager),\n    endDrag: createEndDrag(manager)\n  };\n}", "map": {"version": 3, "names": ["createBeginDrag", "createDrop", "createEndDrag", "createHover", "createPublishDragSource", "createDragDropActions", "manager", "beginDrag", "publishDragSource", "hover", "drop", "endDrag"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/actions/dragDrop/index.ts"], "sourcesContent": ["import type { DragDropActions, DragDropManager } from '../../interfaces.js'\nimport { createBeginDrag } from './beginDrag.js'\nimport { createDrop } from './drop.js'\nimport { createEndDrag } from './endDrag.js'\nimport { createHover } from './hover.js'\nimport { createPublishDragSource } from './publishDragSource.js'\n\nexport * from './types.js'\n\nexport function createDragDropActions(\n\tmanager: DragDropManager,\n): DragDropActions {\n\treturn {\n\t\tbeginDrag: createBeginDrag(manager),\n\t\tpublishDragSource: createPublishDragSource(manager),\n\t\thover: createHover(manager),\n\t\tdrop: createDrop(manager),\n\t\tendDrag: createEndDrag(manager),\n\t}\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,gBAAgB;AAChD,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,uBAAuB,QAAQ,wBAAwB;AAEhE,cAAc,YAAY;AAE1B,OAAO,SAASC,qBAAqBA,CACpCC,OAAwB,EACN;EAClB,OAAO;IACNC,SAAS,EAAEP,eAAe,CAACM,OAAO,CAAC;IACnCE,iBAAiB,EAAEJ,uBAAuB,CAACE,OAAO,CAAC;IACnDG,KAAK,EAAEN,WAAW,CAACG,OAAO,CAAC;IAC3BI,IAAI,EAAET,UAAU,CAACK,OAAO,CAAC;IACzBK,OAAO,EAAET,aAAa,CAACI,OAAO;GAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}