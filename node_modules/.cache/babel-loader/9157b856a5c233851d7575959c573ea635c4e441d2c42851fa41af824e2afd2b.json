{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { mockApiService } from '@/services/api';\nconst initialState = {\n  dashboardData: null,\n  salesPerformance: null,\n  productPerformance: null,\n  customerBehavior: null,\n  loading: false,\n  error: null\n};\nexport const fetchDashboardData = createAsyncThunk('analytics/fetchDashboardData', async () => {\n  const response = await mockApiService.getDashboardData();\n  return response.data;\n});\nconst analyticsSlice = createSlice({\n  name: 'analytics',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchDashboardData.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchDashboardData.fulfilled, (state, action) => {\n      state.loading = false;\n      state.dashboardData = action.payload;\n    }).addCase(fetchDashboardData.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || '获取仪表板数据失败';\n    });\n  }\n});\nexport const {\n  clearError\n} = analyticsSlice.actions;\nexport default analyticsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "mockApiService", "initialState", "dashboardData", "salesPerformance", "productPerformance", "customerBehavior", "loading", "error", "fetchDashboardData", "response", "getDashboardData", "data", "analyticsSlice", "name", "reducers", "clearError", "state", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "message", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { mockApiService } from '@/services/api';\n\ninterface AnalyticsState {\n  dashboardData: any;\n  salesPerformance: any;\n  productPerformance: any;\n  customerBehavior: any;\n  loading: boolean;\n  error: string | null;\n}\n\nconst initialState: AnalyticsState = {\n  dashboardData: null,\n  salesPerformance: null,\n  productPerformance: null,\n  customerBehavior: null,\n  loading: false,\n  error: null,\n};\n\nexport const fetchDashboardData = createAsyncThunk(\n  'analytics/fetchDashboardData',\n  async () => {\n    const response = await mockApiService.getDashboardData();\n    return response.data;\n  }\n);\n\nconst analyticsSlice = createSlice({\n  name: 'analytics',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchDashboardData.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchDashboardData.fulfilled, (state, action) => {\n        state.loading = false;\n        state.dashboardData = action.payload;\n      })\n      .addCase(fetchDashboardData.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || '获取仪表板数据失败';\n      });\n  },\n});\n\nexport const { clearError } = analyticsSlice.actions;\nexport default analyticsSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,SAASC,cAAc,QAAQ,gBAAgB;AAW/C,MAAMC,YAA4B,GAAG;EACnCC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,IAAI;EACxBC,gBAAgB,EAAE,IAAI;EACtBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAGT,gBAAgB,CAChD,8BAA8B,EAC9B,YAAY;EACV,MAAMU,QAAQ,GAAG,MAAMT,cAAc,CAACU,gBAAgB,CAAC,CAAC;EACxD,OAAOD,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMC,cAAc,GAAGd,WAAW,CAAC;EACjCe,IAAI,EAAE,WAAW;EACjBZ,YAAY;EACZa,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACT,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EACDU,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACX,kBAAkB,CAACY,OAAO,EAAGJ,KAAK,IAAK;MAC9CA,KAAK,CAACV,OAAO,GAAG,IAAI;MACpBU,KAAK,CAACT,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDY,OAAO,CAACX,kBAAkB,CAACa,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACxDN,KAAK,CAACV,OAAO,GAAG,KAAK;MACrBU,KAAK,CAACd,aAAa,GAAGoB,MAAM,CAACC,OAAO;IACtC,CAAC,CAAC,CACDJ,OAAO,CAACX,kBAAkB,CAACgB,QAAQ,EAAE,CAACR,KAAK,EAAEM,MAAM,KAAK;MACvDN,KAAK,CAACV,OAAO,GAAG,KAAK;MACrBU,KAAK,CAACT,KAAK,GAAGe,MAAM,CAACf,KAAK,CAACkB,OAAO,IAAI,WAAW;IACnD,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEV;AAAW,CAAC,GAAGH,cAAc,CAACc,OAAO;AACpD,eAAed,cAAc,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}