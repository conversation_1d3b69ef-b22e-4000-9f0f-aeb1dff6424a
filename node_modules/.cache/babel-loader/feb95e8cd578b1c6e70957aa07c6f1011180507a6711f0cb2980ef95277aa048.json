{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/Link_CPQ/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{createSlice,createAsyncThunk}from'@reduxjs/toolkit';import{mockApiService}from'@/services/api';const initialState={quotes:[],currentQuote:null,loading:false,error:null,pagination:{page:1,pageSize:20,total:0,totalPages:0},shareLink:null,analytics:null};export const fetchQuotes=createAsyncThunk('quote/fetchQuotes',async params=>{const response=await mockApiService.getQuotes(params);return response;});export const createQuote=createAsyncThunk('quote/createQuote',async data=>{// 模拟创建报价\nreturn _objectSpread(_objectSpread({},data),{},{id:Date.now().toString()});});export const shareQuote=createAsyncThunk('quote/shareQuote',async id=>{const response=await mockApiService.shareQuote(id);return response.data;});const quoteSlice=createSlice({name:'quote',initialState,reducers:{clearCurrentQuote:state=>{state.currentQuote=null;},clearError:state=>{state.error=null;},clearShareLink:state=>{state.shareLink=null;}},extraReducers:builder=>{builder.addCase(fetchQuotes.pending,state=>{state.loading=true;state.error=null;}).addCase(fetchQuotes.fulfilled,(state,action)=>{state.loading=false;state.quotes=action.payload.data;}).addCase(fetchQuotes.rejected,(state,action)=>{state.loading=false;state.error=action.error.message||'获取报价列表失败';}).addCase(createQuote.fulfilled,(state,action)=>{state.quotes.unshift(action.payload);}).addCase(shareQuote.fulfilled,(state,action)=>{state.shareLink=action.payload;});}});export const{clearCurrentQuote,clearError,clearShareLink}=quoteSlice.actions;export default quoteSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "mockApiService", "initialState", "quotes", "currentQuote", "loading", "error", "pagination", "page", "pageSize", "total", "totalPages", "shareLink", "analytics", "fetchQuotes", "params", "response", "getQuotes", "createQuote", "data", "_objectSpread", "id", "Date", "now", "toString", "shareQuote", "quoteSlice", "name", "reducers", "clearCurrentQuote", "state", "clearError", "clearShareLink", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "message", "unshift", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { Quote, PaginationParams } from '@/types';\nimport { mockApiService } from '@/services/api';\n\ninterface QuoteState {\n  quotes: Quote[];\n  currentQuote: Quote | null;\n  loading: boolean;\n  error: string | null;\n  pagination: {\n    page: number;\n    pageSize: number;\n    total: number;\n    totalPages: number;\n  };\n  shareLink: string | null;\n  analytics: any;\n}\n\nconst initialState: QuoteState = {\n  quotes: [],\n  currentQuote: null,\n  loading: false,\n  error: null,\n  pagination: {\n    page: 1,\n    pageSize: 20,\n    total: 0,\n    totalPages: 0,\n  },\n  shareLink: null,\n  analytics: null,\n};\n\nexport const fetchQuotes = createAsyncThunk(\n  'quote/fetchQuotes',\n  async (params: PaginationParams) => {\n    const response = await mockApiService.getQuotes(params);\n    return response;\n  }\n);\n\nexport const createQuote = createAsyncThunk(\n  'quote/createQuote',\n  async (data: Partial<Quote>) => {\n    // 模拟创建报价\n    return { ...data, id: Date.now().toString() } as Quote;\n  }\n);\n\nexport const shareQuote = createAsyncThunk(\n  'quote/shareQuote',\n  async (id: string) => {\n    const response = await mockApiService.shareQuote(id);\n    return response.data;\n  }\n);\n\nconst quoteSlice = createSlice({\n  name: 'quote',\n  initialState,\n  reducers: {\n    clearCurrentQuote: (state) => {\n      state.currentQuote = null;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    clearShareLink: (state) => {\n      state.shareLink = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchQuotes.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchQuotes.fulfilled, (state, action) => {\n        state.loading = false;\n        state.quotes = action.payload.data;\n      })\n      .addCase(fetchQuotes.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || '获取报价列表失败';\n      })\n      .addCase(createQuote.fulfilled, (state, action) => {\n        state.quotes.unshift(action.payload);\n      })\n      .addCase(shareQuote.fulfilled, (state, action) => {\n        state.shareLink = action.payload;\n      });\n  },\n});\n\nexport const { clearCurrentQuote, clearError, clearShareLink } = quoteSlice.actions;\nexport default quoteSlice.reducer;\n"], "mappings": "oHAAA,OAASA,WAAW,CAAEC,gBAAgB,KAAQ,kBAAkB,CAEhE,OAASC,cAAc,KAAQ,gBAAgB,CAiB/C,KAAM,CAAAC,YAAwB,CAAG,CAC/BC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,IAAI,CAClBC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,IAAI,CACXC,UAAU,CAAE,CACVC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,CACRC,UAAU,CAAE,CACd,CAAC,CACDC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,IACb,CAAC,CAED,MAAO,MAAM,CAAAC,WAAW,CAAGd,gBAAgB,CACzC,mBAAmB,CACnB,KAAO,CAAAe,MAAwB,EAAK,CAClC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAf,cAAc,CAACgB,SAAS,CAACF,MAAM,CAAC,CACvD,MAAO,CAAAC,QAAQ,CACjB,CACF,CAAC,CAED,MAAO,MAAM,CAAAE,WAAW,CAAGlB,gBAAgB,CACzC,mBAAmB,CACnB,KAAO,CAAAmB,IAAoB,EAAK,CAC9B;AACA,OAAAC,aAAA,CAAAA,aAAA,IAAYD,IAAI,MAAEE,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAC7C,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,UAAU,CAAGzB,gBAAgB,CACxC,kBAAkB,CAClB,KAAO,CAAAqB,EAAU,EAAK,CACpB,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAf,cAAc,CAACwB,UAAU,CAACJ,EAAE,CAAC,CACpD,MAAO,CAAAL,QAAQ,CAACG,IAAI,CACtB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG3B,WAAW,CAAC,CAC7B4B,IAAI,CAAE,OAAO,CACbzB,YAAY,CACZ0B,QAAQ,CAAE,CACRC,iBAAiB,CAAGC,KAAK,EAAK,CAC5BA,KAAK,CAAC1B,YAAY,CAAG,IAAI,CAC3B,CAAC,CACD2B,UAAU,CAAGD,KAAK,EAAK,CACrBA,KAAK,CAACxB,KAAK,CAAG,IAAI,CACpB,CAAC,CACD0B,cAAc,CAAGF,KAAK,EAAK,CACzBA,KAAK,CAAClB,SAAS,CAAG,IAAI,CACxB,CACF,CAAC,CACDqB,aAAa,CAAGC,OAAO,EAAK,CAC1BA,OAAO,CACJC,OAAO,CAACrB,WAAW,CAACsB,OAAO,CAAGN,KAAK,EAAK,CACvCA,KAAK,CAACzB,OAAO,CAAG,IAAI,CACpByB,KAAK,CAACxB,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACD6B,OAAO,CAACrB,WAAW,CAACuB,SAAS,CAAE,CAACP,KAAK,CAAEQ,MAAM,GAAK,CACjDR,KAAK,CAACzB,OAAO,CAAG,KAAK,CACrByB,KAAK,CAAC3B,MAAM,CAAGmC,MAAM,CAACC,OAAO,CAACpB,IAAI,CACpC,CAAC,CAAC,CACDgB,OAAO,CAACrB,WAAW,CAAC0B,QAAQ,CAAE,CAACV,KAAK,CAAEQ,MAAM,GAAK,CAChDR,KAAK,CAACzB,OAAO,CAAG,KAAK,CACrByB,KAAK,CAACxB,KAAK,CAAGgC,MAAM,CAAChC,KAAK,CAACmC,OAAO,EAAI,UAAU,CAClD,CAAC,CAAC,CACDN,OAAO,CAACjB,WAAW,CAACmB,SAAS,CAAE,CAACP,KAAK,CAAEQ,MAAM,GAAK,CACjDR,KAAK,CAAC3B,MAAM,CAACuC,OAAO,CAACJ,MAAM,CAACC,OAAO,CAAC,CACtC,CAAC,CAAC,CACDJ,OAAO,CAACV,UAAU,CAACY,SAAS,CAAE,CAACP,KAAK,CAAEQ,MAAM,GAAK,CAChDR,KAAK,CAAClB,SAAS,CAAG0B,MAAM,CAACC,OAAO,CAClC,CAAC,CAAC,CACN,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEV,iBAAiB,CAAEE,UAAU,CAAEC,cAAe,CAAC,CAAGN,UAAU,CAACiB,OAAO,CACnF,cAAe,CAAAjB,UAAU,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}