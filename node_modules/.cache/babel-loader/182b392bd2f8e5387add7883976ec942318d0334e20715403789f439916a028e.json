{"ast": null, "code": "// 模拟数据文件\n// 模拟用户数据\nexport const mockUsers=[{id:'1',username:'admin',email:'<EMAIL>',firstName:'管理员',lastName:'系统',avatar:'https://via.placeholder.com/64x64?text=Admin',roles:[{id:'admin',name:'系统管理员',description:'拥有系统所有权限',permissions:[],createdAt:'2023-01-01',updatedAt:'2023-01-01'}],permissions:[],status:'active',lastLoginAt:'2024-01-15T10:30:00Z',createdAt:'2023-01-01T00:00:00Z',updatedAt:'2024-01-15T10:30:00Z'},{id:'2',username:'sales001',email:'<EMAIL>',firstName:'李',lastName:'销售',avatar:'https://via.placeholder.com/64x64?text=李销售',roles:[{id:'sales_manager',name:'销售经理',description:'管理销售团队和报价',permissions:[],createdAt:'2023-01-01',updatedAt:'2023-01-01'}],permissions:[],status:'active',lastLoginAt:'2024-01-15T09:15:00Z',createdAt:'2023-03-15T00:00:00Z',updatedAt:'2024-01-15T09:15:00Z'}];// 模拟产品数据\nexport const mockProducts=[{id:'1',name:'Dell PowerEdge R750 服务器',description:'高性能2U机架式服务器，适用于数据中心和企业应用',sku:'DELL-R750-001',category:{id:'1',name:'服务器',description:'企业级服务器设备',level:1,path:'/servers',createdAt:'2024-01-01',updatedAt:'2024-01-01'},specifications:[{id:'1',productId:'1',name:'CPU',value:'Intel Xeon Silver 4314',type:'select',required:true,options:['Intel Xeon Silver 4314','Intel Xeon Gold 5318Y'],createdAt:'2024-01-01',updatedAt:'2024-01-01'},{id:'2',productId:'1',name:'内存',value:'32GB DDR4',type:'select',required:true,options:['16GB DDR4','32GB DDR4','64GB DDR4'],createdAt:'2024-01-01',updatedAt:'2024-01-01'}],images:[{id:'1',productId:'1',url:'https://via.placeholder.com/400x300?text=Dell+R750',alt:'Dell PowerEdge R750',isPrimary:true,order:1,createdAt:'2024-01-01',updatedAt:'2024-01-01'}],basePrice:25000,currency:'CNY',status:'active',tags:['企业级','高性能','数据中心'],version:'1.0',createdAt:'2024-01-01',updatedAt:'2024-01-01'},{id:'2',name:'Cisco Catalyst 9300 交换机',description:'企业级核心交换机，支持高密度端口和先进的安全功能',sku:'CISCO-C9300-001',category:{id:'2',name:'网络设备',description:'网络交换机和路由器',level:1,path:'/network',createdAt:'2024-01-01',updatedAt:'2024-01-01'},specifications:[{id:'3',productId:'2',name:'端口数量',value:'48',type:'select',required:true,options:['24','48'],createdAt:'2024-01-01',updatedAt:'2024-01-01'}],images:[{id:'2',productId:'2',url:'https://via.placeholder.com/400x300?text=Cisco+C9300',alt:'Cisco Catalyst 9300',isPrimary:true,order:1,createdAt:'2024-01-01',updatedAt:'2024-01-01'}],basePrice:18000,currency:'CNY',status:'active',tags:['网络','交换机','企业级'],version:'1.0',createdAt:'2024-01-01',updatedAt:'2024-01-01'}];// 模拟客户数据\nexport const mockCustomers=[{id:'1',name:'张三',email:'<EMAIL>',phone:'+86 138-0013-8000',company:'阿里巴巴集团',address:{street:'文一西路969号',city:'杭州',state:'浙江',country:'中国',postalCode:'310000'},tier:'platinum',creditLimit:5000000,paymentTerms:'30天',salesPersonId:'2',preferences:{currency:'CNY',language:'zh-CN',timezone:'Asia/Shanghai',communicationChannel:'email',notifications:{quoteUpdates:true,promotions:true,newsletters:false}},status:'active',createdAt:'2023-01-15T00:00:00Z',updatedAt:'2024-01-15T10:30:00Z'},{id:'2',name:'李四',email:'<EMAIL>',phone:'+86 138-0013-8001',company:'腾讯科技',address:{street:'科技园南区',city:'深圳',state:'广东',country:'中国',postalCode:'518000'},tier:'gold',creditLimit:3000000,paymentTerms:'15天',salesPersonId:'2',preferences:{currency:'CNY',language:'zh-CN',timezone:'Asia/Shanghai',communicationChannel:'email',notifications:{quoteUpdates:true,promotions:false,newsletters:true}},status:'active',createdAt:'2023-03-20T00:00:00Z',updatedAt:'2024-01-10T14:20:00Z'}];// 模拟报价数据\nexport const mockQuotes=[{id:'1',quoteNumber:'Q-2024-001',customerId:'1',customerName:'阿里巴巴集团',customerEmail:'<EMAIL>',salesPersonId:'2',items:[{id:'1',quoteId:'1',productId:'1',productName:'Dell PowerEdge R750 服务器',configurationId:'config-1',quantity:2,unitPrice:25000,discountPercentage:10,discountAmount:5000,lineTotal:45000,specifications:{'CPU':'Intel Xeon Silver 4314','内存':'32GB DDR4'},createdAt:'2024-01-15',updatedAt:'2024-01-15'}],subtotal:50000,discountAmount:5000,taxAmount:4500,shippingAmount:0,total:49500,currency:'CNY',status:'sent',validUntil:'2024-02-15',notes:'此报价包含标准配置，如需定制请联系销售人员。',terms:'1. 报价有效期30天\\n2. 付款方式：预付30%，发货前付清余款',templateId:'template-1',version:1,parentQuoteId:undefined,createdAt:'2024-01-15T10:30:00Z',updatedAt:'2024-01-15T15:45:00Z'}];// 模拟配置数据\nexport const mockConfigurations=[{id:'config-1',name:'企业级服务器标准配置',description:'适用于中型企业的标准服务器配置方案',productId:'1',items:[{id:'item-1',configurationId:'config-1',productId:'1',quantity:1,specifications:{'CPU':'Intel Xeon Silver 4314','内存':'32GB DDR4'},price:25000,position:{x:0,y:0},createdAt:'2024-01-15',updatedAt:'2024-01-15'}],rules:[],status:'active',version:'1.0',templateId:undefined,createdAt:'2024-01-15T10:30:00Z',updatedAt:'2024-01-15T10:30:00Z'}];// API响应模拟函数\nexport const createMockApiResponse=function(data){let success=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;let message=arguments.length>2?arguments[2]:undefined;return{success,data,message,errors:success?undefined:['模拟错误'],pagination:Array.isArray(data)?{page:1,pageSize:20,total:data.length,totalPages:Math.ceil(data.length/20)}:undefined};};// 分页响应模拟函数\nexport const createMockPaginatedResponse=function(data){let page=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;let pageSize=arguments.length>2&&arguments[2]!==undefined?arguments[2]:20;let success=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;let message=arguments.length>4?arguments[4]:undefined;return{success,data:data.slice((page-1)*pageSize,page*pageSize),message,errors:success?undefined:['模拟错误'],pagination:{page,pageSize,total:data.length,totalPages:Math.ceil(data.length/pageSize)}};};// 模拟登录响应\nexport const mockLoginResponse={success:true,data:{user:mockUsers[0],token:'mock_jwt_token_'+Date.now()},message:'登录成功'};// 模拟延迟函数\nexport const mockDelay=function(){let ms=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1000;return new Promise(resolve=>setTimeout(resolve,ms));};", "map": {"version": 3, "names": ["mockUsers", "id", "username", "email", "firstName", "lastName", "avatar", "roles", "name", "description", "permissions", "createdAt", "updatedAt", "status", "lastLoginAt", "mockProducts", "sku", "category", "level", "path", "specifications", "productId", "value", "type", "required", "options", "images", "url", "alt", "isPrimary", "order", "basePrice", "currency", "tags", "version", "mockCustomers", "phone", "company", "address", "street", "city", "state", "country", "postalCode", "tier", "creditLimit", "paymentTerms", "salesPersonId", "preferences", "language", "timezone", "communicationChannel", "notifications", "quoteUpdates", "promotions", "newsletters", "mockQuotes", "quoteNumber", "customerId", "customerName", "customerEmail", "items", "quoteId", "productName", "configurationId", "quantity", "unitPrice", "discountPercentage", "discountAmount", "lineTotal", "subtotal", "taxAmount", "shippingAmount", "total", "validUntil", "notes", "terms", "templateId", "parentQuoteId", "undefined", "mockConfigurations", "price", "position", "x", "y", "rules", "createMockApiResponse", "data", "success", "arguments", "length", "message", "errors", "pagination", "Array", "isArray", "page", "pageSize", "totalPages", "Math", "ceil", "createMockPaginatedResponse", "slice", "mockLoginResponse", "user", "token", "Date", "now", "mockDelay", "ms", "Promise", "resolve", "setTimeout"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts"], "sourcesContent": ["// 模拟数据文件\nimport { User, Product, Configuration, Quote, Customer } from '@/types';\n\n// 模拟用户数据\nexport const mockUsers: User[] = [\n  {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    firstName: '管理员',\n    lastName: '系统',\n    avatar: 'https://via.placeholder.com/64x64?text=Admin',\n    roles: [\n      {\n        id: 'admin',\n        name: '系统管理员',\n        description: '拥有系统所有权限',\n        permissions: [],\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n      },\n    ],\n    permissions: [],\n    status: 'active',\n    lastLoginAt: '2024-01-15T10:30:00Z',\n    createdAt: '2023-01-01T00:00:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n  {\n    id: '2',\n    username: 'sales001',\n    email: '<EMAIL>',\n    firstName: '李',\n    lastName: '销售',\n    avatar: 'https://via.placeholder.com/64x64?text=李销售',\n    roles: [\n      {\n        id: 'sales_manager',\n        name: '销售经理',\n        description: '管理销售团队和报价',\n        permissions: [],\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n      },\n    ],\n    permissions: [],\n    status: 'active',\n    lastLoginAt: '2024-01-15T09:15:00Z',\n    createdAt: '2023-03-15T00:00:00Z',\n    updatedAt: '2024-01-15T09:15:00Z',\n  },\n];\n\n// 模拟产品数据\nexport const mockProducts: Product[] = [\n  {\n    id: '1',\n    name: 'Dell PowerEdge R750 服务器',\n    description: '高性能2U机架式服务器，适用于数据中心和企业应用',\n    sku: 'DELL-R750-001',\n    category: {\n      id: '1',\n      name: '服务器',\n      description: '企业级服务器设备',\n      level: 1,\n      path: '/servers',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '1',\n        productId: '1',\n        name: 'CPU',\n        value: 'Intel Xeon Silver 4314',\n        type: 'select',\n        required: true,\n        options: ['Intel Xeon Silver 4314', 'Intel Xeon Gold 5318Y'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '2',\n        productId: '1',\n        name: '内存',\n        value: '32GB DDR4',\n        type: 'select',\n        required: true,\n        options: ['16GB DDR4', '32GB DDR4', '64GB DDR4'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '1',\n        productId: '1',\n        url: 'https://via.placeholder.com/400x300?text=Dell+R750',\n        alt: 'Dell PowerEdge R750',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 25000,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['企业级', '高性能', '数据中心'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n  {\n    id: '2',\n    name: 'Cisco Catalyst 9300 交换机',\n    description: '企业级核心交换机，支持高密度端口和先进的安全功能',\n    sku: 'CISCO-C9300-001',\n    category: {\n      id: '2',\n      name: '网络设备',\n      description: '网络交换机和路由器',\n      level: 1,\n      path: '/network',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '3',\n        productId: '2',\n        name: '端口数量',\n        value: '48',\n        type: 'select',\n        required: true,\n        options: ['24', '48'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '2',\n        productId: '2',\n        url: 'https://via.placeholder.com/400x300?text=Cisco+C9300',\n        alt: 'Cisco Catalyst 9300',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 18000,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['网络', '交换机', '企业级'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n];\n\n// 模拟客户数据\nexport const mockCustomers: Customer[] = [\n  {\n    id: '1',\n    name: '张三',\n    email: '<EMAIL>',\n    phone: '+86 138-0013-8000',\n    company: '阿里巴巴集团',\n    address: {\n      street: '文一西路969号',\n      city: '杭州',\n      state: '浙江',\n      country: '中国',\n      postalCode: '310000',\n    },\n    tier: 'platinum',\n    creditLimit: 5000000,\n    paymentTerms: '30天',\n    salesPersonId: '2',\n    preferences: {\n      currency: 'CNY',\n      language: 'zh-CN',\n      timezone: 'Asia/Shanghai',\n      communicationChannel: 'email',\n      notifications: {\n        quoteUpdates: true,\n        promotions: true,\n        newsletters: false,\n      },\n    },\n    status: 'active',\n    createdAt: '2023-01-15T00:00:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '+86 138-0013-8001',\n    company: '腾讯科技',\n    address: {\n      street: '科技园南区',\n      city: '深圳',\n      state: '广东',\n      country: '中国',\n      postalCode: '518000',\n    },\n    tier: 'gold',\n    creditLimit: 3000000,\n    paymentTerms: '15天',\n    salesPersonId: '2',\n    preferences: {\n      currency: 'CNY',\n      language: 'zh-CN',\n      timezone: 'Asia/Shanghai',\n      communicationChannel: 'email',\n      notifications: {\n        quoteUpdates: true,\n        promotions: false,\n        newsletters: true,\n      },\n    },\n    status: 'active',\n    createdAt: '2023-03-20T00:00:00Z',\n    updatedAt: '2024-01-10T14:20:00Z',\n  },\n];\n\n// 模拟报价数据\nexport const mockQuotes: Quote[] = [\n  {\n    id: '1',\n    quoteNumber: 'Q-2024-001',\n    customerId: '1',\n    customerName: '阿里巴巴集团',\n    customerEmail: '<EMAIL>',\n    salesPersonId: '2',\n    items: [\n      {\n        id: '1',\n        quoteId: '1',\n        productId: '1',\n        productName: 'Dell PowerEdge R750 服务器',\n        configurationId: 'config-1',\n        quantity: 2,\n        unitPrice: 25000,\n        discountPercentage: 10,\n        discountAmount: 5000,\n        lineTotal: 45000,\n        specifications: {\n          'CPU': 'Intel Xeon Silver 4314',\n          '内存': '32GB DDR4',\n        },\n        createdAt: '2024-01-15',\n        updatedAt: '2024-01-15',\n      },\n    ],\n    subtotal: 50000,\n    discountAmount: 5000,\n    taxAmount: 4500,\n    shippingAmount: 0,\n    total: 49500,\n    currency: 'CNY',\n    status: 'sent',\n    validUntil: '2024-02-15',\n    notes: '此报价包含标准配置，如需定制请联系销售人员。',\n    terms: '1. 报价有效期30天\\n2. 付款方式：预付30%，发货前付清余款',\n    templateId: 'template-1',\n    version: 1,\n    parentQuoteId: undefined,\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T15:45:00Z',\n  },\n];\n\n// 模拟配置数据\nexport const mockConfigurations: Configuration[] = [\n  {\n    id: 'config-1',\n    name: '企业级服务器标准配置',\n    description: '适用于中型企业的标准服务器配置方案',\n    productId: '1',\n    items: [\n      {\n        id: 'item-1',\n        configurationId: 'config-1',\n        productId: '1',\n        quantity: 1,\n        specifications: {\n          'CPU': 'Intel Xeon Silver 4314',\n          '内存': '32GB DDR4',\n        },\n        price: 25000,\n        position: { x: 0, y: 0 },\n        createdAt: '2024-01-15',\n        updatedAt: '2024-01-15',\n      },\n    ],\n    rules: [],\n    status: 'active',\n    version: '1.0',\n    templateId: undefined,\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n];\n\n// API响应模拟函数\nexport const createMockApiResponse = <T>(data: T, success = true, message?: string) => ({\n  success,\n  data,\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: Array.isArray(data) ? {\n    page: 1,\n    pageSize: 20,\n    total: (data as any[]).length,\n    totalPages: Math.ceil((data as any[]).length / 20),\n  } : undefined,\n});\n\n// 分页响应模拟函数\nexport const createMockPaginatedResponse = <T>(\n  data: T[],\n  page: number = 1,\n  pageSize: number = 20,\n  success = true,\n  message?: string\n) => ({\n  success,\n  data: data.slice((page - 1) * pageSize, page * pageSize),\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: {\n    page,\n    pageSize,\n    total: data.length,\n    totalPages: Math.ceil(data.length / pageSize),\n  },\n});\n\n// 模拟登录响应\nexport const mockLoginResponse = {\n  success: true,\n  data: {\n    user: mockUsers[0],\n    token: 'mock_jwt_token_' + Date.now(),\n  },\n  message: '登录成功',\n};\n\n// 模拟延迟函数\nexport const mockDelay = (ms: number = 1000) => \n  new Promise(resolve => setTimeout(resolve, ms));\n"], "mappings": "AAAA;AAGA;AACA,MAAO,MAAM,CAAAA,SAAiB,CAAG,CAC/B,CACEC,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,mBAAmB,CAC1BC,SAAS,CAAE,KAAK,CAChBC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,8CAA8C,CACtDC,KAAK,CAAE,CACL,CACEN,EAAE,CAAE,OAAO,CACXO,IAAI,CAAE,OAAO,CACbC,WAAW,CAAE,UAAU,CACvBC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDF,WAAW,CAAE,EAAE,CACfG,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,sBAAsB,CACnCH,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACD,CACEX,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,UAAU,CACpBC,KAAK,CAAE,sBAAsB,CAC7BC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,4CAA4C,CACpDC,KAAK,CAAE,CACL,CACEN,EAAE,CAAE,eAAe,CACnBO,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,WAAW,CACxBC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDF,WAAW,CAAE,EAAE,CACfG,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,sBAAsB,CACnCH,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAAG,YAAuB,CAAG,CACrC,CACEd,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,yBAAyB,CAC/BC,WAAW,CAAE,0BAA0B,CACvCO,GAAG,CAAE,eAAe,CACpBC,QAAQ,CAAE,CACRhB,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,UAAU,CACvBS,KAAK,CAAE,CAAC,CACRC,IAAI,CAAE,UAAU,CAChBR,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACDQ,cAAc,CAAE,CACd,CACEnB,EAAE,CAAE,GAAG,CACPoB,SAAS,CAAE,GAAG,CACdb,IAAI,CAAE,KAAK,CACXc,KAAK,CAAE,wBAAwB,CAC/BC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,IAAI,CACdC,OAAO,CAAE,CAAC,wBAAwB,CAAE,uBAAuB,CAAC,CAC5Dd,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACD,CACEX,EAAE,CAAE,GAAG,CACPoB,SAAS,CAAE,GAAG,CACdb,IAAI,CAAE,IAAI,CACVc,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,IAAI,CACdC,OAAO,CAAE,CAAC,WAAW,CAAE,WAAW,CAAE,WAAW,CAAC,CAChDd,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDc,MAAM,CAAE,CACN,CACEzB,EAAE,CAAE,GAAG,CACPoB,SAAS,CAAE,GAAG,CACdM,GAAG,CAAE,oDAAoD,CACzDC,GAAG,CAAE,qBAAqB,CAC1BC,SAAS,CAAE,IAAI,CACfC,KAAK,CAAE,CAAC,CACRnB,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDmB,SAAS,CAAE,KAAK,CAChBC,QAAQ,CAAE,KAAK,CACfnB,MAAM,CAAE,QAAQ,CAChBoB,IAAI,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CAC5BC,OAAO,CAAE,KAAK,CACdvB,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACD,CACEX,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,yBAAyB,CAC/BC,WAAW,CAAE,0BAA0B,CACvCO,GAAG,CAAE,iBAAiB,CACtBC,QAAQ,CAAE,CACRhB,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,WAAW,CACxBS,KAAK,CAAE,CAAC,CACRC,IAAI,CAAE,UAAU,CAChBR,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACDQ,cAAc,CAAE,CACd,CACEnB,EAAE,CAAE,GAAG,CACPoB,SAAS,CAAE,GAAG,CACdb,IAAI,CAAE,MAAM,CACZc,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,IAAI,CACdC,OAAO,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CACrBd,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDc,MAAM,CAAE,CACN,CACEzB,EAAE,CAAE,GAAG,CACPoB,SAAS,CAAE,GAAG,CACdM,GAAG,CAAE,sDAAsD,CAC3DC,GAAG,CAAE,qBAAqB,CAC1BC,SAAS,CAAE,IAAI,CACfC,KAAK,CAAE,CAAC,CACRnB,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDmB,SAAS,CAAE,KAAK,CAChBC,QAAQ,CAAE,KAAK,CACfnB,MAAM,CAAE,QAAQ,CAChBoB,IAAI,CAAE,CAAC,IAAI,CAAE,KAAK,CAAE,KAAK,CAAC,CAC1BC,OAAO,CAAE,KAAK,CACdvB,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAAuB,aAAyB,CAAG,CACvC,CACElC,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,IAAI,CACVL,KAAK,CAAE,sBAAsB,CAC7BiC,KAAK,CAAE,mBAAmB,CAC1BC,OAAO,CAAE,QAAQ,CACjBC,OAAO,CAAE,CACPC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,QACd,CAAC,CACDC,IAAI,CAAE,UAAU,CAChBC,WAAW,CAAE,OAAO,CACpBC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,GAAG,CAClBC,WAAW,CAAE,CACXhB,QAAQ,CAAE,KAAK,CACfiB,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,eAAe,CACzBC,oBAAoB,CAAE,OAAO,CAC7BC,aAAa,CAAE,CACbC,YAAY,CAAE,IAAI,CAClBC,UAAU,CAAE,IAAI,CAChBC,WAAW,CAAE,KACf,CACF,CAAC,CACD1C,MAAM,CAAE,QAAQ,CAChBF,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACD,CACEX,EAAE,CAAE,GAAG,CACPO,IAAI,CAAE,IAAI,CACVL,KAAK,CAAE,kBAAkB,CACzBiC,KAAK,CAAE,mBAAmB,CAC1BC,OAAO,CAAE,MAAM,CACfC,OAAO,CAAE,CACPC,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,QACd,CAAC,CACDC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,OAAO,CACpBC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,GAAG,CAClBC,WAAW,CAAE,CACXhB,QAAQ,CAAE,KAAK,CACfiB,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,eAAe,CACzBC,oBAAoB,CAAE,OAAO,CAC7BC,aAAa,CAAE,CACbC,YAAY,CAAE,IAAI,CAClBC,UAAU,CAAE,KAAK,CACjBC,WAAW,CAAE,IACf,CACF,CAAC,CACD1C,MAAM,CAAE,QAAQ,CAChBF,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAA4C,UAAmB,CAAG,CACjC,CACEvD,EAAE,CAAE,GAAG,CACPwD,WAAW,CAAE,YAAY,CACzBC,UAAU,CAAE,GAAG,CACfC,YAAY,CAAE,QAAQ,CACtBC,aAAa,CAAE,yBAAyB,CACxCb,aAAa,CAAE,GAAG,CAClBc,KAAK,CAAE,CACL,CACE5D,EAAE,CAAE,GAAG,CACP6D,OAAO,CAAE,GAAG,CACZzC,SAAS,CAAE,GAAG,CACd0C,WAAW,CAAE,yBAAyB,CACtCC,eAAe,CAAE,UAAU,CAC3BC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,KAAK,CAChBC,kBAAkB,CAAE,EAAE,CACtBC,cAAc,CAAE,IAAI,CACpBC,SAAS,CAAE,KAAK,CAChBjD,cAAc,CAAE,CACd,KAAK,CAAE,wBAAwB,CAC/B,IAAI,CAAE,WACR,CAAC,CACDT,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACD0D,QAAQ,CAAE,KAAK,CACfF,cAAc,CAAE,IAAI,CACpBG,SAAS,CAAE,IAAI,CACfC,cAAc,CAAE,CAAC,CACjBC,KAAK,CAAE,KAAK,CACZzC,QAAQ,CAAE,KAAK,CACfnB,MAAM,CAAE,MAAM,CACd6D,UAAU,CAAE,YAAY,CACxBC,KAAK,CAAE,wBAAwB,CAC/BC,KAAK,CAAE,oCAAoC,CAC3CC,UAAU,CAAE,YAAY,CACxB3C,OAAO,CAAE,CAAC,CACV4C,aAAa,CAAEC,SAAS,CACxBpE,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAAoE,kBAAmC,CAAG,CACjD,CACE/E,EAAE,CAAE,UAAU,CACdO,IAAI,CAAE,YAAY,CAClBC,WAAW,CAAE,mBAAmB,CAChCY,SAAS,CAAE,GAAG,CACdwC,KAAK,CAAE,CACL,CACE5D,EAAE,CAAE,QAAQ,CACZ+D,eAAe,CAAE,UAAU,CAC3B3C,SAAS,CAAE,GAAG,CACd4C,QAAQ,CAAE,CAAC,CACX7C,cAAc,CAAE,CACd,KAAK,CAAE,wBAAwB,CAC/B,IAAI,CAAE,WACR,CAAC,CACD6D,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CACxBzE,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CACDyE,KAAK,CAAE,EAAE,CACTxE,MAAM,CAAE,QAAQ,CAChBqB,OAAO,CAAE,KAAK,CACd2C,UAAU,CAAEE,SAAS,CACrBpE,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAA0E,qBAAqB,CAAG,QAAAA,CAAIC,IAAO,KAAE,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAV,SAAA,CAAAU,SAAA,IAAG,IAAI,IAAE,CAAAE,OAAgB,CAAAF,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAV,SAAA,OAAM,CACtFS,OAAO,CACPD,IAAI,CACJI,OAAO,CACPC,MAAM,CAAEJ,OAAO,CAAGT,SAAS,CAAG,CAAC,MAAM,CAAC,CACtCc,UAAU,CAAEC,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC,CAAG,CAChCS,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EAAE,CACZxB,KAAK,CAAGc,IAAI,CAAWG,MAAM,CAC7BQ,UAAU,CAAEC,IAAI,CAACC,IAAI,CAAEb,IAAI,CAAWG,MAAM,CAAG,EAAE,CACnD,CAAC,CAAGX,SACN,CAAC,EAAC,CAEF;AACA,MAAO,MAAM,CAAAsB,2BAA2B,CAAG,QAAAA,CACzCd,IAAS,KACT,CAAAS,IAAY,CAAAP,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAV,SAAA,CAAAU,SAAA,IAAG,CAAC,IAChB,CAAAQ,QAAgB,CAAAR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAV,SAAA,CAAAU,SAAA,IAAG,EAAE,IACrB,CAAAD,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAV,SAAA,CAAAU,SAAA,IAAG,IAAI,IACd,CAAAE,OAAgB,CAAAF,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAV,SAAA,OACZ,CACJS,OAAO,CACPD,IAAI,CAAEA,IAAI,CAACe,KAAK,CAAC,CAACN,IAAI,CAAG,CAAC,EAAIC,QAAQ,CAAED,IAAI,CAAGC,QAAQ,CAAC,CACxDN,OAAO,CACPC,MAAM,CAAEJ,OAAO,CAAGT,SAAS,CAAG,CAAC,MAAM,CAAC,CACtCc,UAAU,CAAE,CACVG,IAAI,CACJC,QAAQ,CACRxB,KAAK,CAAEc,IAAI,CAACG,MAAM,CAClBQ,UAAU,CAAEC,IAAI,CAACC,IAAI,CAACb,IAAI,CAACG,MAAM,CAAGO,QAAQ,CAC9C,CACF,CAAC,EAAC,CAEF;AACA,MAAO,MAAM,CAAAM,iBAAiB,CAAG,CAC/Bf,OAAO,CAAE,IAAI,CACbD,IAAI,CAAE,CACJiB,IAAI,CAAExG,SAAS,CAAC,CAAC,CAAC,CAClByG,KAAK,CAAE,iBAAiB,CAAGC,IAAI,CAACC,GAAG,CAAC,CACtC,CAAC,CACDhB,OAAO,CAAE,MACX,CAAC,CAED;AACA,MAAO,MAAM,CAAAiB,SAAS,CAAG,QAAAA,CAAA,KAAC,CAAAC,EAAU,CAAApB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAV,SAAA,CAAAU,SAAA,IAAG,IAAI,OACzC,IAAI,CAAAqB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAEF,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}