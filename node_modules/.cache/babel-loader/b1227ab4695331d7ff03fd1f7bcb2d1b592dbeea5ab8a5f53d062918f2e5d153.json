{"ast": null, "code": "import axios from 'axios';\nimport { mockUsers, mockProducts, mockCustomers, mockQuotes, mockConfigurations, createMockApiResponse, mockLoginResponse, mockDelay } from '@/data/mockData';\n\n// API基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';\nclass ApiService {\n  constructor() {\n    this.instance = void 0;\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(config => {\n      const token = localStorage.getItem('access_token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        localStorage.removeItem('access_token');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // 通用请求方法\n  async request(config) {\n    try {\n      const response = await this.instance.request(config);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || '请求失败');\n    }\n  }\n\n  // GET请求\n  async get(url, params) {\n    return this.request({\n      method: 'GET',\n      url,\n      params\n    });\n  }\n\n  // POST请求\n  async post(url, data) {\n    return this.request({\n      method: 'POST',\n      url,\n      data\n    });\n  }\n\n  // PUT请求\n  async put(url, data) {\n    return this.request({\n      method: 'PUT',\n      url,\n      data\n    });\n  }\n\n  // DELETE请求\n  async delete(url) {\n    return this.request({\n      method: 'DELETE',\n      url\n    });\n  }\n\n  // 分页查询\n  async getPaginated(url, params) {\n    return this.get(url, params);\n  }\n\n  // 文件上传\n  async upload(url, file, onProgress) {\n    const formData = new FormData();\n    formData.append('file', file);\n    return this.request({\n      method: 'POST',\n      url,\n      data: formData,\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: progressEvent => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(progress);\n        }\n      }\n    });\n  }\n\n  // 文件下载\n  async download(url, filename) {\n    try {\n      const response = await this.instance.get(url, {\n        responseType: 'blob'\n      });\n      const blob = new Blob([response.data]);\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename || 'download';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || '下载失败');\n    }\n  }\n}\n\n// 创建API服务实例\nexport const apiService = new ApiService();\nclass MockApiService {\n  // 模拟登录\n  async login(credentials) {\n    await mockDelay(1000);\n    if (credentials.username === 'admin' && credentials.password === 'admin123') {\n      return mockLoginResponse;\n    }\n    throw new Error('用户名或密码错误');\n  }\n\n  // 模拟获取当前用户\n  async getCurrentUser() {\n    await mockDelay(500);\n    return createMockApiResponse(mockUsers[0]);\n  }\n\n  // 模拟产品API\n  async getProducts(params) {\n    await mockDelay(800);\n    return createMockApiResponse(mockProducts);\n  }\n  async getProduct(id) {\n    await mockDelay(500);\n    const product = mockProducts.find(p => p.id === id);\n    if (!product) throw new Error('产品未找到');\n    return createMockApiResponse(product);\n  }\n\n  // 模拟客户API\n  async getCustomers(params) {\n    await mockDelay(600);\n    return createMockApiResponse(mockCustomers);\n  }\n\n  // 模拟报价API\n  async getQuotes(params) {\n    await mockDelay(700);\n    return createMockApiResponse(mockQuotes);\n  }\n  async shareQuote(id) {\n    await mockDelay(500);\n    const shareLink = `https://cpq.example.com/shared/${id}/${Date.now()}`;\n    return createMockApiResponse(shareLink);\n  }\n\n  // 模拟配置API\n  async getConfigurations(params) {\n    await mockDelay(600);\n    return createMockApiResponse(mockConfigurations);\n  }\n\n  // 模拟仪表板数据\n  async getDashboardData() {\n    await mockDelay(1000);\n    return createMockApiResponse({\n      totalQuotes: 1250,\n      totalRevenue: 2500000,\n      conversionRate: 0.35,\n      averageQuoteValue: 2000,\n      recentQuotes: mockQuotes.slice(0, 5),\n      topProducts: mockProducts.slice(0, 5),\n      salesTrend: [{\n        month: '1月',\n        sales: 65000,\n        quotes: 120\n      }, {\n        month: '2月',\n        sales: 78000,\n        quotes: 145\n      }, {\n        month: '3月',\n        sales: 92000,\n        quotes: 168\n      }]\n    });\n  }\n}\n\n// 根据环境选择API服务\nconst isDevelopment = process.env.NODE_ENV === 'development';\nexport const mockApiService = new MockApiService();\n\n// 产品相关API\nexport const productApi = {\n  // 产品分类\n  getCategories: () => apiService.get('/products/categories'),\n  createCategory: data => apiService.post('/products/categories', data),\n  updateCategory: (id, data) => apiService.put(`/products/categories/${id}`, data),\n  deleteCategory: id => apiService.delete(`/products/categories/${id}`),\n  // 产品管理\n  getProducts: params => apiService.getPaginated('/products', params),\n  getProduct: id => apiService.get(`/products/${id}`),\n  createProduct: data => apiService.post('/products', data),\n  updateProduct: (id, data) => apiService.put(`/products/${id}`, data),\n  deleteProduct: id => apiService.delete(`/products/${id}`),\n  uploadProductImage: (id, file) => apiService.upload(`/products/${id}/images`, file),\n  getProductSpecifications: id => apiService.get(`/products/${id}/specifications`),\n  updateProductSpecifications: (id, data) => apiService.put(`/products/${id}/specifications`, data)\n};\n\n// 配置相关API\nexport const configurationApi = {\n  getConfigurations: params => apiService.getPaginated('/configurations', params),\n  getConfiguration: id => apiService.get(`/configurations/${id}`),\n  createConfiguration: data => apiService.post('/configurations', data),\n  updateConfiguration: (id, data) => apiService.put(`/configurations/${id}`, data),\n  deleteConfiguration: id => apiService.delete(`/configurations/${id}`),\n  validateConfiguration: data => apiService.post('/configurations/validate', data),\n  getConfigurationTemplates: () => apiService.get('/configurations/templates'),\n  createConfigurationTemplate: data => apiService.post('/configurations/templates', data),\n  cloneConfiguration: id => apiService.post(`/configurations/${id}/clone`),\n  getConfigurationPreview: id => apiService.get(`/configurations/${id}/preview`),\n  // 智能配置\n  smartBuild: data => apiService.post('/configurations/smart-build', data),\n  aiRecommend: data => apiService.post('/configurations/ai-recommend', data),\n  optimize: data => apiService.post('/configurations/optimize', data),\n  simulate: data => apiService.post('/configurations/simulate', data),\n  checkCompatibility: data => apiService.get('/configurations/compatibility', data),\n  autoComplete: data => apiService.post('/configurations/auto-complete', data),\n  getAlternatives: id => apiService.get(`/configurations/alternatives`, {\n    configurationId: id\n  }),\n  costOptimize: data => apiService.post('/configurations/cost-optimize', data),\n  performanceTest: data => apiService.post('/configurations/performance-test', data),\n  get3DModel: id => apiService.get(`/configurations/${id}/3d-model`),\n  // 高级配置场景\n  scenarioBased: data => apiService.post('/configurations/scenario-based', data),\n  multiStep: data => apiService.post('/configurations/multi-step', data),\n  conditional: data => apiService.post('/configurations/conditional', data),\n  getDependencies: id => apiService.get(`/configurations/dependencies`, {\n    configurationId: id\n  }),\n  validateConstraints: data => apiService.post('/configurations/constraints', data),\n  bulkCreate: data => apiService.post('/configurations/bulk-create', data),\n  clone: data => apiService.post('/configurations/clone', data),\n  migrate: data => apiService.post('/configurations/migrate', data),\n  getAuditTrail: id => apiService.get(`/configurations/audit-trail`, {\n    configurationId: id\n  }),\n  rollback: data => apiService.post('/configurations/rollback', data),\n  impactAnalysis: data => apiService.get('/configurations/impact-analysis', data),\n  merge: data => apiService.post('/configurations/merge', data)\n};\n\n// 配置规则API\nexport const configurationRuleApi = {\n  getRules: params => apiService.getPaginated('/configuration-rules', params),\n  getRule: id => apiService.get(`/configuration-rules/${id}`),\n  createRule: data => apiService.post('/configuration-rules', data),\n  updateRule: (id, data) => apiService.put(`/configuration-rules/${id}`, data),\n  deleteRule: id => apiService.delete(`/configuration-rules/${id}`),\n  validateRule: data => apiService.post('/configuration-rules/validate', data),\n  testRule: data => apiService.post('/configuration-rules/test', data),\n  getConflicts: () => apiService.get('/configuration-rules/conflicts')\n};\n\n// 套餐组合API\nexport const bundleApi = {\n  getBundles: params => apiService.getPaginated('/bundles', params),\n  getBundle: id => apiService.get(`/bundles/${id}`),\n  createBundle: data => apiService.post('/bundles', data),\n  updateBundle: (id, data) => apiService.put(`/bundles/${id}`, data),\n  deleteBundle: id => apiService.delete(`/bundles/${id}`),\n  designBundle: data => apiService.post('/bundles/design', data),\n  validateBundle: data => apiService.post('/bundles/validate', data),\n  getBundleComponents: id => apiService.get(`/bundles/${id}/components`),\n  getCrossSellRecommendations: data => apiService.post('/bundles/cross-sell', data),\n  getUpSellRecommendations: data => apiService.post('/bundles/up-sell', data),\n  checkCompatibility: data => apiService.get('/bundles/compatibility', data),\n  optimizeBundle: data => apiService.post('/bundles/optimize', data),\n  getBundleAnalytics: id => apiService.get(`/bundles/analytics`, {\n    bundleId: id\n  })\n};\nexport default apiService;", "map": {"version": 3, "names": ["axios", "mockUsers", "mockProducts", "mockCustomers", "mockQuotes", "mockConfigurations", "createMockApiResponse", "mockLoginResponse", "mockDelay", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "ApiService", "constructor", "instance", "create", "baseURL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "data", "_error$response2", "_error$response2$data", "Error", "message", "get", "url", "params", "method", "post", "put", "delete", "getPaginated", "upload", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "download", "filename", "responseType", "blob", "Blob", "downloadUrl", "URL", "createObjectURL", "link", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_error$response3", "_error$response3$data", "apiService", "MockApiService", "login", "credentials", "username", "password", "getCurrentUser", "getProducts", "getProduct", "id", "product", "find", "p", "getCustomers", "getQuotes", "shareQuote", "shareLink", "Date", "now", "getConfigurations", "getDashboardData", "totalQuotes", "totalRevenue", "conversionRate", "averageQuoteValue", "recentQuotes", "slice", "topProducts", "salesTrend", "month", "sales", "quotes", "isDevelopment", "NODE_ENV", "mockApiService", "productApi", "getCategories", "createCategory", "updateCategory", "deleteCategory", "createProduct", "updateProduct", "deleteProduct", "uploadProductImage", "getProductSpecifications", "updateProductSpecifications", "configurationApi", "getConfiguration", "createConfiguration", "updateConfiguration", "deleteConfiguration", "validateConfiguration", "getConfigurationTemplates", "createConfigurationTemplate", "cloneConfiguration", "getConfigurationPreview", "smartBuild", "aiRecommend", "optimize", "simulate", "checkCompatibility", "autoComplete", "getAlternatives", "configurationId", "costOptimize", "performanceTest", "get3DModel", "scenarioBased", "multiStep", "conditional", "getDependencies", "validateConstraints", "bulkCreate", "clone", "migrate", "getAuditTrail", "rollback", "impactAnalysis", "merge", "configurationRuleApi", "getRules", "getRule", "createRule", "updateRule", "deleteRule", "validateRule", "testRule", "getConflicts", "bundleApi", "getBundles", "getBundle", "createBundle", "updateBundle", "deleteBundle", "designBundle", "validateBundle", "getBundleComponents", "getCrossSellRecommendations", "getUpSellRecommendations", "optimizeBundle", "getBundleAnalytics", "bundleId"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { ApiResponse, PaginationParams } from '@/types';\nimport {\n  mockUsers,\n  mockProducts,\n  mockCustomers,\n  mockQuotes,\n  mockConfigurations,\n  createMockApiResponse,\n  createMockPaginatedResponse,\n  mockLoginResponse,\n  mockDelay,\n} from '@/data/mockData';\n\n// API基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';\n\nclass ApiService {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('access_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error) => {\n        if (error.response?.status === 401) {\n          localStorage.removeItem('access_token');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // 通用请求方法\n  async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {\n    try {\n      const response = await this.instance.request<ApiResponse<T>>(config);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || error.message || '请求失败');\n    }\n  }\n\n  // GET请求\n  async get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {\n    return this.request<T>({\n      method: 'GET',\n      url,\n      params,\n    });\n  }\n\n  // POST请求\n  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>({\n      method: 'POST',\n      url,\n      data,\n    });\n  }\n\n  // PUT请求\n  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>({\n      method: 'PUT',\n      url,\n      data,\n    });\n  }\n\n  // DELETE请求\n  async delete<T = any>(url: string): Promise<ApiResponse<T>> {\n    return this.request<T>({\n      method: 'DELETE',\n      url,\n    });\n  }\n\n  // 分页查询\n  async getPaginated<T = any>(url: string, params: PaginationParams): Promise<ApiResponse<T[]>> {\n    return this.get<T[]>(url, params);\n  }\n\n  // 文件上传\n  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return this.request<T>({\n      method: 'POST',\n      url,\n      data: formData,\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    });\n  }\n\n  // 文件下载\n  async download(url: string, filename?: string): Promise<void> {\n    try {\n      const response = await this.instance.get(url, {\n        responseType: 'blob',\n      });\n\n      const blob = new Blob([response.data]);\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename || 'download';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || error.message || '下载失败');\n    }\n  }\n}\n\n// 创建API服务实例\nexport const apiService = new ApiService();\n\nclass MockApiService {\n  // 模拟登录\n  async login(credentials: { username: string; password: string }) {\n    await mockDelay(1000);\n    if (credentials.username === 'admin' && credentials.password === 'admin123') {\n      return mockLoginResponse;\n    }\n    throw new Error('用户名或密码错误');\n  }\n\n  // 模拟获取当前用户\n  async getCurrentUser() {\n    await mockDelay(500);\n    return createMockApiResponse(mockUsers[0]);\n  }\n\n  // 模拟产品API\n  async getProducts(params: any) {\n    await mockDelay(800);\n    return createMockApiResponse(mockProducts);\n  }\n\n  async getProduct(id: string) {\n    await mockDelay(500);\n    const product = mockProducts.find(p => p.id === id);\n    if (!product) throw new Error('产品未找到');\n    return createMockApiResponse(product);\n  }\n\n  // 模拟客户API\n  async getCustomers(params: any) {\n    await mockDelay(600);\n    return createMockApiResponse(mockCustomers);\n  }\n\n  // 模拟报价API\n  async getQuotes(params: any) {\n    await mockDelay(700);\n    return createMockApiResponse(mockQuotes);\n  }\n\n  async shareQuote(id: string) {\n    await mockDelay(500);\n    const shareLink = `https://cpq.example.com/shared/${id}/${Date.now()}`;\n    return createMockApiResponse(shareLink);\n  }\n\n  // 模拟配置API\n  async getConfigurations(params: any) {\n    await mockDelay(600);\n    return createMockApiResponse(mockConfigurations);\n  }\n\n  // 模拟仪表板数据\n  async getDashboardData() {\n    await mockDelay(1000);\n    return createMockApiResponse({\n      totalQuotes: 1250,\n      totalRevenue: 2500000,\n      conversionRate: 0.35,\n      averageQuoteValue: 2000,\n      recentQuotes: mockQuotes.slice(0, 5),\n      topProducts: mockProducts.slice(0, 5),\n      salesTrend: [\n        { month: '1月', sales: 65000, quotes: 120 },\n        { month: '2月', sales: 78000, quotes: 145 },\n        { month: '3月', sales: 92000, quotes: 168 },\n      ],\n    });\n  }\n}\n\n// 根据环境选择API服务\nconst isDevelopment = process.env.NODE_ENV === 'development';\nexport const mockApiService = new MockApiService();\n\n// 产品相关API\nexport const productApi = {\n  // 产品分类\n  getCategories: () => apiService.get('/products/categories'),\n  createCategory: (data: any) => apiService.post('/products/categories', data),\n  updateCategory: (id: string, data: any) => apiService.put(`/products/categories/${id}`, data),\n  deleteCategory: (id: string) => apiService.delete(`/products/categories/${id}`),\n\n  // 产品管理\n  getProducts: (params: PaginationParams) => apiService.getPaginated('/products', params),\n  getProduct: (id: string) => apiService.get(`/products/${id}`),\n  createProduct: (data: any) => apiService.post('/products', data),\n  updateProduct: (id: string, data: any) => apiService.put(`/products/${id}`, data),\n  deleteProduct: (id: string) => apiService.delete(`/products/${id}`),\n  uploadProductImage: (id: string, file: File) => apiService.upload(`/products/${id}/images`, file),\n  getProductSpecifications: (id: string) => apiService.get(`/products/${id}/specifications`),\n  updateProductSpecifications: (id: string, data: any) => apiService.put(`/products/${id}/specifications`, data),\n};\n\n// 配置相关API\nexport const configurationApi = {\n  getConfigurations: (params: PaginationParams) => apiService.getPaginated('/configurations', params),\n  getConfiguration: (id: string) => apiService.get(`/configurations/${id}`),\n  createConfiguration: (data: any) => apiService.post('/configurations', data),\n  updateConfiguration: (id: string, data: any) => apiService.put(`/configurations/${id}`, data),\n  deleteConfiguration: (id: string) => apiService.delete(`/configurations/${id}`),\n  validateConfiguration: (data: any) => apiService.post('/configurations/validate', data),\n  getConfigurationTemplates: () => apiService.get('/configurations/templates'),\n  createConfigurationTemplate: (data: any) => apiService.post('/configurations/templates', data),\n  cloneConfiguration: (id: string) => apiService.post(`/configurations/${id}/clone`),\n  getConfigurationPreview: (id: string) => apiService.get(`/configurations/${id}/preview`),\n\n  // 智能配置\n  smartBuild: (data: any) => apiService.post('/configurations/smart-build', data),\n  aiRecommend: (data: any) => apiService.post('/configurations/ai-recommend', data),\n  optimize: (data: any) => apiService.post('/configurations/optimize', data),\n  simulate: (data: any) => apiService.post('/configurations/simulate', data),\n  checkCompatibility: (data: any) => apiService.get('/configurations/compatibility', data),\n  autoComplete: (data: any) => apiService.post('/configurations/auto-complete', data),\n  getAlternatives: (id: string) => apiService.get(`/configurations/alternatives`, { configurationId: id }),\n  costOptimize: (data: any) => apiService.post('/configurations/cost-optimize', data),\n  performanceTest: (data: any) => apiService.post('/configurations/performance-test', data),\n  get3DModel: (id: string) => apiService.get(`/configurations/${id}/3d-model`),\n\n  // 高级配置场景\n  scenarioBased: (data: any) => apiService.post('/configurations/scenario-based', data),\n  multiStep: (data: any) => apiService.post('/configurations/multi-step', data),\n  conditional: (data: any) => apiService.post('/configurations/conditional', data),\n  getDependencies: (id: string) => apiService.get(`/configurations/dependencies`, { configurationId: id }),\n  validateConstraints: (data: any) => apiService.post('/configurations/constraints', data),\n  bulkCreate: (data: any) => apiService.post('/configurations/bulk-create', data),\n  clone: (data: any) => apiService.post('/configurations/clone', data),\n  migrate: (data: any) => apiService.post('/configurations/migrate', data),\n  getAuditTrail: (id: string) => apiService.get(`/configurations/audit-trail`, { configurationId: id }),\n  rollback: (data: any) => apiService.post('/configurations/rollback', data),\n  impactAnalysis: (data: any) => apiService.get('/configurations/impact-analysis', data),\n  merge: (data: any) => apiService.post('/configurations/merge', data),\n};\n\n// 配置规则API\nexport const configurationRuleApi = {\n  getRules: (params: PaginationParams) => apiService.getPaginated('/configuration-rules', params),\n  getRule: (id: string) => apiService.get(`/configuration-rules/${id}`),\n  createRule: (data: any) => apiService.post('/configuration-rules', data),\n  updateRule: (id: string, data: any) => apiService.put(`/configuration-rules/${id}`, data),\n  deleteRule: (id: string) => apiService.delete(`/configuration-rules/${id}`),\n  validateRule: (data: any) => apiService.post('/configuration-rules/validate', data),\n  testRule: (data: any) => apiService.post('/configuration-rules/test', data),\n  getConflicts: () => apiService.get('/configuration-rules/conflicts'),\n};\n\n// 套餐组合API\nexport const bundleApi = {\n  getBundles: (params: PaginationParams) => apiService.getPaginated('/bundles', params),\n  getBundle: (id: string) => apiService.get(`/bundles/${id}`),\n  createBundle: (data: any) => apiService.post('/bundles', data),\n  updateBundle: (id: string, data: any) => apiService.put(`/bundles/${id}`, data),\n  deleteBundle: (id: string) => apiService.delete(`/bundles/${id}`),\n  designBundle: (data: any) => apiService.post('/bundles/design', data),\n  validateBundle: (data: any) => apiService.post('/bundles/validate', data),\n  getBundleComponents: (id: string) => apiService.get(`/bundles/${id}/components`),\n  getCrossSellRecommendations: (data: any) => apiService.post('/bundles/cross-sell', data),\n  getUpSellRecommendations: (data: any) => apiService.post('/bundles/up-sell', data),\n  checkCompatibility: (data: any) => apiService.get('/bundles/compatibility', data),\n  optimizeBundle: (data: any) => apiService.post('/bundles/optimize', data),\n  getBundleAnalytics: (id: string) => apiService.get(`/bundles/analytics`, { bundleId: id }),\n};\n\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA4D,OAAO;AAE/E,SACEC,SAAS,EACTC,YAAY,EACZC,aAAa,EACbC,UAAU,EACVC,kBAAkB,EAClBC,qBAAqB,EAErBC,iBAAiB,EACjBC,SAAS,QACJ,iBAAiB;;AAExB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;AAEtF,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,QAAQ;IAGd,IAAI,CAACA,QAAQ,GAAGf,KAAK,CAACgB,MAAM,CAAC;MAC3BC,OAAO,EAAER,YAAY;MACrBS,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAClD,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACd,QAAQ,CAACM,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClCR,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;QACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMP,OAAOA,CAAUE,MAA0B,EAA2B;IAC1E,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM,IAAI,CAACjB,QAAQ,CAACO,OAAO,CAAiBE,MAAM,CAAC;MACpE,OAAOQ,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAW,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAX,KAAK,CAACG,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBD,IAAI,cAAAE,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAId,KAAK,CAACc,OAAO,IAAI,MAAM,CAAC;IAC3E;EACF;;EAEA;EACA,MAAMC,GAAGA,CAAUC,GAAW,EAAEC,MAAY,EAA2B;IACrE,OAAO,IAAI,CAACxB,OAAO,CAAI;MACrByB,MAAM,EAAE,KAAK;MACbF,GAAG;MACHC;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,IAAIA,CAAUH,GAAW,EAAEN,IAAU,EAA2B;IACpE,OAAO,IAAI,CAACjB,OAAO,CAAI;MACrByB,MAAM,EAAE,MAAM;MACdF,GAAG;MACHN;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMU,GAAGA,CAAUJ,GAAW,EAAEN,IAAU,EAA2B;IACnE,OAAO,IAAI,CAACjB,OAAO,CAAI;MACrByB,MAAM,EAAE,KAAK;MACbF,GAAG;MACHN;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMW,MAAMA,CAAUL,GAAW,EAA2B;IAC1D,OAAO,IAAI,CAACvB,OAAO,CAAI;MACrByB,MAAM,EAAE,QAAQ;MAChBF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMM,YAAYA,CAAUN,GAAW,EAAEC,MAAwB,EAA6B;IAC5F,OAAO,IAAI,CAACF,GAAG,CAAMC,GAAG,EAAEC,MAAM,CAAC;EACnC;;EAEA;EACA,MAAMM,MAAMA,CAAUP,GAAW,EAAEQ,IAAU,EAAEC,UAAuC,EAA2B;IAC/G,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAE7B,OAAO,IAAI,CAAC/B,OAAO,CAAI;MACrByB,MAAM,EAAE,MAAM;MACdF,GAAG;MACHN,IAAI,EAAEgB,QAAQ;MACdpC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDuC,gBAAgB,EAAGC,aAAa,IAAK;QACnC,IAAIL,UAAU,IAAIK,aAAa,CAACC,KAAK,EAAE;UACrC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAM,GAAG,GAAG,GAAIL,aAAa,CAACC,KAAK,CAAC;UAC/EN,UAAU,CAACO,QAAQ,CAAC;QACtB;MACF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMI,QAAQA,CAACpB,GAAW,EAAEqB,QAAiB,EAAiB;IAC5D,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAM,IAAI,CAACjB,QAAQ,CAAC6B,GAAG,CAACC,GAAG,EAAE;QAC5CsB,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACrC,QAAQ,CAACO,IAAI,CAAC,CAAC;MACtC,MAAM+B,WAAW,GAAGlC,MAAM,CAACmC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACpD,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACnC,IAAI,GAAGgC,WAAW;MACvBG,IAAI,CAACR,QAAQ,GAAGC,QAAQ,IAAI,UAAU;MACtCQ,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;MAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;MACZJ,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;MAC/BrC,MAAM,CAACmC,GAAG,CAACS,eAAe,CAACV,WAAW,CAAC;IACzC,CAAC,CAAC,OAAOzC,KAAU,EAAE;MAAA,IAAAoD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIxC,KAAK,CAAC,EAAAuC,gBAAA,GAAApD,KAAK,CAACG,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBvC,OAAO,KAAId,KAAK,CAACc,OAAO,IAAI,MAAM,CAAC;IAC3E;EACF;AACF;;AAEA;AACA,OAAO,MAAMwC,UAAU,GAAG,IAAItE,UAAU,CAAC,CAAC;AAE1C,MAAMuE,cAAc,CAAC;EACnB;EACA,MAAMC,KAAKA,CAACC,WAAmD,EAAE;IAC/D,MAAM9E,SAAS,CAAC,IAAI,CAAC;IACrB,IAAI8E,WAAW,CAACC,QAAQ,KAAK,OAAO,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MAC3E,OAAOjF,iBAAiB;IAC1B;IACA,MAAM,IAAImC,KAAK,CAAC,UAAU,CAAC;EAC7B;;EAEA;EACA,MAAM+C,cAAcA,CAAA,EAAG;IACrB,MAAMjF,SAAS,CAAC,GAAG,CAAC;IACpB,OAAOF,qBAAqB,CAACL,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA;EACA,MAAMyF,WAAWA,CAAC5C,MAAW,EAAE;IAC7B,MAAMtC,SAAS,CAAC,GAAG,CAAC;IACpB,OAAOF,qBAAqB,CAACJ,YAAY,CAAC;EAC5C;EAEA,MAAMyF,UAAUA,CAACC,EAAU,EAAE;IAC3B,MAAMpF,SAAS,CAAC,GAAG,CAAC;IACpB,MAAMqF,OAAO,GAAG3F,YAAY,CAAC4F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKA,EAAE,CAAC;IACnD,IAAI,CAACC,OAAO,EAAE,MAAM,IAAInD,KAAK,CAAC,OAAO,CAAC;IACtC,OAAOpC,qBAAqB,CAACuF,OAAO,CAAC;EACvC;;EAEA;EACA,MAAMG,YAAYA,CAAClD,MAAW,EAAE;IAC9B,MAAMtC,SAAS,CAAC,GAAG,CAAC;IACpB,OAAOF,qBAAqB,CAACH,aAAa,CAAC;EAC7C;;EAEA;EACA,MAAM8F,SAASA,CAACnD,MAAW,EAAE;IAC3B,MAAMtC,SAAS,CAAC,GAAG,CAAC;IACpB,OAAOF,qBAAqB,CAACF,UAAU,CAAC;EAC1C;EAEA,MAAM8F,UAAUA,CAACN,EAAU,EAAE;IAC3B,MAAMpF,SAAS,CAAC,GAAG,CAAC;IACpB,MAAM2F,SAAS,GAAG,kCAAkCP,EAAE,IAAIQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IACtE,OAAO/F,qBAAqB,CAAC6F,SAAS,CAAC;EACzC;;EAEA;EACA,MAAMG,iBAAiBA,CAACxD,MAAW,EAAE;IACnC,MAAMtC,SAAS,CAAC,GAAG,CAAC;IACpB,OAAOF,qBAAqB,CAACD,kBAAkB,CAAC;EAClD;;EAEA;EACA,MAAMkG,gBAAgBA,CAAA,EAAG;IACvB,MAAM/F,SAAS,CAAC,IAAI,CAAC;IACrB,OAAOF,qBAAqB,CAAC;MAC3BkG,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,OAAO;MACrBC,cAAc,EAAE,IAAI;MACpBC,iBAAiB,EAAE,IAAI;MACvBC,YAAY,EAAExG,UAAU,CAACyG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACpCC,WAAW,EAAE5G,YAAY,CAAC2G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACrCE,UAAU,EAAE,CACV;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAI,CAAC,EAC1C;QAAEF,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAI,CAAC,EAC1C;QAAEF,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAI,CAAC;IAE9C,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,MAAMC,aAAa,GAAGzG,OAAO,CAACC,GAAG,CAACyG,QAAQ,KAAK,aAAa;AAC5D,OAAO,MAAMC,cAAc,GAAG,IAAIjC,cAAc,CAAC,CAAC;;AAElD;AACA,OAAO,MAAMkC,UAAU,GAAG;EACxB;EACAC,aAAa,EAAEA,CAAA,KAAMpC,UAAU,CAACvC,GAAG,CAAC,sBAAsB,CAAC;EAC3D4E,cAAc,EAAGjF,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,sBAAsB,EAAET,IAAI,CAAC;EAC5EkF,cAAc,EAAEA,CAAC7B,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,wBAAwB2C,EAAE,EAAE,EAAErD,IAAI,CAAC;EAC7FmF,cAAc,EAAG9B,EAAU,IAAKT,UAAU,CAACjC,MAAM,CAAC,wBAAwB0C,EAAE,EAAE,CAAC;EAE/E;EACAF,WAAW,EAAG5C,MAAwB,IAAKqC,UAAU,CAAChC,YAAY,CAAC,WAAW,EAAEL,MAAM,CAAC;EACvF6C,UAAU,EAAGC,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,aAAagD,EAAE,EAAE,CAAC;EAC7D+B,aAAa,EAAGpF,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,WAAW,EAAET,IAAI,CAAC;EAChEqF,aAAa,EAAEA,CAAChC,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,aAAa2C,EAAE,EAAE,EAAErD,IAAI,CAAC;EACjFsF,aAAa,EAAGjC,EAAU,IAAKT,UAAU,CAACjC,MAAM,CAAC,aAAa0C,EAAE,EAAE,CAAC;EACnEkC,kBAAkB,EAAEA,CAAClC,EAAU,EAAEvC,IAAU,KAAK8B,UAAU,CAAC/B,MAAM,CAAC,aAAawC,EAAE,SAAS,EAAEvC,IAAI,CAAC;EACjG0E,wBAAwB,EAAGnC,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,aAAagD,EAAE,iBAAiB,CAAC;EAC1FoC,2BAA2B,EAAEA,CAACpC,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,aAAa2C,EAAE,iBAAiB,EAAErD,IAAI;AAC/G,CAAC;;AAED;AACA,OAAO,MAAM0F,gBAAgB,GAAG;EAC9B3B,iBAAiB,EAAGxD,MAAwB,IAAKqC,UAAU,CAAChC,YAAY,CAAC,iBAAiB,EAAEL,MAAM,CAAC;EACnGoF,gBAAgB,EAAGtC,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,mBAAmBgD,EAAE,EAAE,CAAC;EACzEuC,mBAAmB,EAAG5F,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,iBAAiB,EAAET,IAAI,CAAC;EAC5E6F,mBAAmB,EAAEA,CAACxC,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,mBAAmB2C,EAAE,EAAE,EAAErD,IAAI,CAAC;EAC7F8F,mBAAmB,EAAGzC,EAAU,IAAKT,UAAU,CAACjC,MAAM,CAAC,mBAAmB0C,EAAE,EAAE,CAAC;EAC/E0C,qBAAqB,EAAG/F,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,0BAA0B,EAAET,IAAI,CAAC;EACvFgG,yBAAyB,EAAEA,CAAA,KAAMpD,UAAU,CAACvC,GAAG,CAAC,2BAA2B,CAAC;EAC5E4F,2BAA2B,EAAGjG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,2BAA2B,EAAET,IAAI,CAAC;EAC9FkG,kBAAkB,EAAG7C,EAAU,IAAKT,UAAU,CAACnC,IAAI,CAAC,mBAAmB4C,EAAE,QAAQ,CAAC;EAClF8C,uBAAuB,EAAG9C,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,mBAAmBgD,EAAE,UAAU,CAAC;EAExF;EACA+C,UAAU,EAAGpG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,6BAA6B,EAAET,IAAI,CAAC;EAC/EqG,WAAW,EAAGrG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,8BAA8B,EAAET,IAAI,CAAC;EACjFsG,QAAQ,EAAGtG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,0BAA0B,EAAET,IAAI,CAAC;EAC1EuG,QAAQ,EAAGvG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,0BAA0B,EAAET,IAAI,CAAC;EAC1EwG,kBAAkB,EAAGxG,IAAS,IAAK4C,UAAU,CAACvC,GAAG,CAAC,+BAA+B,EAAEL,IAAI,CAAC;EACxFyG,YAAY,EAAGzG,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,+BAA+B,EAAET,IAAI,CAAC;EACnF0G,eAAe,EAAGrD,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,8BAA8B,EAAE;IAAEsG,eAAe,EAAEtD;EAAG,CAAC,CAAC;EACxGuD,YAAY,EAAG5G,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,+BAA+B,EAAET,IAAI,CAAC;EACnF6G,eAAe,EAAG7G,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,kCAAkC,EAAET,IAAI,CAAC;EACzF8G,UAAU,EAAGzD,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,mBAAmBgD,EAAE,WAAW,CAAC;EAE5E;EACA0D,aAAa,EAAG/G,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,gCAAgC,EAAET,IAAI,CAAC;EACrFgH,SAAS,EAAGhH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,4BAA4B,EAAET,IAAI,CAAC;EAC7EiH,WAAW,EAAGjH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,6BAA6B,EAAET,IAAI,CAAC;EAChFkH,eAAe,EAAG7D,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,8BAA8B,EAAE;IAAEsG,eAAe,EAAEtD;EAAG,CAAC,CAAC;EACxG8D,mBAAmB,EAAGnH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,6BAA6B,EAAET,IAAI,CAAC;EACxFoH,UAAU,EAAGpH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,6BAA6B,EAAET,IAAI,CAAC;EAC/EqH,KAAK,EAAGrH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,uBAAuB,EAAET,IAAI,CAAC;EACpEsH,OAAO,EAAGtH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,yBAAyB,EAAET,IAAI,CAAC;EACxEuH,aAAa,EAAGlE,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,6BAA6B,EAAE;IAAEsG,eAAe,EAAEtD;EAAG,CAAC,CAAC;EACrGmE,QAAQ,EAAGxH,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,0BAA0B,EAAET,IAAI,CAAC;EAC1EyH,cAAc,EAAGzH,IAAS,IAAK4C,UAAU,CAACvC,GAAG,CAAC,iCAAiC,EAAEL,IAAI,CAAC;EACtF0H,KAAK,EAAG1H,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,uBAAuB,EAAET,IAAI;AACrE,CAAC;;AAED;AACA,OAAO,MAAM2H,oBAAoB,GAAG;EAClCC,QAAQ,EAAGrH,MAAwB,IAAKqC,UAAU,CAAChC,YAAY,CAAC,sBAAsB,EAAEL,MAAM,CAAC;EAC/FsH,OAAO,EAAGxE,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,wBAAwBgD,EAAE,EAAE,CAAC;EACrEyE,UAAU,EAAG9H,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,sBAAsB,EAAET,IAAI,CAAC;EACxE+H,UAAU,EAAEA,CAAC1E,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,wBAAwB2C,EAAE,EAAE,EAAErD,IAAI,CAAC;EACzFgI,UAAU,EAAG3E,EAAU,IAAKT,UAAU,CAACjC,MAAM,CAAC,wBAAwB0C,EAAE,EAAE,CAAC;EAC3E4E,YAAY,EAAGjI,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,+BAA+B,EAAET,IAAI,CAAC;EACnFkI,QAAQ,EAAGlI,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,2BAA2B,EAAET,IAAI,CAAC;EAC3EmI,YAAY,EAAEA,CAAA,KAAMvF,UAAU,CAACvC,GAAG,CAAC,gCAAgC;AACrE,CAAC;;AAED;AACA,OAAO,MAAM+H,SAAS,GAAG;EACvBC,UAAU,EAAG9H,MAAwB,IAAKqC,UAAU,CAAChC,YAAY,CAAC,UAAU,EAAEL,MAAM,CAAC;EACrF+H,SAAS,EAAGjF,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,YAAYgD,EAAE,EAAE,CAAC;EAC3DkF,YAAY,EAAGvI,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,UAAU,EAAET,IAAI,CAAC;EAC9DwI,YAAY,EAAEA,CAACnF,EAAU,EAAErD,IAAS,KAAK4C,UAAU,CAAClC,GAAG,CAAC,YAAY2C,EAAE,EAAE,EAAErD,IAAI,CAAC;EAC/EyI,YAAY,EAAGpF,EAAU,IAAKT,UAAU,CAACjC,MAAM,CAAC,YAAY0C,EAAE,EAAE,CAAC;EACjEqF,YAAY,EAAG1I,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,iBAAiB,EAAET,IAAI,CAAC;EACrE2I,cAAc,EAAG3I,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,mBAAmB,EAAET,IAAI,CAAC;EACzE4I,mBAAmB,EAAGvF,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,YAAYgD,EAAE,aAAa,CAAC;EAChFwF,2BAA2B,EAAG7I,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,qBAAqB,EAAET,IAAI,CAAC;EACxF8I,wBAAwB,EAAG9I,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,kBAAkB,EAAET,IAAI,CAAC;EAClFwG,kBAAkB,EAAGxG,IAAS,IAAK4C,UAAU,CAACvC,GAAG,CAAC,wBAAwB,EAAEL,IAAI,CAAC;EACjF+I,cAAc,EAAG/I,IAAS,IAAK4C,UAAU,CAACnC,IAAI,CAAC,mBAAmB,EAAET,IAAI,CAAC;EACzEgJ,kBAAkB,EAAG3F,EAAU,IAAKT,UAAU,CAACvC,GAAG,CAAC,oBAAoB,EAAE;IAAE4I,QAAQ,EAAE5F;EAAG,CAAC;AAC3F,CAAC;AAED,eAAeT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}