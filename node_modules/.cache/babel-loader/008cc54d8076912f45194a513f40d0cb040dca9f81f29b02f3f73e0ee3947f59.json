{"ast": null, "code": "export function isRef(obj) {\n  return (\n    // eslint-disable-next-line no-prototype-builtins\n    obj !== null && typeof obj === 'object' && Object.prototype.hasOwnProperty.call(obj, 'current')\n  );\n}", "map": {"version": 3, "names": ["isRef", "obj", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/react-dnd/src/internals/isRef.ts"], "sourcesContent": ["export interface Ref<T> {\n\tcurrent: T\n}\n\nexport function isRef(obj: unknown): boolean {\n\treturn (\n\t\t// eslint-disable-next-line no-prototype-builtins\n\t\tobj !== null &&\n\t\ttypeof obj === 'object' &&\n\t\tObject.prototype.hasOwnProperty.call(obj, 'current')\n\t)\n}\n"], "mappings": "AAIA,OAAO,SAASA,KAAKA,CAACC,GAAY,EAAW;EAC5C;IACC;IACAA,GAAG,KAAK,IAAI,IACZ,OAAOA,GAAG,KAAK,QAAQ,IACvBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,SAAS;EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}