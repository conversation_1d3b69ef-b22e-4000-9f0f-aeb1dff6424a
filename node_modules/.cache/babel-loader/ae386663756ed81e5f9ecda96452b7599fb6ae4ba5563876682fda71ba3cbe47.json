{"ast": null, "code": "export function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return state + 1;\n}", "map": {"version": 3, "names": ["reduce", "state", "arguments", "length", "undefined"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/reducers/stateId.ts"], "sourcesContent": ["export type State = number\n\nexport function reduce(state: State = 0): State {\n\treturn state + 1\n}\n"], "mappings": "AAEA,OAAO,SAASA,MAAMA,CAAA,EAA0B;EAAA,IAAzBC,KAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACtC,OAAOD,KAAK,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}