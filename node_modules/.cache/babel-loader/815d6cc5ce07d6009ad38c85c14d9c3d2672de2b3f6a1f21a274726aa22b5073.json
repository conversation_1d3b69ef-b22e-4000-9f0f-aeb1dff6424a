{"ast": null, "code": "// 模拟数据文件\n\n// 模拟用户数据\nexport const mockUsers = [{\n  id: '1',\n  username: 'admin',\n  email: '<EMAIL>',\n  firstName: '管理员',\n  lastName: '系统',\n  avatar: 'https://via.placeholder.com/64x64?text=Admin',\n  roles: [{\n    id: 'admin',\n    name: '系统管理员',\n    description: '拥有系统所有权限',\n    permissions: [],\n    createdAt: '2023-01-01',\n    updatedAt: '2023-01-01'\n  }],\n  permissions: [],\n  status: 'active',\n  lastLoginAt: '2024-01-15T10:30:00Z',\n  createdAt: '2023-01-01T00:00:00Z',\n  updatedAt: '2024-01-15T10:30:00Z'\n}, {\n  id: '2',\n  username: 'sales001',\n  email: '<EMAIL>',\n  firstName: '李',\n  lastName: '销售',\n  avatar: 'https://via.placeholder.com/64x64?text=李销售',\n  roles: [{\n    id: 'sales_manager',\n    name: '销售经理',\n    description: '管理销售团队和报价',\n    permissions: [],\n    createdAt: '2023-01-01',\n    updatedAt: '2023-01-01'\n  }],\n  permissions: [],\n  status: 'active',\n  lastLoginAt: '2024-01-15T09:15:00Z',\n  createdAt: '2023-03-15T00:00:00Z',\n  updatedAt: '2024-01-15T09:15:00Z'\n}];\n\n// 金蝶产品数据\nexport const mockProducts = [{\n  id: '1',\n  name: '金蝶云星辰标准版',\n  description: '适用于小微企业的云端财务管理软件，提供完整的财务核算、报表分析等功能',\n  sku: 'KIS-CLOUD-STD',\n  category: {\n    id: '1',\n    name: '财务软件',\n    description: '企业财务管理软件',\n    level: 1,\n    path: '/financial-software',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  },\n  specifications: [{\n    id: '1',\n    productId: '1',\n    name: '用户数量',\n    value: '3用户',\n    type: 'select',\n    required: true,\n    options: ['1用户', '3用户', '5用户', '10用户'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '2',\n    productId: '1',\n    name: '账套数量',\n    value: '1个账套',\n    type: 'select',\n    required: true,\n    options: ['1个账套', '3个账套', '5个账套'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '3',\n    productId: '1',\n    name: '存储空间',\n    value: '10GB',\n    type: 'select',\n    required: true,\n    options: ['5GB', '10GB', '20GB', '50GB'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '4',\n    productId: '1',\n    name: '服务期限',\n    value: '1年',\n    type: 'select',\n    required: true,\n    options: ['1年', '2年', '3年'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '5',\n    productId: '1',\n    name: '技术支持',\n    value: '标准支持',\n    type: 'select',\n    required: true,\n    options: ['标准支持', '高级支持', '专属支持'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  images: [{\n    id: '1',\n    productId: '1',\n    url: 'https://via.placeholder.com/400x300?text=金蝶云星辰',\n    alt: '金蝶云星辰标准版',\n    isPrimary: true,\n    order: 1,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  basePrice: 3980,\n  currency: 'CNY',\n  status: 'active',\n  tags: ['财务软件', '云端', '小微企业', '热门'],\n  version: '1.0',\n  createdAt: '2024-01-01',\n  updatedAt: '2024-01-01'\n}, {\n  id: '2',\n  name: '金蝶精斗云专业版',\n  description: '一体化云端企业管理软件，集成进销存、财务、CRM等功能模块',\n  sku: 'JDY-PRO-001',\n  category: {\n    id: '2',\n    name: '企业管理软件',\n    description: '一体化企业管理解决方案',\n    level: 1,\n    path: '/enterprise-software',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  },\n  specifications: [{\n    id: '6',\n    productId: '2',\n    name: '用户数量',\n    value: '5用户',\n    type: 'select',\n    required: true,\n    options: ['3用户', '5用户', '10用户', '20用户'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '7',\n    productId: '2',\n    name: '功能模块',\n    value: '进销存+财务',\n    type: 'select',\n    required: true,\n    options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '8',\n    productId: '2',\n    name: '存储空间',\n    value: '20GB',\n    type: 'select',\n    required: true,\n    options: ['10GB', '20GB', '50GB', '100GB'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '9',\n    productId: '2',\n    name: 'API调用',\n    value: '5000次/月',\n    type: 'select',\n    required: false,\n    options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  images: [{\n    id: '2',\n    productId: '2',\n    url: 'https://via.placeholder.com/400x300?text=金蝶精斗云',\n    alt: '金蝶精斗云专业版',\n    isPrimary: true,\n    order: 1,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  basePrice: 6980,\n  currency: 'CNY',\n  status: 'active',\n  tags: ['企业管理', '一体化', '云端', '推荐'],\n  version: '1.0',\n  createdAt: '2024-01-01',\n  updatedAt: '2024-01-01'\n}, {\n  id: '3',\n  name: '金蝶云星辰专业版',\n  description: '适用于中小企业的专业财务管理软件，支持多账套、多用户协同办公',\n  sku: 'KIS-CLOUD-PRO',\n  category: {\n    id: '1',\n    name: '财务软件',\n    description: '企业财务管理软件',\n    level: 1,\n    path: '/financial-software',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  },\n  specifications: [{\n    id: '10',\n    productId: '3',\n    name: '用户数量',\n    value: '10用户',\n    type: 'select',\n    required: true,\n    options: ['5用户', '10用户', '20用户', '50用户'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '11',\n    productId: '3',\n    name: '账套数量',\n    value: '5个账套',\n    type: 'select',\n    required: true,\n    options: ['3个账套', '5个账套', '10个账套', '不限'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '12',\n    productId: '3',\n    name: '功能模块',\n    value: '全功能版',\n    type: 'select',\n    required: true,\n    options: ['基础版', '标准版', '全功能版'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  images: [{\n    id: '3',\n    productId: '3',\n    url: 'https://via.placeholder.com/400x300?text=云星辰专业版',\n    alt: '金蝶云星辰专业版',\n    isPrimary: true,\n    order: 1,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  basePrice: 8980,\n  currency: 'CNY',\n  status: 'active',\n  tags: ['财务软件', '专业版', '中小企业'],\n  version: '1.0',\n  createdAt: '2024-01-01',\n  updatedAt: '2024-01-01'\n}, {\n  id: '4',\n  name: '金蝶K/3 Cloud',\n  description: '大中型企业数字化转型平台，提供全面的ERP解决方案',\n  sku: 'K3-CLOUD-001',\n  category: {\n    id: '3',\n    name: 'ERP系统',\n    description: '企业资源规划系统',\n    level: 1,\n    path: '/erp-systems',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  },\n  specifications: [{\n    id: '13',\n    productId: '4',\n    name: '用户数量',\n    value: '100用户',\n    type: 'select',\n    required: true,\n    options: ['50用户', '100用户', '200用户', '500用户', '不限'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }, {\n    id: '14',\n    productId: '4',\n    name: '部署方式',\n    value: '云端部署',\n    type: 'select',\n    required: true,\n    options: ['云端部署', '本地部署', '混合部署'],\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  images: [{\n    id: '4',\n    productId: '4',\n    url: 'https://via.placeholder.com/400x300?text=K3+Cloud',\n    alt: '金蝶K/3 Cloud',\n    isPrimary: true,\n    order: 1,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01'\n  }],\n  basePrice: 58000,\n  currency: 'CNY',\n  status: 'active',\n  tags: ['ERP', '大中型企业', '数字化转型'],\n  version: '1.0',\n  createdAt: '2024-01-01',\n  updatedAt: '2024-01-01'\n}];\n\n// 模拟客户数据\nexport const mockCustomers = [{\n  id: '1',\n  name: '王总',\n  email: '<EMAIL>',\n  phone: '+86 138-0013-8000',\n  company: '深圳明星科技有限公司',\n  address: {\n    street: '南山区科技园南区',\n    city: '深圳',\n    state: '广东',\n    country: '中国',\n    postalCode: '518000'\n  },\n  tier: 'gold',\n  creditLimit: 500000,\n  paymentTerms: '30天',\n  salesPersonId: '2',\n  preferences: {\n    currency: 'CNY',\n    language: 'zh-CN',\n    timezone: 'Asia/Shanghai',\n    communicationChannel: 'email',\n    notifications: {\n      quoteUpdates: true,\n      promotions: true,\n      newsletters: false\n    }\n  },\n  status: 'active',\n  createdAt: '2023-01-15T00:00:00Z',\n  updatedAt: '2024-01-15T10:30:00Z'\n}, {\n  id: '2',\n  name: '李四',\n  email: '<EMAIL>',\n  phone: '+86 138-0013-8001',\n  company: '腾讯科技',\n  address: {\n    street: '科技园南区',\n    city: '深圳',\n    state: '广东',\n    country: '中国',\n    postalCode: '518000'\n  },\n  tier: 'gold',\n  creditLimit: 3000000,\n  paymentTerms: '15天',\n  salesPersonId: '2',\n  preferences: {\n    currency: 'CNY',\n    language: 'zh-CN',\n    timezone: 'Asia/Shanghai',\n    communicationChannel: 'email',\n    notifications: {\n      quoteUpdates: true,\n      promotions: false,\n      newsletters: true\n    }\n  },\n  status: 'active',\n  createdAt: '2023-03-20T00:00:00Z',\n  updatedAt: '2024-01-10T14:20:00Z'\n}];\n\n// 模拟报价数据\nexport const mockQuotes = [{\n  id: '1',\n  quoteNumber: 'Q-2024-001',\n  customerId: '1',\n  customerName: '阿里巴巴集团',\n  customerEmail: '<EMAIL>',\n  salesPersonId: '2',\n  items: [{\n    id: '1',\n    quoteId: '1',\n    productId: '1',\n    productName: 'Dell PowerEdge R750 服务器',\n    configurationId: 'config-1',\n    quantity: 2,\n    unitPrice: 25000,\n    discountPercentage: 10,\n    discountAmount: 5000,\n    lineTotal: 45000,\n    specifications: {\n      'CPU': 'Intel Xeon Silver 4314',\n      '内存': '32GB DDR4'\n    },\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-15'\n  }],\n  subtotal: 50000,\n  discountAmount: 5000,\n  taxAmount: 4500,\n  shippingAmount: 0,\n  total: 49500,\n  currency: 'CNY',\n  status: 'sent',\n  validUntil: '2024-02-15',\n  notes: '此报价包含标准配置，如需定制请联系销售人员。',\n  terms: '1. 报价有效期30天\\n2. 付款方式：预付30%，发货前付清余款',\n  templateId: 'template-1',\n  version: 1,\n  parentQuoteId: undefined,\n  createdAt: '2024-01-15T10:30:00Z',\n  updatedAt: '2024-01-15T15:45:00Z'\n}];\n\n// 模拟配置数据\nexport const mockConfigurations = [{\n  id: 'config-1',\n  name: '企业级服务器标准配置',\n  description: '适用于中型企业的标准服务器配置方案',\n  productId: '1',\n  items: [{\n    id: 'item-1',\n    configurationId: 'config-1',\n    productId: '1',\n    quantity: 1,\n    specifications: {\n      'CPU': 'Intel Xeon Silver 4314',\n      '内存': '32GB DDR4'\n    },\n    price: 25000,\n    position: {\n      x: 0,\n      y: 0\n    },\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-15'\n  }],\n  rules: [],\n  status: 'active',\n  version: '1.0',\n  templateId: undefined,\n  createdAt: '2024-01-15T10:30:00Z',\n  updatedAt: '2024-01-15T10:30:00Z'\n}];\n\n// API响应模拟函数\nexport const createMockApiResponse = (data, success = true, message) => ({\n  success,\n  data,\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: Array.isArray(data) ? {\n    page: 1,\n    pageSize: 20,\n    total: data.length,\n    totalPages: Math.ceil(data.length / 20)\n  } : undefined\n});\n\n// 分页响应模拟函数\nexport const createMockPaginatedResponse = (data, page = 1, pageSize = 20, success = true, message) => ({\n  success,\n  data: data.slice((page - 1) * pageSize, page * pageSize),\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: {\n    page,\n    pageSize,\n    total: data.length,\n    totalPages: Math.ceil(data.length / pageSize)\n  }\n});\n\n// 模拟登录响应\nexport const mockLoginResponse = {\n  success: true,\n  data: {\n    user: mockUsers[0],\n    token: 'mock_jwt_token_' + Date.now()\n  },\n  message: '登录成功'\n};\n\n// 模拟延迟函数\nexport const mockDelay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms));", "map": {"version": 3, "names": ["mockUsers", "id", "username", "email", "firstName", "lastName", "avatar", "roles", "name", "description", "permissions", "createdAt", "updatedAt", "status", "lastLoginAt", "mockProducts", "sku", "category", "level", "path", "specifications", "productId", "value", "type", "required", "options", "images", "url", "alt", "isPrimary", "order", "basePrice", "currency", "tags", "version", "mockCustomers", "phone", "company", "address", "street", "city", "state", "country", "postalCode", "tier", "creditLimit", "paymentTerms", "salesPersonId", "preferences", "language", "timezone", "communicationChannel", "notifications", "quoteUpdates", "promotions", "newsletters", "mockQuotes", "quoteNumber", "customerId", "customerName", "customerEmail", "items", "quoteId", "productName", "configurationId", "quantity", "unitPrice", "discountPercentage", "discountAmount", "lineTotal", "subtotal", "taxAmount", "shippingAmount", "total", "validUntil", "notes", "terms", "templateId", "parentQuoteId", "undefined", "mockConfigurations", "price", "position", "x", "y", "rules", "createMockApiResponse", "data", "success", "message", "errors", "pagination", "Array", "isArray", "page", "pageSize", "length", "totalPages", "Math", "ceil", "createMockPaginatedResponse", "slice", "mockLoginResponse", "user", "token", "Date", "now", "mockDelay", "ms", "Promise", "resolve", "setTimeout"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts"], "sourcesContent": ["// 模拟数据文件\nimport { User, Product, Configuration, Quote, Customer } from '@/types';\n\n// 模拟用户数据\nexport const mockUsers: User[] = [\n  {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    firstName: '管理员',\n    lastName: '系统',\n    avatar: 'https://via.placeholder.com/64x64?text=Admin',\n    roles: [\n      {\n        id: 'admin',\n        name: '系统管理员',\n        description: '拥有系统所有权限',\n        permissions: [],\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n      },\n    ],\n    permissions: [],\n    status: 'active',\n    lastLoginAt: '2024-01-15T10:30:00Z',\n    createdAt: '2023-01-01T00:00:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n  {\n    id: '2',\n    username: 'sales001',\n    email: '<EMAIL>',\n    firstName: '李',\n    lastName: '销售',\n    avatar: 'https://via.placeholder.com/64x64?text=李销售',\n    roles: [\n      {\n        id: 'sales_manager',\n        name: '销售经理',\n        description: '管理销售团队和报价',\n        permissions: [],\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n      },\n    ],\n    permissions: [],\n    status: 'active',\n    lastLoginAt: '2024-01-15T09:15:00Z',\n    createdAt: '2023-03-15T00:00:00Z',\n    updatedAt: '2024-01-15T09:15:00Z',\n  },\n];\n\n// 金蝶产品数据\nexport const mockProducts: Product[] = [\n  {\n    id: '1',\n    name: '金蝶云星辰标准版',\n    description: '适用于小微企业的云端财务管理软件，提供完整的财务核算、报表分析等功能',\n    sku: 'KIS-CLOUD-STD',\n    category: {\n      id: '1',\n      name: '财务软件',\n      description: '企业财务管理软件',\n      level: 1,\n      path: '/financial-software',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '1',\n        productId: '1',\n        name: '用户数量',\n        value: '3用户',\n        type: 'select',\n        required: true,\n        options: ['1用户', '3用户', '5用户', '10用户'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '2',\n        productId: '1',\n        name: '账套数量',\n        value: '1个账套',\n        type: 'select',\n        required: true,\n        options: ['1个账套', '3个账套', '5个账套'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '3',\n        productId: '1',\n        name: '存储空间',\n        value: '10GB',\n        type: 'select',\n        required: true,\n        options: ['5GB', '10GB', '20GB', '50GB'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '4',\n        productId: '1',\n        name: '服务期限',\n        value: '1年',\n        type: 'select',\n        required: true,\n        options: ['1年', '2年', '3年'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '5',\n        productId: '1',\n        name: '技术支持',\n        value: '标准支持',\n        type: 'select',\n        required: true,\n        options: ['标准支持', '高级支持', '专属支持'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '1',\n        productId: '1',\n        url: 'https://via.placeholder.com/400x300?text=金蝶云星辰',\n        alt: '金蝶云星辰标准版',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 3980,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['财务软件', '云端', '小微企业', '热门'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n  {\n    id: '2',\n    name: '金蝶精斗云专业版',\n    description: '一体化云端企业管理软件，集成进销存、财务、CRM等功能模块',\n    sku: 'JDY-PRO-001',\n    category: {\n      id: '2',\n      name: '企业管理软件',\n      description: '一体化企业管理解决方案',\n      level: 1,\n      path: '/enterprise-software',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '6',\n        productId: '2',\n        name: '用户数量',\n        value: '5用户',\n        type: 'select',\n        required: true,\n        options: ['3用户', '5用户', '10用户', '20用户'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '7',\n        productId: '2',\n        name: '功能模块',\n        value: '进销存+财务',\n        type: 'select',\n        required: true,\n        options: ['进销存', '进销存+财务', '进销存+财务+CRM', '全功能版'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '8',\n        productId: '2',\n        name: '存储空间',\n        value: '20GB',\n        type: 'select',\n        required: true,\n        options: ['10GB', '20GB', '50GB', '100GB'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '9',\n        productId: '2',\n        name: 'API调用',\n        value: '5000次/月',\n        type: 'select',\n        required: false,\n        options: ['1000次/月', '5000次/月', '10000次/月', '不限'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '2',\n        productId: '2',\n        url: 'https://via.placeholder.com/400x300?text=金蝶精斗云',\n        alt: '金蝶精斗云专业版',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 6980,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['企业管理', '一体化', '云端', '推荐'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n  {\n    id: '3',\n    name: '金蝶云星辰专业版',\n    description: '适用于中小企业的专业财务管理软件，支持多账套、多用户协同办公',\n    sku: 'KIS-CLOUD-PRO',\n    category: {\n      id: '1',\n      name: '财务软件',\n      description: '企业财务管理软件',\n      level: 1,\n      path: '/financial-software',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '10',\n        productId: '3',\n        name: '用户数量',\n        value: '10用户',\n        type: 'select',\n        required: true,\n        options: ['5用户', '10用户', '20用户', '50用户'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '11',\n        productId: '3',\n        name: '账套数量',\n        value: '5个账套',\n        type: 'select',\n        required: true,\n        options: ['3个账套', '5个账套', '10个账套', '不限'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '12',\n        productId: '3',\n        name: '功能模块',\n        value: '全功能版',\n        type: 'select',\n        required: true,\n        options: ['基础版', '标准版', '全功能版'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '3',\n        productId: '3',\n        url: 'https://via.placeholder.com/400x300?text=云星辰专业版',\n        alt: '金蝶云星辰专业版',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 8980,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['财务软件', '专业版', '中小企业'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n  {\n    id: '4',\n    name: '金蝶K/3 Cloud',\n    description: '大中型企业数字化转型平台，提供全面的ERP解决方案',\n    sku: 'K3-CLOUD-001',\n    category: {\n      id: '3',\n      name: 'ERP系统',\n      description: '企业资源规划系统',\n      level: 1,\n      path: '/erp-systems',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n    },\n    specifications: [\n      {\n        id: '13',\n        productId: '4',\n        name: '用户数量',\n        value: '100用户',\n        type: 'select',\n        required: true,\n        options: ['50用户', '100用户', '200用户', '500用户', '不限'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n      {\n        id: '14',\n        productId: '4',\n        name: '部署方式',\n        value: '云端部署',\n        type: 'select',\n        required: true,\n        options: ['云端部署', '本地部署', '混合部署'],\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    images: [\n      {\n        id: '4',\n        productId: '4',\n        url: 'https://via.placeholder.com/400x300?text=K3+Cloud',\n        alt: '金蝶K/3 Cloud',\n        isPrimary: true,\n        order: 1,\n        createdAt: '2024-01-01',\n        updatedAt: '2024-01-01',\n      },\n    ],\n    basePrice: 58000,\n    currency: 'CNY',\n    status: 'active',\n    tags: ['ERP', '大中型企业', '数字化转型'],\n    version: '1.0',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n];\n\n// 模拟客户数据\nexport const mockCustomers: Customer[] = [\n  {\n    id: '1',\n    name: '王总',\n    email: '<EMAIL>',\n    phone: '+86 138-0013-8000',\n    company: '深圳明星科技有限公司',\n    address: {\n      street: '南山区科技园南区',\n      city: '深圳',\n      state: '广东',\n      country: '中国',\n      postalCode: '518000',\n    },\n    tier: 'gold',\n    creditLimit: 500000,\n    paymentTerms: '30天',\n    salesPersonId: '2',\n    preferences: {\n      currency: 'CNY',\n      language: 'zh-CN',\n      timezone: 'Asia/Shanghai',\n      communicationChannel: 'email',\n      notifications: {\n        quoteUpdates: true,\n        promotions: true,\n        newsletters: false,\n      },\n    },\n    status: 'active',\n    createdAt: '2023-01-15T00:00:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '+86 138-0013-8001',\n    company: '腾讯科技',\n    address: {\n      street: '科技园南区',\n      city: '深圳',\n      state: '广东',\n      country: '中国',\n      postalCode: '518000',\n    },\n    tier: 'gold',\n    creditLimit: 3000000,\n    paymentTerms: '15天',\n    salesPersonId: '2',\n    preferences: {\n      currency: 'CNY',\n      language: 'zh-CN',\n      timezone: 'Asia/Shanghai',\n      communicationChannel: 'email',\n      notifications: {\n        quoteUpdates: true,\n        promotions: false,\n        newsletters: true,\n      },\n    },\n    status: 'active',\n    createdAt: '2023-03-20T00:00:00Z',\n    updatedAt: '2024-01-10T14:20:00Z',\n  },\n];\n\n// 模拟报价数据\nexport const mockQuotes: Quote[] = [\n  {\n    id: '1',\n    quoteNumber: 'Q-2024-001',\n    customerId: '1',\n    customerName: '阿里巴巴集团',\n    customerEmail: '<EMAIL>',\n    salesPersonId: '2',\n    items: [\n      {\n        id: '1',\n        quoteId: '1',\n        productId: '1',\n        productName: 'Dell PowerEdge R750 服务器',\n        configurationId: 'config-1',\n        quantity: 2,\n        unitPrice: 25000,\n        discountPercentage: 10,\n        discountAmount: 5000,\n        lineTotal: 45000,\n        specifications: {\n          'CPU': 'Intel Xeon Silver 4314',\n          '内存': '32GB DDR4',\n        },\n        createdAt: '2024-01-15',\n        updatedAt: '2024-01-15',\n      },\n    ],\n    subtotal: 50000,\n    discountAmount: 5000,\n    taxAmount: 4500,\n    shippingAmount: 0,\n    total: 49500,\n    currency: 'CNY',\n    status: 'sent',\n    validUntil: '2024-02-15',\n    notes: '此报价包含标准配置，如需定制请联系销售人员。',\n    terms: '1. 报价有效期30天\\n2. 付款方式：预付30%，发货前付清余款',\n    templateId: 'template-1',\n    version: 1,\n    parentQuoteId: undefined,\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T15:45:00Z',\n  },\n];\n\n// 模拟配置数据\nexport const mockConfigurations: Configuration[] = [\n  {\n    id: 'config-1',\n    name: '企业级服务器标准配置',\n    description: '适用于中型企业的标准服务器配置方案',\n    productId: '1',\n    items: [\n      {\n        id: 'item-1',\n        configurationId: 'config-1',\n        productId: '1',\n        quantity: 1,\n        specifications: {\n          'CPU': 'Intel Xeon Silver 4314',\n          '内存': '32GB DDR4',\n        },\n        price: 25000,\n        position: { x: 0, y: 0 },\n        createdAt: '2024-01-15',\n        updatedAt: '2024-01-15',\n      },\n    ],\n    rules: [],\n    status: 'active',\n    version: '1.0',\n    templateId: undefined,\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n  },\n];\n\n// API响应模拟函数\nexport const createMockApiResponse = <T>(data: T, success = true, message?: string) => ({\n  success,\n  data,\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: Array.isArray(data) ? {\n    page: 1,\n    pageSize: 20,\n    total: (data as any[]).length,\n    totalPages: Math.ceil((data as any[]).length / 20),\n  } : undefined,\n});\n\n// 分页响应模拟函数\nexport const createMockPaginatedResponse = <T>(\n  data: T[],\n  page: number = 1,\n  pageSize: number = 20,\n  success = true,\n  message?: string\n) => ({\n  success,\n  data: data.slice((page - 1) * pageSize, page * pageSize),\n  message,\n  errors: success ? undefined : ['模拟错误'],\n  pagination: {\n    page,\n    pageSize,\n    total: data.length,\n    totalPages: Math.ceil(data.length / pageSize),\n  },\n});\n\n// 模拟登录响应\nexport const mockLoginResponse = {\n  success: true,\n  data: {\n    user: mockUsers[0],\n    token: 'mock_jwt_token_' + Date.now(),\n  },\n  message: '登录成功',\n};\n\n// 模拟延迟函数\nexport const mockDelay = (ms: number = 1000) => \n  new Promise(resolve => setTimeout(resolve, ms));\n"], "mappings": "AAAA;;AAGA;AACA,OAAO,MAAMA,SAAiB,GAAG,CAC/B;EACEC,EAAE,EAAE,GAAG;EACPC,QAAQ,EAAE,OAAO;EACjBC,KAAK,EAAE,mBAAmB;EAC1BC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,8CAA8C;EACtDC,KAAK,EAAE,CACL;IACEN,EAAE,EAAE,OAAO;IACXO,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDF,WAAW,EAAE,EAAE;EACfG,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,sBAAsB;EACnCH,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,sBAAsB;EAC7BC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,4CAA4C;EACpDC,KAAK,EAAE,CACL;IACEN,EAAE,EAAE,eAAe;IACnBO,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,WAAW;IACxBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDF,WAAW,EAAE,EAAE;EACfG,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,sBAAsB;EACnCH,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMG,YAAuB,GAAG,CACrC;EACEd,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,oCAAoC;EACjDO,GAAG,EAAE,eAAe;EACpBC,QAAQ,EAAE;IACRhB,EAAE,EAAE,GAAG;IACPO,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,UAAU;IACvBS,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,qBAAqB;IAC3BR,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EACDQ,cAAc,EAAE,CACd;IACEnB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACtCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACxCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3Bd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDc,MAAM,EAAE,CACN;IACEzB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdM,GAAG,EAAE,gDAAgD;IACrDC,GAAG,EAAE,UAAU;IACfC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,CAAC;IACRnB,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDmB,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfnB,MAAM,EAAE,QAAQ;EAChBoB,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;EAClCC,OAAO,EAAE,KAAK;EACdvB,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE;AACb,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,+BAA+B;EAC5CO,GAAG,EAAE,aAAa;EAClBC,QAAQ,EAAE;IACRhB,EAAE,EAAE,GAAG;IACPO,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,aAAa;IAC1BS,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,sBAAsB;IAC5BR,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EACDQ,cAAc,EAAE,CACd;IACEnB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACvCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC;IAChDd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IAC1Cd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,OAAO;IACbc,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;IACjDd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDc,MAAM,EAAE,CACN;IACEzB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdM,GAAG,EAAE,gDAAgD;IACrDC,GAAG,EAAE,UAAU;IACfC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,CAAC;IACRnB,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDmB,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfnB,MAAM,EAAE,QAAQ;EAChBoB,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACjCC,OAAO,EAAE,KAAK;EACdvB,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE;AACb,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,gCAAgC;EAC7CO,GAAG,EAAE,eAAe;EACpBC,QAAQ,EAAE;IACRhB,EAAE,EAAE,GAAG;IACPO,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,UAAU;IACvBS,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,qBAAqB;IAC3BR,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EACDQ,cAAc,EAAE,CACd;IACEnB,EAAE,EAAE,IAAI;IACRoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACxCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,IAAI;IACRoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;IACxCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,IAAI;IACRoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC/Bd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDc,MAAM,EAAE,CACN;IACEzB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdM,GAAG,EAAE,iDAAiD;IACtDC,GAAG,EAAE,UAAU;IACfC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,CAAC;IACRnB,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDmB,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfnB,MAAM,EAAE,QAAQ;EAChBoB,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EAC7BC,OAAO,EAAE,KAAK;EACdvB,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE;AACb,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,2BAA2B;EACxCO,GAAG,EAAE,cAAc;EACnBC,QAAQ,EAAE;IACRhB,EAAE,EAAE,GAAG;IACPO,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,UAAU;IACvBS,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,cAAc;IACpBR,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EACDQ,cAAc,EAAE,CACd;IACEnB,EAAE,EAAE,IAAI;IACRoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;IAClDd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEX,EAAE,EAAE,IAAI;IACRoB,SAAS,EAAE,GAAG;IACdb,IAAI,EAAE,MAAM;IACZc,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjCd,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDc,MAAM,EAAE,CACN;IACEzB,EAAE,EAAE,GAAG;IACPoB,SAAS,EAAE,GAAG;IACdM,GAAG,EAAE,mDAAmD;IACxDC,GAAG,EAAE,aAAa;IAClBC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,CAAC;IACRnB,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDmB,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfnB,MAAM,EAAE,QAAQ;EAChBoB,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;EAC/BC,OAAO,EAAE,KAAK;EACdvB,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMuB,aAAyB,GAAG,CACvC;EACElC,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,IAAI;EACVL,KAAK,EAAE,uBAAuB;EAC9BiC,KAAK,EAAE,mBAAmB;EAC1BC,OAAO,EAAE,YAAY;EACrBC,OAAO,EAAE;IACPC,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC;EACDC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,MAAM;EACnBC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,GAAG;EAClBC,WAAW,EAAE;IACXhB,QAAQ,EAAE,KAAK;IACfiB,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,eAAe;IACzBC,oBAAoB,EAAE,OAAO;IAC7BC,aAAa,EAAE;MACbC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE;IACf;EACF,CAAC;EACD1C,MAAM,EAAE,QAAQ;EAChBF,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPO,IAAI,EAAE,IAAI;EACVL,KAAK,EAAE,kBAAkB;EACzBiC,KAAK,EAAE,mBAAmB;EAC1BC,OAAO,EAAE,MAAM;EACfC,OAAO,EAAE;IACPC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EACd,CAAC;EACDC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,OAAO;EACpBC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,GAAG;EAClBC,WAAW,EAAE;IACXhB,QAAQ,EAAE,KAAK;IACfiB,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,eAAe;IACzBC,oBAAoB,EAAE,OAAO;IAC7BC,aAAa,EAAE;MACbC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf;EACF,CAAC;EACD1C,MAAM,EAAE,QAAQ;EAChBF,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAM4C,UAAmB,GAAG,CACjC;EACEvD,EAAE,EAAE,GAAG;EACPwD,WAAW,EAAE,YAAY;EACzBC,UAAU,EAAE,GAAG;EACfC,YAAY,EAAE,QAAQ;EACtBC,aAAa,EAAE,yBAAyB;EACxCb,aAAa,EAAE,GAAG;EAClBc,KAAK,EAAE,CACL;IACE5D,EAAE,EAAE,GAAG;IACP6D,OAAO,EAAE,GAAG;IACZzC,SAAS,EAAE,GAAG;IACd0C,WAAW,EAAE,yBAAyB;IACtCC,eAAe,EAAE,UAAU;IAC3BC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,KAAK;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,KAAK;IAChBjD,cAAc,EAAE;MACd,KAAK,EAAE,wBAAwB;MAC/B,IAAI,EAAE;IACR,CAAC;IACDT,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACD0D,QAAQ,EAAE,KAAK;EACfF,cAAc,EAAE,IAAI;EACpBG,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC;EACjBC,KAAK,EAAE,KAAK;EACZzC,QAAQ,EAAE,KAAK;EACfnB,MAAM,EAAE,MAAM;EACd6D,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE,wBAAwB;EAC/BC,KAAK,EAAE,oCAAoC;EAC3CC,UAAU,EAAE,YAAY;EACxB3C,OAAO,EAAE,CAAC;EACV4C,aAAa,EAAEC,SAAS;EACxBpE,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMoE,kBAAmC,GAAG,CACjD;EACE/E,EAAE,EAAE,UAAU;EACdO,IAAI,EAAE,YAAY;EAClBC,WAAW,EAAE,mBAAmB;EAChCY,SAAS,EAAE,GAAG;EACdwC,KAAK,EAAE,CACL;IACE5D,EAAE,EAAE,QAAQ;IACZ+D,eAAe,EAAE,UAAU;IAC3B3C,SAAS,EAAE,GAAG;IACd4C,QAAQ,EAAE,CAAC;IACX7C,cAAc,EAAE;MACd,KAAK,EAAE,wBAAwB;MAC/B,IAAI,EAAE;IACR,CAAC;IACD6D,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACxBzE,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EACDyE,KAAK,EAAE,EAAE;EACTxE,MAAM,EAAE,QAAQ;EAChBqB,OAAO,EAAE,KAAK;EACd2C,UAAU,EAAEE,SAAS;EACrBpE,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAM0E,qBAAqB,GAAGA,CAAIC,IAAO,EAAEC,OAAO,GAAG,IAAI,EAAEC,OAAgB,MAAM;EACtFD,OAAO;EACPD,IAAI;EACJE,OAAO;EACPC,MAAM,EAAEF,OAAO,GAAGT,SAAS,GAAG,CAAC,MAAM,CAAC;EACtCY,UAAU,EAAEC,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,GAAG;IAChCO,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZtB,KAAK,EAAGc,IAAI,CAAWS,MAAM;IAC7BC,UAAU,EAAEC,IAAI,CAACC,IAAI,CAAEZ,IAAI,CAAWS,MAAM,GAAG,EAAE;EACnD,CAAC,GAAGjB;AACN,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMqB,2BAA2B,GAAGA,CACzCb,IAAS,EACTO,IAAY,GAAG,CAAC,EAChBC,QAAgB,GAAG,EAAE,EACrBP,OAAO,GAAG,IAAI,EACdC,OAAgB,MACZ;EACJD,OAAO;EACPD,IAAI,EAAEA,IAAI,CAACc,KAAK,CAAC,CAACP,IAAI,GAAG,CAAC,IAAIC,QAAQ,EAAED,IAAI,GAAGC,QAAQ,CAAC;EACxDN,OAAO;EACPC,MAAM,EAAEF,OAAO,GAAGT,SAAS,GAAG,CAAC,MAAM,CAAC;EACtCY,UAAU,EAAE;IACVG,IAAI;IACJC,QAAQ;IACRtB,KAAK,EAAEc,IAAI,CAACS,MAAM;IAClBC,UAAU,EAAEC,IAAI,CAACC,IAAI,CAACZ,IAAI,CAACS,MAAM,GAAGD,QAAQ;EAC9C;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMO,iBAAiB,GAAG;EAC/Bd,OAAO,EAAE,IAAI;EACbD,IAAI,EAAE;IACJgB,IAAI,EAAEvG,SAAS,CAAC,CAAC,CAAC;IAClBwG,KAAK,EAAE,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAAC;EACtC,CAAC;EACDjB,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMkB,SAAS,GAAGA,CAACC,EAAU,GAAG,IAAI,KACzC,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}