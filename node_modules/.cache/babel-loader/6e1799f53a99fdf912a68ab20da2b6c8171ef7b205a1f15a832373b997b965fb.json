{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Row, Col, Button, Tag, Typography, Space, Divider, Tabs, List, Modal, Form, Select, InputNumber, Alert, Statistic } from 'antd';\nimport { StarOutlined, CloudOutlined, TeamOutlined, SettingOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { mockProducts } from '@/data/mockData';\nimport { calculateKingsoftPrice } from '@/data/kingsoftPricingRules';\nimport { allKingsoftConfigurations, recommendConfiguration } from '@/data/kingsoftConfigurations';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\nconst PriceCalculator = ({\n  productSku,\n  basePrice\n}) => {\n  _s();\n  const [quantity, setQuantity] = useState(1);\n  const [customerTier, setCustomerTier] = useState('standard');\n  const [isRenewal, setIsRenewal] = useState(false);\n  const [industry, setIndustry] = useState();\n  const priceResult = calculateKingsoftPrice(basePrice, productSku, quantity, customerTier, isRenewal, undefined, industry);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u4EF7\\u683C\\u8BA1\\u7B97\\u5668\",\n    size: \"small\",\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u8D2D\\u4E70\\u6570\\u91CF\",\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              value: quantity,\n              onChange: value => setQuantity(value || 1),\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u5BA2\\u6237\\u7C7B\\u578B\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: customerTier,\n              onChange: setCustomerTier,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"standard\",\n                children: \"\\u6807\\u51C6\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"gold\",\n                children: \"\\u91D1\\u724C\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"platinum\",\n                children: \"\\u767D\\u91D1\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"enterprise\",\n                children: \"\\u4F01\\u4E1A\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u662F\\u5426\\u7EED\\u8D39\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: isRenewal,\n              onChange: setIsRenewal,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: false,\n                children: \"\\u65B0\\u8D2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: true,\n                children: \"\\u7EED\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u884C\\u4E1A\\u7C7B\\u578B\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: industry,\n              onChange: setIndustry,\n              allowClear: true,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"manufacturing\",\n                children: \"\\u5236\\u9020\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"retail\",\n                children: \"\\u96F6\\u552E\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"service\",\n                children: \"\\u670D\\u52A1\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"technology\",\n                children: \"\\u79D1\\u6280\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u539F\\u4EF7\",\n          value: priceResult.originalPrice,\n          precision: 2,\n          prefix: \"\\xA5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u4F18\\u60E0\\u91D1\\u989D\",\n          value: priceResult.totalDiscount,\n          precision: 2,\n          prefix: \"\\xA5\",\n          valueStyle: {\n            color: '#cf1322'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u6700\\u7EC8\\u4EF7\\u683C\",\n          value: priceResult.finalPrice,\n          precision: 2,\n          prefix: \"\\xA5\",\n          valueStyle: {\n            color: '#3f8600'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), priceResult.appliedRules.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u5DF2\\u5E94\\u7528\\u7684\\u4F18\\u60E0\\u89C4\\u5219\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        size: \"small\",\n        dataSource: priceResult.appliedRules,\n        renderItem: rule => /*#__PURE__*/_jsxDEV(List.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: rule.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: rule.discount.type === 'percentage' ? `${rule.discount.value}%折扣` : `¥${rule.discount.value}优惠`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(PriceCalculator, \"mCyjl3Ke7J8/LFNKf5v19mYPPSk=\");\n_c = PriceCalculator;\nconst KingsoftProductShowcase = () => {\n  _s2();\n  var _currentProduct$speci, _currentProduct$tags;\n  const [selectedProduct, setSelectedProduct] = useState('1');\n  const [configModalVisible, setConfigModalVisible] = useState(false);\n  const kingsoftProducts = mockProducts.filter(p => {\n    var _p$sku, _p$sku2, _p$sku3;\n    return ((_p$sku = p.sku) === null || _p$sku === void 0 ? void 0 : _p$sku.includes('KIS')) || ((_p$sku2 = p.sku) === null || _p$sku2 === void 0 ? void 0 : _p$sku2.includes('JDY')) || ((_p$sku3 = p.sku) === null || _p$sku3 === void 0 ? void 0 : _p$sku3.includes('K3'));\n  });\n  const currentProduct = kingsoftProducts.find(p => p.id === selectedProduct);\n  const productConfigurations = allKingsoftConfigurations.filter(config => config.productId === selectedProduct);\n  const handleConfigureProduct = () => {\n    setConfigModalVisible(true);\n  };\n  const handleRecommendation = () => {\n    const recommendations = recommendConfiguration('小微企业', 'technology', 10000, ['财务管理', '进销存']);\n    Modal.info({\n      title: '推荐配置',\n      width: 600,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n          children: \"\\u6839\\u636E\\u60A8\\u7684\\u9700\\u6C42\\uFF0C\\u6211\\u4EEC\\u4E3A\\u60A8\\u63A8\\u8350\\u4EE5\\u4E0B\\u914D\\u7F6E\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), recommendations.map(config => /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          style: {\n            marginBottom: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: config.configurationName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: config.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u4EF7\\u683C: \\xA5\", config.basePrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, config.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u91D1\\u8776\\u4EA7\\u54C1\\u7CFB\\u5217\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: kingsoftProducts,\n            renderItem: product => {\n              var _product$tags;\n              return /*#__PURE__*/_jsxDEV(List.Item, {\n                style: {\n                  cursor: 'pointer',\n                  backgroundColor: selectedProduct === product.id ? '#f0f0f0' : 'transparent',\n                  padding: '8px',\n                  borderRadius: '4px'\n                },\n                onClick: () => setSelectedProduct(product.id),\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  title: product.name,\n                  description: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\xA5\", product.basePrice]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 25\n                    }, this), (_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n                      size: \"small\",\n                      children: tag\n                    }, tag, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 18,\n        children: currentProduct && /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CloudOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), currentProduct.name, /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: currentProduct.sku\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 48\n              }, this),\n              onClick: handleConfigureProduct,\n              children: \"\\u4EA7\\u54C1\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 33\n              }, this),\n              onClick: handleRecommendation,\n              children: \"\\u667A\\u80FD\\u63A8\\u8350\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 16,\n              children: /*#__PURE__*/_jsxDEV(Tabs, {\n                defaultActiveKey: \"overview\",\n                children: [/*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: \"\\u4EA7\\u54C1\\u6982\\u8FF0\",\n                  children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                    children: currentProduct.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Title, {\n                    level: 5,\n                    children: \"\\u4EA7\\u54C1\\u7279\\u8272\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    gutter: 16,\n                    children: (_currentProduct$speci = currentProduct.specifications) === null || _currentProduct$speci === void 0 ? void 0 : _currentProduct$speci.map((spec, index) => /*#__PURE__*/_jsxDEV(Col, {\n                      span: 12,\n                      style: {\n                        marginBottom: 16\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        children: /*#__PURE__*/_jsxDEV(Space, {\n                          children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 275,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(Text, {\n                              strong: true,\n                              children: spec.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 277,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 278,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              type: \"secondary\",\n                              children: spec.value\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 279,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 276,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 274,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 29\n                      }, this)\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Title, {\n                    level: 5,\n                    children: \"\\u4EA7\\u54C1\\u6807\\u7B7E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Space, {\n                    wrap: true,\n                    children: (_currentProduct$tags = currentProduct.tags) === null || _currentProduct$tags === void 0 ? void 0 : _currentProduct$tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"blue\",\n                      children: tag\n                    }, tag, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this)]\n                }, \"overview\", true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: \"\\u914D\\u7F6E\\u65B9\\u6848\",\n                  children: /*#__PURE__*/_jsxDEV(Row, {\n                    gutter: 16,\n                    children: productConfigurations.map(config => /*#__PURE__*/_jsxDEV(Col, {\n                      span: 12,\n                      style: {\n                        marginBottom: 16\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        title: config.configurationName,\n                        extra: /*#__PURE__*/_jsxDEV(Text, {\n                          strong: true,\n                          children: [\"\\xA5\", config.basePrice]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 302,\n                          columnNumber: 38\n                        }, this),\n                        children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                          ellipsis: {\n                            rows: 2\n                          },\n                          children: config.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 304,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Space, {\n                          wrap: true,\n                          children: config.recommendedFor.map(rec => /*#__PURE__*/_jsxDEV(Tag, {\n                            size: \"small\",\n                            children: rec\n                          }, rec, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 310,\n                            columnNumber: 35\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 308,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 314,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(List, {\n                          size: \"small\",\n                          dataSource: config.features.slice(0, 4),\n                          renderItem: feature => /*#__PURE__*/_jsxDEV(List.Item, {\n                            style: {\n                              padding: '2px 0'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Space, {\n                              children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                                style: {\n                                  color: '#52c41a'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 322,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                                style: {\n                                  fontSize: '12px'\n                                },\n                                children: feature\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 323,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 321,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 320,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 316,\n                          columnNumber: 31\n                        }, this), config.features.length > 4 && /*#__PURE__*/_jsxDEV(Text, {\n                          type: \"secondary\",\n                          style: {\n                            fontSize: '12px'\n                          },\n                          children: [\"\\u8FD8\\u6709 \", config.features.length - 4, \" \\u9879\\u529F\\u80FD...\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 330,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 29\n                      }, this)\n                    }, config.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this)\n                }, \"configurations\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: \"\\u6280\\u672F\\u652F\\u6301\",\n                  children: [/*#__PURE__*/_jsxDEV(Alert, {\n                    message: \"\\u4E13\\u4E1A\\u6280\\u672F\\u652F\\u6301\",\n                    description: \"\\u91D1\\u8776\\u63D0\\u4F9B\\u5168\\u65B9\\u4F4D\\u7684\\u6280\\u672F\\u652F\\u6301\\u670D\\u52A1\\uFF0C\\u786E\\u4FDD\\u60A8\\u7684\\u4E1A\\u52A1\\u987A\\u5229\\u8FD0\\u884C\",\n                    type: \"info\",\n                    showIcon: true,\n                    style: {\n                      marginBottom: 16\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    gutter: 16,\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      span: 8,\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        title: \"\\u5728\\u7EBF\\u652F\\u6301\",\n                        children: /*#__PURE__*/_jsxDEV(Space, {\n                          direction: \"vertical\",\n                          children: [/*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u5DE5\\u4F5C\\u65E5 9:00-18:00\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 353,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u5728\\u7EBF\\u5BA2\\u670D\\u54CD\\u5E94\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 354,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u8FDC\\u7A0B\\u534F\\u52A9\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 355,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 352,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      span: 8,\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        title: \"\\u7535\\u8BDD\\u652F\\u6301\",\n                        children: /*#__PURE__*/_jsxDEV(Space, {\n                          direction: \"vertical\",\n                          children: [/*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 400\\u5BA2\\u670D\\u70ED\\u7EBF\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 362,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u6280\\u672F\\u4E13\\u5BB6\\u652F\\u6301\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 363,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u7D27\\u6025\\u95EE\\u9898\\u5904\\u7406\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 364,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 361,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      span: 8,\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        title: \"\\u57F9\\u8BAD\\u670D\\u52A1\",\n                        children: /*#__PURE__*/_jsxDEV(Space, {\n                          direction: \"vertical\",\n                          children: [/*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u4EA7\\u54C1\\u4F7F\\u7528\\u57F9\\u8BAD\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 371,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u5728\\u7EBF\\u89C6\\u9891\\u6559\\u7A0B\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 372,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Text, {\n                            children: \"\\u2022 \\u73B0\\u573A\\u5B9E\\u65BD\\u6307\\u5BFC\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 373,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)]\n                }, \"support\", true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(PriceCalculator, {\n                productSku: currentProduct.sku || '',\n                basePrice: currentProduct.basePrice\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EA7\\u54C1\\u914D\\u7F6E\\u5411\\u5BFC\",\n      visible: configModalVisible,\n      onCancel: () => setConfigModalVisible(false),\n      width: 800,\n      footer: null,\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u914D\\u7F6E\\u5411\\u5BFC\",\n        description: \"\\u8BF7\\u6839\\u636E\\u60A8\\u7684\\u4E1A\\u52A1\\u9700\\u6C42\\u9009\\u62E9\\u5408\\u9002\\u7684\\u4EA7\\u54C1\\u914D\\u7F6E\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: \"\\u914D\\u7F6E\\u5411\\u5BFC\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s2(KingsoftProductShowcase, \"QLMPAF6/gWXQMEgor7dPkMD2mt4=\");\n_c2 = KingsoftProductShowcase;\nexport default KingsoftProductShowcase;\nvar _c, _c2;\n$RefreshReg$(_c, \"PriceCalculator\");\n$RefreshReg$(_c2, \"KingsoftProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Row", "Col", "<PERSON><PERSON>", "Tag", "Typography", "Space", "Divider", "Tabs", "List", "Modal", "Form", "Select", "InputNumber", "<PERSON><PERSON>", "Statistic", "StarOutlined", "CloudOutlined", "TeamOutlined", "SettingOutlined", "CheckCircleOutlined", "mockProducts", "calculateKingsoftPrice", "allKingsoftConfigurations", "recommendConfiguration", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "TabPane", "Option", "PriceCalculator", "productSku", "basePrice", "_s", "quantity", "setQuantity", "customerTier", "setCustomerTier", "isRenewal", "set<PERSON>s<PERSON>enewal", "industry", "setIndustry", "priceResult", "undefined", "title", "size", "children", "layout", "gutter", "span", "<PERSON><PERSON>", "label", "min", "value", "onChange", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "allowClear", "originalPrice", "precision", "prefix", "totalDiscount", "valueStyle", "color", "finalPrice", "appliedRules", "length", "strong", "dataSource", "renderItem", "rule", "name", "discount", "type", "_c", "KingsoftProductShowcase", "_s2", "_currentProduct$speci", "_currentProduct$tags", "selectedProduct", "setSelectedProduct", "configModalVisible", "setConfigModalVisible", "kingsoftProducts", "filter", "p", "_p$sku", "_p$sku2", "_p$sku3", "sku", "includes", "currentProduct", "find", "id", "productConfigurations", "config", "productId", "handleConfigureProduct", "handleRecommendation", "recommendations", "info", "content", "map", "marginBottom", "level", "configurationName", "description", "padding", "product", "_product$tags", "cursor", "backgroundColor", "borderRadius", "onClick", "Meta", "tags", "tag", "extra", "icon", "defaultActiveKey", "tab", "specifications", "spec", "index", "wrap", "ellipsis", "rows", "recommendedFor", "rec", "features", "slice", "feature", "fontSize", "message", "showIcon", "direction", "visible", "onCancel", "footer", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Tag,\n  Typography,\n  Space,\n  Divider,\n  Tabs,\n  List,\n  Badge,\n  Tooltip,\n  Modal,\n  Form,\n  Select,\n  InputNumber,\n  Alert,\n  Statistic,\n} from 'antd';\nimport {\n  ShoppingCartOutlined,\n  StarOutlined,\n  CloudOutlined,\n  SafetyOutlined,\n  TeamOutlined,\n  SettingOutlined,\n  CheckCircleOutlined,\n  InfoCircleOutlined,\n} from '@ant-design/icons';\nimport { mockProducts } from '@/data/mockData';\nimport { calculateKingsoftPrice } from '@/data/kingsoftPricingRules';\nimport { allKingsoftConfigurations, recommendConfiguration } from '@/data/kingsoftConfigurations';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TabPane } = Tabs;\nconst { Option } = Select;\n\ninterface PriceCalculatorProps {\n  productSku: string;\n  basePrice: number;\n}\n\nconst PriceCalculator: React.FC<PriceCalculatorProps> = ({ productSku, basePrice }) => {\n  const [quantity, setQuantity] = useState(1);\n  const [customerTier, setCustomerTier] = useState('standard');\n  const [isRenewal, setIsRenewal] = useState(false);\n  const [industry, setIndustry] = useState<string>();\n\n  const priceResult = calculateKingsoftPrice(\n    basePrice,\n    productSku,\n    quantity,\n    customerTier,\n    isRenewal,\n    undefined,\n    industry\n  );\n\n  return (\n    <Card title=\"价格计算器\" size=\"small\">\n      <Form layout=\"vertical\">\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item label=\"购买数量\">\n              <InputNumber\n                min={1}\n                value={quantity}\n                onChange={(value) => setQuantity(value || 1)}\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item label=\"客户类型\">\n              <Select value={customerTier} onChange={setCustomerTier}>\n                <Option value=\"standard\">标准客户</Option>\n                <Option value=\"gold\">金牌客户</Option>\n                <Option value=\"platinum\">白金客户</Option>\n                <Option value=\"enterprise\">企业客户</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item label=\"是否续费\">\n              <Select value={isRenewal} onChange={setIsRenewal}>\n                <Option value={false}>新购</Option>\n                <Option value={true}>续费</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item label=\"行业类型\">\n              <Select value={industry} onChange={setIndustry} allowClear>\n                <Option value=\"manufacturing\">制造业</Option>\n                <Option value=\"retail\">零售业</Option>\n                <Option value=\"service\">服务业</Option>\n                <Option value=\"technology\">科技业</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      </Form>\n\n      <Divider />\n\n      <Row gutter={16}>\n        <Col span={8}>\n          <Statistic\n            title=\"原价\"\n            value={priceResult.originalPrice}\n            precision={2}\n            prefix=\"¥\"\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"优惠金额\"\n            value={priceResult.totalDiscount}\n            precision={2}\n            prefix=\"¥\"\n            valueStyle={{ color: '#cf1322' }}\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"最终价格\"\n            value={priceResult.finalPrice}\n            precision={2}\n            prefix=\"¥\"\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Col>\n      </Row>\n\n      {priceResult.appliedRules.length > 0 && (\n        <>\n          <Divider />\n          <Text strong>已应用的优惠规则：</Text>\n          <List\n            size=\"small\"\n            dataSource={priceResult.appliedRules}\n            renderItem={(rule) => (\n              <List.Item>\n                <Space>\n                  <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                  <Text>{rule.name}</Text>\n                  <Tag color=\"green\">\n                    {rule.discount.type === 'percentage' ? `${rule.discount.value}%折扣` : `¥${rule.discount.value}优惠`}\n                  </Tag>\n                </Space>\n              </List.Item>\n            )}\n          />\n        </>\n      )}\n    </Card>\n  );\n};\n\nconst KingsoftProductShowcase: React.FC = () => {\n  const [selectedProduct, setSelectedProduct] = useState<string>('1');\n  const [configModalVisible, setConfigModalVisible] = useState(false);\n\n  const kingsoftProducts = mockProducts.filter(p => \n    p.sku?.includes('KIS') || p.sku?.includes('JDY') || p.sku?.includes('K3')\n  );\n\n  const currentProduct = kingsoftProducts.find(p => p.id === selectedProduct);\n  const productConfigurations = allKingsoftConfigurations.filter(\n    config => config.productId === selectedProduct\n  );\n\n  const handleConfigureProduct = () => {\n    setConfigModalVisible(true);\n  };\n\n  const handleRecommendation = () => {\n    const recommendations = recommendConfiguration(\n      '小微企业',\n      'technology',\n      10000,\n      ['财务管理', '进销存']\n    );\n    \n    Modal.info({\n      title: '推荐配置',\n      width: 600,\n      content: (\n        <div>\n          <Paragraph>根据您的需求，我们为您推荐以下配置：</Paragraph>\n          {recommendations.map(config => (\n            <Card key={config.id} size=\"small\" style={{ marginBottom: 8 }}>\n              <Title level={5}>{config.configurationName}</Title>\n              <Text>{config.description}</Text>\n              <br />\n              <Text strong>价格: ¥{config.basePrice}</Text>\n            </Card>\n          ))}\n        </div>\n      ),\n    });\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Row gutter={24}>\n        <Col span={6}>\n          <Card title=\"金蝶产品系列\" size=\"small\">\n            <List\n              dataSource={kingsoftProducts}\n              renderItem={(product) => (\n                <List.Item\n                  style={{\n                    cursor: 'pointer',\n                    backgroundColor: selectedProduct === product.id ? '#f0f0f0' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                  }}\n                  onClick={() => setSelectedProduct(product.id)}\n                >\n                  <List.Item.Meta\n                    title={product.name}\n                    description={\n                      <Space>\n                        <Text type=\"secondary\">¥{product.basePrice}</Text>\n                        {product.tags?.map(tag => (\n                          <Tag key={tag} size=\"small\">{tag}</Tag>\n                        ))}\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        <Col span={18}>\n          {currentProduct && (\n            <Card\n              title={\n                <Space>\n                  <CloudOutlined />\n                  {currentProduct.name}\n                  <Tag color=\"blue\">{currentProduct.sku}</Tag>\n                </Space>\n              }\n              extra={\n                <Space>\n                  <Button type=\"primary\" icon={<SettingOutlined />} onClick={handleConfigureProduct}>\n                    产品配置\n                  </Button>\n                  <Button icon={<StarOutlined />} onClick={handleRecommendation}>\n                    智能推荐\n                  </Button>\n                </Space>\n              }\n            >\n              <Row gutter={24}>\n                <Col span={16}>\n                  <Tabs defaultActiveKey=\"overview\">\n                    <TabPane tab=\"产品概述\" key=\"overview\">\n                      <Paragraph>{currentProduct.description}</Paragraph>\n                      \n                      <Title level={5}>产品特色</Title>\n                      <Row gutter={16}>\n                        {currentProduct.specifications?.map((spec, index) => (\n                          <Col span={12} key={index} style={{ marginBottom: 16 }}>\n                            <Card size=\"small\">\n                              <Space>\n                                <TeamOutlined />\n                                <div>\n                                  <Text strong>{spec.name}</Text>\n                                  <br />\n                                  <Text type=\"secondary\">{spec.value}</Text>\n                                </div>\n                              </Space>\n                            </Card>\n                          </Col>\n                        ))}\n                      </Row>\n\n                      <Title level={5}>产品标签</Title>\n                      <Space wrap>\n                        {currentProduct.tags?.map(tag => (\n                          <Tag key={tag} color=\"blue\">{tag}</Tag>\n                        ))}\n                      </Space>\n                    </TabPane>\n\n                    <TabPane tab=\"配置方案\" key=\"configurations\">\n                      <Row gutter={16}>\n                        {productConfigurations.map(config => (\n                          <Col span={12} key={config.id} style={{ marginBottom: 16 }}>\n                            <Card\n                              size=\"small\"\n                              title={config.configurationName}\n                              extra={<Text strong>¥{config.basePrice}</Text>}\n                            >\n                              <Paragraph ellipsis={{ rows: 2 }}>\n                                {config.description}\n                              </Paragraph>\n                              \n                              <Space wrap>\n                                {config.recommendedFor.map(rec => (\n                                  <Tag key={rec} size=\"small\">{rec}</Tag>\n                                ))}\n                              </Space>\n                              \n                              <Divider />\n                              \n                              <List\n                                size=\"small\"\n                                dataSource={config.features.slice(0, 4)}\n                                renderItem={(feature) => (\n                                  <List.Item style={{ padding: '2px 0' }}>\n                                    <Space>\n                                      <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                                      <Text style={{ fontSize: '12px' }}>{feature}</Text>\n                                    </Space>\n                                  </List.Item>\n                                )}\n                              />\n                              \n                              {config.features.length > 4 && (\n                                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                                  还有 {config.features.length - 4} 项功能...\n                                </Text>\n                              )}\n                            </Card>\n                          </Col>\n                        ))}\n                      </Row>\n                    </TabPane>\n\n                    <TabPane tab=\"技术支持\" key=\"support\">\n                      <Alert\n                        message=\"专业技术支持\"\n                        description=\"金蝶提供全方位的技术支持服务，确保您的业务顺利运行\"\n                        type=\"info\"\n                        showIcon\n                        style={{ marginBottom: 16 }}\n                      />\n                      \n                      <Row gutter={16}>\n                        <Col span={8}>\n                          <Card size=\"small\" title=\"在线支持\">\n                            <Space direction=\"vertical\">\n                              <Text>• 工作日 9:00-18:00</Text>\n                              <Text>• 在线客服响应</Text>\n                              <Text>• 远程协助</Text>\n                            </Space>\n                          </Card>\n                        </Col>\n                        <Col span={8}>\n                          <Card size=\"small\" title=\"电话支持\">\n                            <Space direction=\"vertical\">\n                              <Text>• 400客服热线</Text>\n                              <Text>• 技术专家支持</Text>\n                              <Text>• 紧急问题处理</Text>\n                            </Space>\n                          </Card>\n                        </Col>\n                        <Col span={8}>\n                          <Card size=\"small\" title=\"培训服务\">\n                            <Space direction=\"vertical\">\n                              <Text>• 产品使用培训</Text>\n                              <Text>• 在线视频教程</Text>\n                              <Text>• 现场实施指导</Text>\n                            </Space>\n                          </Card>\n                        </Col>\n                      </Row>\n                    </TabPane>\n                  </Tabs>\n                </Col>\n\n                <Col span={8}>\n                  <PriceCalculator\n                    productSku={currentProduct.sku || ''}\n                    basePrice={currentProduct.basePrice}\n                  />\n                </Col>\n              </Row>\n            </Card>\n          )}\n        </Col>\n      </Row>\n\n      <Modal\n        title=\"产品配置向导\"\n        visible={configModalVisible}\n        onCancel={() => setConfigModalVisible(false)}\n        width={800}\n        footer={null}\n      >\n        <Alert\n          message=\"配置向导\"\n          description=\"请根据您的业务需求选择合适的产品配置\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        {/* 这里可以添加配置向导的具体内容 */}\n        <Text>配置向导功能正在开发中...</Text>\n      </Modal>\n    </div>\n  );\n};\n\nexport default KingsoftProductShowcase;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,IAAI,EAGJC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,SAAS,QACJ,MAAM;AACb,SAEEC,YAAY,EACZC,aAAa,EAEbC,YAAY,EACZC,eAAe,EACfC,mBAAmB,QAEd,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,yBAAyB,EAAEC,sBAAsB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElG,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG1B,UAAU;AAC7C,MAAM;EAAE2B;AAAQ,CAAC,GAAGxB,IAAI;AACxB,MAAM;EAAEyB;AAAO,CAAC,GAAGrB,MAAM;AAOzB,MAAMsB,eAA+C,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAS,CAAC;EAElD,MAAM+C,WAAW,GAAGxB,sBAAsB,CACxCc,SAAS,EACTD,UAAU,EACVG,QAAQ,EACRE,YAAY,EACZE,SAAS,EACTK,SAAS,EACTH,QACF,CAAC;EAED,oBACElB,OAAA,CAAC1B,IAAI;IAACgD,KAAK,EAAC,gCAAO;IAACC,IAAI,EAAC,OAAO;IAAAC,QAAA,gBAC9BxB,OAAA,CAACf,IAAI;MAACwC,MAAM,EAAC,UAAU;MAAAD,QAAA,gBACrBxB,OAAA,CAACzB,GAAG;QAACmD,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;UAACmD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZxB,OAAA,CAACf,IAAI,CAAC2C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBxB,OAAA,CAACb,WAAW;cACV2C,GAAG,EAAE,CAAE;cACPC,KAAK,EAAEnB,QAAS;cAChBoB,QAAQ,EAAGD,KAAK,IAAKlB,WAAW,CAACkB,KAAK,IAAI,CAAC,CAAE;cAC7CE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNtC,OAAA,CAACxB,GAAG;UAACmD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZxB,OAAA,CAACf,IAAI,CAAC2C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBxB,OAAA,CAACd,MAAM;cAAC6C,KAAK,EAAEjB,YAAa;cAACkB,QAAQ,EAAEjB,eAAgB;cAAAS,QAAA,gBACrDxB,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA,CAACzB,GAAG;QAACmD,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;UAACmD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZxB,OAAA,CAACf,IAAI,CAAC2C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBxB,OAAA,CAACd,MAAM;cAAC6C,KAAK,EAAEf,SAAU;cAACgB,QAAQ,EAAEf,YAAa;cAAAO,QAAA,gBAC/CxB,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAE,KAAM;gBAAAP,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAE,IAAK;gBAAAP,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNtC,OAAA,CAACxB,GAAG;UAACmD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZxB,OAAA,CAACf,IAAI,CAAC2C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBxB,OAAA,CAACd,MAAM;cAAC6C,KAAK,EAAEb,QAAS;cAACc,QAAQ,EAAEb,WAAY;cAACoB,UAAU;cAAAf,QAAA,gBACxDxB,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,eAAe;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,SAAS;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCtC,OAAA,CAACO,MAAM;gBAACwB,KAAK,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPtC,OAAA,CAACnB,OAAO;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXtC,OAAA,CAACzB,GAAG;MAACmD,MAAM,EAAE,EAAG;MAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;QAACmD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXxB,OAAA,CAACX,SAAS;UACRiC,KAAK,EAAC,cAAI;UACVS,KAAK,EAAEX,WAAW,CAACoB,aAAc;UACjCC,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtC,OAAA,CAACxB,GAAG;QAACmD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXxB,OAAA,CAACX,SAAS;UACRiC,KAAK,EAAC,0BAAM;UACZS,KAAK,EAAEX,WAAW,CAACuB,aAAc;UACjCF,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC,MAAG;UACVE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtC,OAAA,CAACxB,GAAG;QAACmD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXxB,OAAA,CAACX,SAAS;UACRiC,KAAK,EAAC,0BAAM;UACZS,KAAK,EAAEX,WAAW,CAAC0B,UAAW;UAC9BL,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC,MAAG;UACVE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlB,WAAW,CAAC2B,YAAY,CAACC,MAAM,GAAG,CAAC,iBAClChD,OAAA,CAAAE,SAAA;MAAAsB,QAAA,gBACExB,OAAA,CAACnB,OAAO;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXtC,OAAA,CAACI,IAAI;QAAC6C,MAAM;QAAAzB,QAAA,EAAC;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7BtC,OAAA,CAACjB,IAAI;QACHwC,IAAI,EAAC,OAAO;QACZ2B,UAAU,EAAE9B,WAAW,CAAC2B,YAAa;QACrCI,UAAU,EAAGC,IAAI,iBACfpD,OAAA,CAACjB,IAAI,CAAC6C,IAAI;UAAAJ,QAAA,eACRxB,OAAA,CAACpB,KAAK;YAAA4C,QAAA,gBACJxB,OAAA,CAACN,mBAAmB;cAACuC,KAAK,EAAE;gBAAEY,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDtC,OAAA,CAACI,IAAI;cAAAoB,QAAA,EAAE4B,IAAI,CAACC;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBtC,OAAA,CAACtB,GAAG;cAACmE,KAAK,EAAC,OAAO;cAAArB,QAAA,EACf4B,IAAI,CAACE,QAAQ,CAACC,IAAI,KAAK,YAAY,GAAG,GAAGH,IAAI,CAACE,QAAQ,CAACvB,KAAK,KAAK,GAAG,IAAIqB,IAAI,CAACE,QAAQ,CAACvB,KAAK;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACF,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC3B,EAAA,CArHIH,eAA+C;AAAAgD,EAAA,GAA/ChD,eAA+C;AAuHrD,MAAMiD,uBAAiC,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAS,GAAG,CAAC;EACnE,MAAM,CAAC0F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM4F,gBAAgB,GAAGtE,YAAY,CAACuE,MAAM,CAACC,CAAC;IAAA,IAAAC,MAAA,EAAAC,OAAA,EAAAC,OAAA;IAAA,OAC5C,EAAAF,MAAA,GAAAD,CAAC,CAACI,GAAG,cAAAH,MAAA,uBAALA,MAAA,CAAOI,QAAQ,CAAC,KAAK,CAAC,OAAAH,OAAA,GAAIF,CAAC,CAACI,GAAG,cAAAF,OAAA,uBAALA,OAAA,CAAOG,QAAQ,CAAC,KAAK,CAAC,OAAAF,OAAA,GAAIH,CAAC,CAACI,GAAG,cAAAD,OAAA,uBAALA,OAAA,CAAOE,QAAQ,CAAC,IAAI,CAAC;EAAA,CAC3E,CAAC;EAED,MAAMC,cAAc,GAAGR,gBAAgB,CAACS,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACQ,EAAE,KAAKd,eAAe,CAAC;EAC3E,MAAMe,qBAAqB,GAAG/E,yBAAyB,CAACqE,MAAM,CAC5DW,MAAM,IAAIA,MAAM,CAACC,SAAS,KAAKjB,eACjC,CAAC;EAED,MAAMkB,sBAAsB,GAAGA,CAAA,KAAM;IACnCf,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMgB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,eAAe,GAAGnF,sBAAsB,CAC5C,MAAM,EACN,YAAY,EACZ,KAAK,EACL,CAAC,MAAM,EAAE,KAAK,CAChB,CAAC;IAEDd,KAAK,CAACkG,IAAI,CAAC;MACT5D,KAAK,EAAE,MAAM;MACbY,KAAK,EAAE,GAAG;MACViD,OAAO,eACLnF,OAAA;QAAAwB,QAAA,gBACExB,OAAA,CAACK,SAAS;UAAAmB,QAAA,EAAC;QAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,EACxC2C,eAAe,CAACG,GAAG,CAACP,MAAM,iBACzB7E,OAAA,CAAC1B,IAAI;UAAiBiD,IAAI,EAAC,OAAO;UAACU,KAAK,EAAE;YAAEoD,YAAY,EAAE;UAAE,CAAE;UAAA7D,QAAA,gBAC5DxB,OAAA,CAACG,KAAK;YAACmF,KAAK,EAAE,CAAE;YAAA9D,QAAA,EAAEqD,MAAM,CAACU;UAAiB;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnDtC,OAAA,CAACI,IAAI;YAAAoB,QAAA,EAAEqD,MAAM,CAACW;UAAW;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjCtC,OAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtC,OAAA,CAACI,IAAI;YAAC6C,MAAM;YAAAzB,QAAA,GAAC,oBAAK,EAACqD,MAAM,CAACnE,SAAS;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAJlCuC,MAAM,CAACF,EAAE;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKd,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAET,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtC,OAAA;IAAKiC,KAAK,EAAE;MAAEwD,OAAO,EAAE;IAAO,CAAE;IAAAjE,QAAA,gBAC9BxB,OAAA,CAACzB,GAAG;MAACmD,MAAM,EAAE,EAAG;MAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;QAACmD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXxB,OAAA,CAAC1B,IAAI;UAACgD,KAAK,EAAC,sCAAQ;UAACC,IAAI,EAAC,OAAO;UAAAC,QAAA,eAC/BxB,OAAA,CAACjB,IAAI;YACHmE,UAAU,EAAEe,gBAAiB;YAC7Bd,UAAU,EAAGuC,OAAO;cAAA,IAAAC,aAAA;cAAA,oBAClB3F,OAAA,CAACjB,IAAI,CAAC6C,IAAI;gBACRK,KAAK,EAAE;kBACL2D,MAAM,EAAE,SAAS;kBACjBC,eAAe,EAAEhC,eAAe,KAAK6B,OAAO,CAACf,EAAE,GAAG,SAAS,GAAG,aAAa;kBAC3Ec,OAAO,EAAE,KAAK;kBACdK,YAAY,EAAE;gBAChB,CAAE;gBACFC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC4B,OAAO,CAACf,EAAE,CAAE;gBAAAnD,QAAA,eAE9CxB,OAAA,CAACjB,IAAI,CAAC6C,IAAI,CAACoE,IAAI;kBACb1E,KAAK,EAAEoE,OAAO,CAACrC,IAAK;kBACpBmC,WAAW,eACTxF,OAAA,CAACpB,KAAK;oBAAA4C,QAAA,gBACJxB,OAAA,CAACI,IAAI;sBAACmD,IAAI,EAAC,WAAW;sBAAA/B,QAAA,GAAC,MAAC,EAACkE,OAAO,CAAChF,SAAS;oBAAA;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,GAAAqD,aAAA,GACjDD,OAAO,CAACO,IAAI,cAAAN,aAAA,uBAAZA,aAAA,CAAcP,GAAG,CAACc,GAAG,iBACpBlG,OAAA,CAACtB,GAAG;sBAAW6C,IAAI,EAAC,OAAO;sBAAAC,QAAA,EAAE0E;oBAAG,GAAtBA,GAAG;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAyB,CACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;UACZ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtC,OAAA,CAACxB,GAAG;QAACmD,IAAI,EAAE,EAAG;QAAAH,QAAA,EACXiD,cAAc,iBACbzE,OAAA,CAAC1B,IAAI;UACHgD,KAAK,eACHtB,OAAA,CAACpB,KAAK;YAAA4C,QAAA,gBACJxB,OAAA,CAACT,aAAa;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChBmC,cAAc,CAACpB,IAAI,eACpBrD,OAAA,CAACtB,GAAG;cAACmE,KAAK,EAAC,MAAM;cAAArB,QAAA,EAAEiD,cAAc,CAACF;YAAG;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACR;UACD6D,KAAK,eACHnG,OAAA,CAACpB,KAAK;YAAA4C,QAAA,gBACJxB,OAAA,CAACvB,MAAM;cAAC8E,IAAI,EAAC,SAAS;cAAC6C,IAAI,eAAEpG,OAAA,CAACP,eAAe;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACyD,OAAO,EAAEhB,sBAAuB;cAAAvD,QAAA,EAAC;YAEnF;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA,CAACvB,MAAM;cAAC2H,IAAI,eAAEpG,OAAA,CAACV,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACyD,OAAO,EAAEf,oBAAqB;cAAAxD,QAAA,EAAC;YAE/D;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAd,QAAA,eAEDxB,OAAA,CAACzB,GAAG;YAACmD,MAAM,EAAE,EAAG;YAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;cAACmD,IAAI,EAAE,EAAG;cAAAH,QAAA,eACZxB,OAAA,CAAClB,IAAI;gBAACuH,gBAAgB,EAAC,UAAU;gBAAA7E,QAAA,gBAC/BxB,OAAA,CAACM,OAAO;kBAACgG,GAAG,EAAC,0BAAM;kBAAA9E,QAAA,gBACjBxB,OAAA,CAACK,SAAS;oBAAAmB,QAAA,EAAEiD,cAAc,CAACe;kBAAW;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAEnDtC,OAAA,CAACG,KAAK;oBAACmF,KAAK,EAAE,CAAE;oBAAA9D,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7BtC,OAAA,CAACzB,GAAG;oBAACmD,MAAM,EAAE,EAAG;oBAAAF,QAAA,GAAAmC,qBAAA,GACbc,cAAc,CAAC8B,cAAc,cAAA5C,qBAAA,uBAA7BA,qBAAA,CAA+ByB,GAAG,CAAC,CAACoB,IAAI,EAAEC,KAAK,kBAC9CzG,OAAA,CAACxB,GAAG;sBAACmD,IAAI,EAAE,EAAG;sBAAaM,KAAK,EAAE;wBAAEoD,YAAY,EAAE;sBAAG,CAAE;sBAAA7D,QAAA,eACrDxB,OAAA,CAAC1B,IAAI;wBAACiD,IAAI,EAAC,OAAO;wBAAAC,QAAA,eAChBxB,OAAA,CAACpB,KAAK;0BAAA4C,QAAA,gBACJxB,OAAA,CAACR,YAAY;4BAAA2C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChBtC,OAAA;4BAAAwB,QAAA,gBACExB,OAAA,CAACI,IAAI;8BAAC6C,MAAM;8BAAAzB,QAAA,EAAEgF,IAAI,CAACnD;4BAAI;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC/BtC,OAAA;8BAAAmC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACNtC,OAAA,CAACI,IAAI;8BAACmD,IAAI,EAAC,WAAW;8BAAA/B,QAAA,EAAEgF,IAAI,CAACzE;4BAAK;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC,GAVWmE,KAAK;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAWpB,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENtC,OAAA,CAACG,KAAK;oBAACmF,KAAK,EAAE,CAAE;oBAAA9D,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7BtC,OAAA,CAACpB,KAAK;oBAAC8H,IAAI;oBAAAlF,QAAA,GAAAoC,oBAAA,GACRa,cAAc,CAACwB,IAAI,cAAArC,oBAAA,uBAAnBA,oBAAA,CAAqBwB,GAAG,CAACc,GAAG,iBAC3BlG,OAAA,CAACtB,GAAG;sBAAWmE,KAAK,EAAC,MAAM;sBAAArB,QAAA,EAAE0E;oBAAG,GAAtBA,GAAG;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAyB,CACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GA1Bc,UAAU;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2BzB,CAAC,eAEVtC,OAAA,CAACM,OAAO;kBAACgG,GAAG,EAAC,0BAAM;kBAAA9E,QAAA,eACjBxB,OAAA,CAACzB,GAAG;oBAACmD,MAAM,EAAE,EAAG;oBAAAF,QAAA,EACboD,qBAAqB,CAACQ,GAAG,CAACP,MAAM,iBAC/B7E,OAAA,CAACxB,GAAG;sBAACmD,IAAI,EAAE,EAAG;sBAAiBM,KAAK,EAAE;wBAAEoD,YAAY,EAAE;sBAAG,CAAE;sBAAA7D,QAAA,eACzDxB,OAAA,CAAC1B,IAAI;wBACHiD,IAAI,EAAC,OAAO;wBACZD,KAAK,EAAEuD,MAAM,CAACU,iBAAkB;wBAChCY,KAAK,eAAEnG,OAAA,CAACI,IAAI;0BAAC6C,MAAM;0BAAAzB,QAAA,GAAC,MAAC,EAACqD,MAAM,CAACnE,SAAS;wBAAA;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAE;wBAAAd,QAAA,gBAE/CxB,OAAA,CAACK,SAAS;0BAACsG,QAAQ,EAAE;4BAAEC,IAAI,EAAE;0BAAE,CAAE;0BAAApF,QAAA,EAC9BqD,MAAM,CAACW;wBAAW;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eAEZtC,OAAA,CAACpB,KAAK;0BAAC8H,IAAI;0BAAAlF,QAAA,EACRqD,MAAM,CAACgC,cAAc,CAACzB,GAAG,CAAC0B,GAAG,iBAC5B9G,OAAA,CAACtB,GAAG;4BAAW6C,IAAI,EAAC,OAAO;4BAAAC,QAAA,EAAEsF;0BAAG,GAAtBA,GAAG;4BAAA3E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAyB,CACvC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC,eAERtC,OAAA,CAACnB,OAAO;0BAAAsD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAEXtC,OAAA,CAACjB,IAAI;0BACHwC,IAAI,EAAC,OAAO;0BACZ2B,UAAU,EAAE2B,MAAM,CAACkC,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;0BACxC7D,UAAU,EAAG8D,OAAO,iBAClBjH,OAAA,CAACjB,IAAI,CAAC6C,IAAI;4BAACK,KAAK,EAAE;8BAAEwD,OAAO,EAAE;4BAAQ,CAAE;4BAAAjE,QAAA,eACrCxB,OAAA,CAACpB,KAAK;8BAAA4C,QAAA,gBACJxB,OAAA,CAACN,mBAAmB;gCAACuC,KAAK,EAAE;kCAAEY,KAAK,EAAE;gCAAU;8BAAE;gCAAAV,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eACpDtC,OAAA,CAACI,IAAI;gCAAC6B,KAAK,EAAE;kCAAEiF,QAAQ,EAAE;gCAAO,CAAE;gCAAA1F,QAAA,EAAEyF;8BAAO;gCAAA9E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBACX;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAEDuC,MAAM,CAACkC,QAAQ,CAAC/D,MAAM,GAAG,CAAC,iBACzBhD,OAAA,CAACI,IAAI;0BAACmD,IAAI,EAAC,WAAW;0BAACtB,KAAK,EAAE;4BAAEiF,QAAQ,EAAE;0BAAO,CAAE;0BAAA1F,QAAA,GAAC,eAC/C,EAACqD,MAAM,CAACkC,QAAQ,CAAC/D,MAAM,GAAG,CAAC,EAAC,wBACjC;wBAAA;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG;oBAAC,GApCWuC,MAAM,CAACF,EAAE;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAqCxB,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC,GA1CgB,gBAAgB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2C/B,CAAC,eAEVtC,OAAA,CAACM,OAAO;kBAACgG,GAAG,EAAC,0BAAM;kBAAA9E,QAAA,gBACjBxB,OAAA,CAACZ,KAAK;oBACJ+H,OAAO,EAAC,sCAAQ;oBAChB3B,WAAW,EAAC,wJAA2B;oBACvCjC,IAAI,EAAC,MAAM;oBACX6D,QAAQ;oBACRnF,KAAK,EAAE;sBAAEoD,YAAY,EAAE;oBAAG;kBAAE;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eAEFtC,OAAA,CAACzB,GAAG;oBAACmD,MAAM,EAAE,EAAG;oBAAAF,QAAA,gBACdxB,OAAA,CAACxB,GAAG;sBAACmD,IAAI,EAAE,CAAE;sBAAAH,QAAA,eACXxB,OAAA,CAAC1B,IAAI;wBAACiD,IAAI,EAAC,OAAO;wBAACD,KAAK,EAAC,0BAAM;wBAAAE,QAAA,eAC7BxB,OAAA,CAACpB,KAAK;0BAACyI,SAAS,EAAC,UAAU;0BAAA7F,QAAA,gBACzBxB,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAgB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7BtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrBtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAM;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNtC,OAAA,CAACxB,GAAG;sBAACmD,IAAI,EAAE,CAAE;sBAAAH,QAAA,eACXxB,OAAA,CAAC1B,IAAI;wBAACiD,IAAI,EAAC,OAAO;wBAACD,KAAK,EAAC,0BAAM;wBAAAE,QAAA,eAC7BxB,OAAA,CAACpB,KAAK;0BAACyI,SAAS,EAAC,UAAU;0BAAA7F,QAAA,gBACzBxB,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAS;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACtBtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrBtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNtC,OAAA,CAACxB,GAAG;sBAACmD,IAAI,EAAE,CAAE;sBAAAH,QAAA,eACXxB,OAAA,CAAC1B,IAAI;wBAACiD,IAAI,EAAC,OAAO;wBAACD,KAAK,EAAC,0BAAM;wBAAAE,QAAA,eAC7BxB,OAAA,CAACpB,KAAK;0BAACyI,SAAS,EAAC,UAAU;0BAAA7F,QAAA,gBACzBxB,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrBtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrBtC,OAAA,CAACI,IAAI;4BAAAoB,QAAA,EAAC;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GArCgB,SAAS;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENtC,OAAA,CAACxB,GAAG;cAACmD,IAAI,EAAE,CAAE;cAAAH,QAAA,eACXxB,OAAA,CAACQ,eAAe;gBACdC,UAAU,EAAEgE,cAAc,CAACF,GAAG,IAAI,EAAG;gBACrC7D,SAAS,EAAE+D,cAAc,CAAC/D;cAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtC,OAAA,CAAChB,KAAK;MACJsC,KAAK,EAAC,sCAAQ;MACdgG,OAAO,EAAEvD,kBAAmB;MAC5BwD,QAAQ,EAAEA,CAAA,KAAMvD,qBAAqB,CAAC,KAAK,CAAE;MAC7C9B,KAAK,EAAE,GAAI;MACXsF,MAAM,EAAE,IAAK;MAAAhG,QAAA,gBAEbxB,OAAA,CAACZ,KAAK;QACJ+H,OAAO,EAAC,0BAAM;QACd3B,WAAW,EAAC,8GAAoB;QAChCjC,IAAI,EAAC,MAAM;QACX6D,QAAQ;QACRnF,KAAK,EAAE;UAAEoD,YAAY,EAAE;QAAG;MAAE;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFtC,OAAA,CAACI,IAAI;QAAAoB,QAAA,EAAC;MAAc;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACoB,GAAA,CAzPID,uBAAiC;AAAAgE,GAAA,GAAjChE,uBAAiC;AA2PvC,eAAeA,uBAAuB;AAAC,IAAAD,EAAA,EAAAiE,GAAA;AAAAC,YAAA,CAAAlE,EAAA;AAAAkE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}