{"ast": null, "code": "// 金蝶软件报价专用数据结构\n\n// 金蝶云星辰产品定义\nexport const kisCloudProduct = {\n  id: 'kis-cloud',\n  name: '金蝶云星辰',\n  code: 'KIS-CLOUD',\n  description: '专为小微企业设计的云端财务管理软件',\n  category: 'financial',\n  targetCustomer: ['小微企业', '个体工商户', '初创企业'],\n  basePrice: 1980,\n  priceUnit: 'user/year',\n  versions: [{\n    id: 'starter',\n    name: '入门版',\n    description: '基础财务功能，适合初创企业',\n    features: ['基础总账', '简单报表', '凭证管理'],\n    maxUsers: 1,\n    priceMultiplier: 0.8,\n    recommended: false\n  }, {\n    id: 'standard',\n    name: '标准版',\n    description: '完整财务功能，适合小微企业',\n    features: ['完整总账', '应收应付', '固定资产', '财务报表', '现金流量表'],\n    maxUsers: 5,\n    priceMultiplier: 1.0,\n    recommended: true\n  }, {\n    id: 'professional',\n    name: '专业版',\n    description: '高级财务功能，支持多账套',\n    features: ['多账套管理', '成本核算', '预算管理', '自定义报表', '数据分析'],\n    maxUsers: 20,\n    priceMultiplier: 1.5,\n    recommended: false\n  }],\n  modules: [{\n    id: 'general_ledger',\n    name: '总账管理',\n    description: '完整的总账核算功能',\n    category: '核心模块',\n    price: 0,\n    priceType: 'fixed',\n    required: true\n  }, {\n    id: 'ar_ap',\n    name: '应收应付',\n    description: '应收账款和应付账款管理',\n    category: '核心模块',\n    price: 500,\n    priceType: 'per_user',\n    required: false\n  }, {\n    id: 'fixed_assets',\n    name: '固定资产',\n    description: '固定资产管理和折旧计算',\n    category: '扩展模块',\n    price: 800,\n    priceType: 'fixed',\n    required: false\n  }, {\n    id: 'cash_management',\n    name: '出纳管理',\n    description: '现金和银行存款管理',\n    category: '扩展模块',\n    price: 300,\n    priceType: 'per_user',\n    required: false\n  }, {\n    id: 'cost_accounting',\n    name: '成本核算',\n    description: '产品成本计算和分析',\n    category: '高级模块',\n    price: 1200,\n    priceType: 'fixed',\n    required: false,\n    dependencies: ['general_ledger']\n  }],\n  services: [{\n    id: 'basic_implementation',\n    name: '基础实施服务',\n    description: '软件安装配置和基础培训',\n    type: 'implementation',\n    price: 2000,\n    duration: '5个工作日',\n    deliverables: ['系统安装', '基础配置', '用户培训', '上线支持']\n  }, {\n    id: 'data_migration',\n    name: '数据迁移服务',\n    description: '从旧系统迁移数据',\n    type: 'implementation',\n    price: 3000,\n    duration: '3个工作日',\n    deliverables: ['数据分析', '数据清洗', '数据导入', '数据验证']\n  }, {\n    id: 'advanced_training',\n    name: '高级培训服务',\n    description: '深度业务培训和最佳实践',\n    type: 'training',\n    price: 1500,\n    duration: '2天',\n    deliverables: ['现场培训', '培训材料', '操作手册', '后续答疑']\n  }],\n  deploymentOptions: [{\n    id: 'cloud',\n    name: '云端部署',\n    description: '金蝶云平台托管，免维护',\n    type: 'cloud',\n    priceImpact: 0,\n    features: ['自动备份', '弹性扩容', '7x24监控', '自动更新']\n  }, {\n    id: 'private_cloud',\n    name: '私有云部署',\n    description: '独立云环境，更高安全性',\n    type: 'cloud',\n    priceImpact: 2000,\n    features: ['独立环境', '数据隔离', '定制配置', '专属支持']\n  }],\n  supportLevels: [{\n    id: 'standard',\n    name: '标准支持',\n    description: '工作日在线支持',\n    responseTime: '4小时内响应',\n    channels: ['在线客服', '电话支持', '邮件支持'],\n    price: 0,\n    priceType: 'fixed'\n  }, {\n    id: 'premium',\n    name: '高级支持',\n    description: '7x12小时专业支持',\n    responseTime: '2小时内响应',\n    channels: ['专属客服', '电话支持', '远程协助', '现场支持'],\n    price: 1000,\n    priceType: 'fixed'\n  }, {\n    id: 'enterprise',\n    name: '企业支持',\n    description: '7x24小时专属支持',\n    responseTime: '1小时内响应',\n    channels: ['专属经理', '技术专家', '现场支持', '定制开发'],\n    price: 3000,\n    priceType: 'fixed'\n  }]\n};\n\n// 金蝶精斗云产品定义\nexport const jdyProduct = {\n  id: 'jdy',\n  name: '金蝶精斗云',\n  code: 'JDY',\n  description: '一体化云端企业管理软件，集成多业务模块',\n  category: 'erp',\n  targetCustomer: ['中小企业', '成长型企业', '连锁企业'],\n  basePrice: 2980,\n  priceUnit: 'user/year',\n  versions: [{\n    id: 'basic',\n    name: '基础版',\n    description: '进销存+基础财务',\n    features: ['进销存管理', '基础财务', '库存管理', '销售管理'],\n    maxUsers: 10,\n    priceMultiplier: 0.8,\n    recommended: false\n  }, {\n    id: 'professional',\n    name: '专业版',\n    description: '完整业务管理功能',\n    features: ['进销存', '财务管理', 'CRM', '项目管理', '报表分析'],\n    maxUsers: 50,\n    priceMultiplier: 1.0,\n    recommended: true\n  }, {\n    id: 'enterprise',\n    name: '企业版',\n    description: '全功能企业管理平台',\n    features: ['全业务模块', '工作流', 'BI分析', 'API接口', '移动应用'],\n    maxUsers: 200,\n    priceMultiplier: 1.8,\n    recommended: false\n  }],\n  modules: [{\n    id: 'inventory',\n    name: '进销存管理',\n    description: '采购、销售、库存一体化管理',\n    category: '核心模块',\n    price: 0,\n    priceType: 'fixed',\n    required: true\n  }, {\n    id: 'finance',\n    name: '财务管理',\n    description: '完整的财务核算功能',\n    category: '核心模块',\n    price: 800,\n    priceType: 'per_user',\n    required: false\n  }, {\n    id: 'crm',\n    name: 'CRM客户管理',\n    description: '客户关系管理和销售跟进',\n    category: '业务模块',\n    price: 600,\n    priceType: 'per_user',\n    required: false\n  }, {\n    id: 'project',\n    name: '项目管理',\n    description: '项目进度和成本管理',\n    category: '业务模块',\n    price: 500,\n    priceType: 'per_user',\n    required: false\n  }, {\n    id: 'hr',\n    name: '人事管理',\n    description: '员工信息和考勤管理',\n    category: '扩展模块',\n    price: 400,\n    priceType: 'per_user',\n    required: false\n  }],\n  services: [{\n    id: 'standard_implementation',\n    name: '标准实施服务',\n    description: '完整的系统实施和培训',\n    type: 'implementation',\n    price: 5000,\n    duration: '10个工作日',\n    deliverables: ['需求调研', '系统配置', '数据导入', '用户培训', '上线支持']\n  }, {\n    id: 'business_consulting',\n    name: '业务咨询服务',\n    description: '业务流程优化和最佳实践',\n    type: 'customization',\n    price: 8000,\n    duration: '15个工作日',\n    deliverables: ['流程梳理', '方案设计', '系统配置', '培训指导']\n  }],\n  deploymentOptions: [{\n    id: 'saas',\n    name: 'SaaS云服务',\n    description: '标准云服务，快速上线',\n    type: 'cloud',\n    priceImpact: 0,\n    features: ['即开即用', '弹性付费', '自动更新', '数据安全']\n  }, {\n    id: 'dedicated_cloud',\n    name: '专属云服务',\n    description: '独立云环境，定制配置',\n    type: 'cloud',\n    priceImpact: 5000,\n    features: ['独立环境', '定制功能', '专属支持', '数据隔离']\n  }],\n  supportLevels: [{\n    id: 'standard',\n    name: '标准支持',\n    description: '工作日支持服务',\n    responseTime: '4小时内响应',\n    channels: ['在线客服', '电话支持'],\n    price: 0,\n    priceType: 'fixed'\n  }, {\n    id: 'premium',\n    name: '高级支持',\n    description: '扩展时间支持服务',\n    responseTime: '2小时内响应',\n    channels: ['专属客服', '电话支持', '远程协助'],\n    price: 2000,\n    priceType: 'fixed'\n  }]\n};\n\n// 所有软件产品\nexport const allSoftwareProducts = [kisCloudProduct, jdyProduct];\n\n// 软件报价计算函数\nexport function calculateSoftwareQuote(productId, versionId, userCount, selectedModules, selectedServices, deploymentId, supportId, servicePeriod = 1) {\n  const product = allSoftwareProducts.find(p => p.id === productId);\n  if (!product) {\n    throw new Error('产品不存在');\n  }\n  const version = product.versions.find(v => v.id === versionId);\n  if (!version) {\n    throw new Error('版本不存在');\n  }\n  const breakdown = [];\n\n  // 基础软件费用\n  const baseAmount = product.basePrice * version.priceMultiplier * userCount * servicePeriod;\n  breakdown.push({\n    item: `${product.name} ${version.name}`,\n    quantity: userCount,\n    unit: '用户',\n    unitPrice: product.basePrice * version.priceMultiplier,\n    period: servicePeriod,\n    amount: baseAmount\n  });\n\n  // 模块费用\n  let moduleAmount = 0;\n  selectedModules.forEach(moduleId => {\n    const module = product.modules.find(m => m.id === moduleId);\n    if (module && !module.required) {\n      let amount = 0;\n      if (module.priceType === 'per_user') {\n        amount = module.price * userCount * servicePeriod;\n      } else {\n        amount = module.price * servicePeriod;\n      }\n      moduleAmount += amount;\n      breakdown.push({\n        item: module.name,\n        quantity: module.priceType === 'per_user' ? userCount : 1,\n        unit: module.priceType === 'per_user' ? '用户' : '套',\n        unitPrice: module.price,\n        period: servicePeriod,\n        amount: amount\n      });\n    }\n  });\n\n  // 服务费用\n  let serviceAmount = 0;\n  selectedServices.forEach(serviceId => {\n    const service = product.services.find(s => s.id === serviceId);\n    if (service) {\n      serviceAmount += service.price;\n      breakdown.push({\n        item: service.name,\n        quantity: 1,\n        unit: '项',\n        unitPrice: service.price,\n        period: 1,\n        amount: service.price\n      });\n    }\n  });\n\n  // 部署费用\n  const deployment = product.deploymentOptions.find(d => d.id === deploymentId);\n  const deploymentAmount = deployment ? deployment.priceImpact * servicePeriod : 0;\n  if (deploymentAmount > 0) {\n    breakdown.push({\n      item: deployment.name,\n      quantity: 1,\n      unit: '套',\n      unitPrice: deployment.priceImpact,\n      period: servicePeriod,\n      amount: deploymentAmount\n    });\n  }\n\n  // 支持费用\n  const support = product.supportLevels.find(s => s.id === supportId);\n  const supportAmount = support ? support.price * servicePeriod : 0;\n  if (supportAmount > 0) {\n    breakdown.push({\n      item: support.name,\n      quantity: 1,\n      unit: '年',\n      unitPrice: support.price,\n      period: servicePeriod,\n      amount: supportAmount\n    });\n  }\n  const totalAmount = baseAmount + moduleAmount + serviceAmount + deploymentAmount + supportAmount;\n  return {\n    baseAmount,\n    moduleAmount,\n    serviceAmount,\n    deploymentAmount,\n    supportAmount,\n    totalAmount,\n    breakdown\n  };\n}", "map": {"version": 3, "names": ["kisCloudProduct", "id", "name", "code", "description", "category", "targetCustomer", "basePrice", "priceUnit", "versions", "features", "maxUsers", "priceMultiplier", "recommended", "modules", "price", "priceType", "required", "dependencies", "services", "type", "duration", "deliverables", "deploymentOptions", "priceImpact", "supportLevels", "responseTime", "channels", "jdyProduct", "allSoftwareProducts", "calculateSoftwareQuote", "productId", "versionId", "userCount", "selectedModules", "selectedServices", "deploymentId", "supportId", "servicePeriod", "product", "find", "p", "Error", "version", "v", "breakdown", "baseAmount", "push", "item", "quantity", "unit", "unitPrice", "period", "amount", "moduleAmount", "for<PERSON>ach", "moduleId", "module", "m", "serviceAmount", "serviceId", "service", "s", "deployment", "d", "deploymentAmount", "support", "supportAmount", "totalAmount"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/softwareQuoteData.ts"], "sourcesContent": ["// 金蝶软件报价专用数据结构\nexport interface SoftwareProduct {\n  id: string;\n  name: string;\n  code: string;\n  description: string;\n  category: 'financial' | 'erp' | 'crm' | 'hr' | 'oa';\n  targetCustomer: string[];\n  basePrice: number;\n  priceUnit: 'user/year' | 'license/year' | 'enterprise/year';\n  versions: SoftwareVersion[];\n  modules: SoftwareModule[];\n  services: SoftwareService[];\n  deploymentOptions: DeploymentOption[];\n  supportLevels: SupportLevel[];\n}\n\nexport interface SoftwareVersion {\n  id: string;\n  name: string;\n  description: string;\n  features: string[];\n  maxUsers: number;\n  priceMultiplier: number; // 基于基础价格的倍数\n  recommended: boolean;\n}\n\nexport interface SoftwareModule {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  price: number;\n  priceType: 'fixed' | 'per_user' | 'percentage';\n  required: boolean;\n  dependencies?: string[]; // 依赖的其他模块\n}\n\nexport interface SoftwareService {\n  id: string;\n  name: string;\n  description: string;\n  type: 'implementation' | 'training' | 'customization' | 'maintenance';\n  price: number;\n  duration: string;\n  deliverables: string[];\n}\n\nexport interface DeploymentOption {\n  id: string;\n  name: string;\n  description: string;\n  type: 'cloud' | 'on_premise' | 'hybrid';\n  priceImpact: number; // 价格影响（正数为加价，负数为减价）\n  features: string[];\n}\n\nexport interface SupportLevel {\n  id: string;\n  name: string;\n  description: string;\n  responseTime: string;\n  channels: string[];\n  price: number;\n  priceType: 'fixed' | 'percentage';\n}\n\n// 金蝶云星辰产品定义\nexport const kisCloudProduct: SoftwareProduct = {\n  id: 'kis-cloud',\n  name: '金蝶云星辰',\n  code: 'KIS-CLOUD',\n  description: '专为小微企业设计的云端财务管理软件',\n  category: 'financial',\n  targetCustomer: ['小微企业', '个体工商户', '初创企业'],\n  basePrice: 1980,\n  priceUnit: 'user/year',\n  versions: [\n    {\n      id: 'starter',\n      name: '入门版',\n      description: '基础财务功能，适合初创企业',\n      features: ['基础总账', '简单报表', '凭证管理'],\n      maxUsers: 1,\n      priceMultiplier: 0.8,\n      recommended: false,\n    },\n    {\n      id: 'standard',\n      name: '标准版',\n      description: '完整财务功能，适合小微企业',\n      features: ['完整总账', '应收应付', '固定资产', '财务报表', '现金流量表'],\n      maxUsers: 5,\n      priceMultiplier: 1.0,\n      recommended: true,\n    },\n    {\n      id: 'professional',\n      name: '专业版',\n      description: '高级财务功能，支持多账套',\n      features: ['多账套管理', '成本核算', '预算管理', '自定义报表', '数据分析'],\n      maxUsers: 20,\n      priceMultiplier: 1.5,\n      recommended: false,\n    },\n  ],\n  modules: [\n    {\n      id: 'general_ledger',\n      name: '总账管理',\n      description: '完整的总账核算功能',\n      category: '核心模块',\n      price: 0,\n      priceType: 'fixed',\n      required: true,\n    },\n    {\n      id: 'ar_ap',\n      name: '应收应付',\n      description: '应收账款和应付账款管理',\n      category: '核心模块',\n      price: 500,\n      priceType: 'per_user',\n      required: false,\n    },\n    {\n      id: 'fixed_assets',\n      name: '固定资产',\n      description: '固定资产管理和折旧计算',\n      category: '扩展模块',\n      price: 800,\n      priceType: 'fixed',\n      required: false,\n    },\n    {\n      id: 'cash_management',\n      name: '出纳管理',\n      description: '现金和银行存款管理',\n      category: '扩展模块',\n      price: 300,\n      priceType: 'per_user',\n      required: false,\n    },\n    {\n      id: 'cost_accounting',\n      name: '成本核算',\n      description: '产品成本计算和分析',\n      category: '高级模块',\n      price: 1200,\n      priceType: 'fixed',\n      required: false,\n      dependencies: ['general_ledger'],\n    },\n  ],\n  services: [\n    {\n      id: 'basic_implementation',\n      name: '基础实施服务',\n      description: '软件安装配置和基础培训',\n      type: 'implementation',\n      price: 2000,\n      duration: '5个工作日',\n      deliverables: ['系统安装', '基础配置', '用户培训', '上线支持'],\n    },\n    {\n      id: 'data_migration',\n      name: '数据迁移服务',\n      description: '从旧系统迁移数据',\n      type: 'implementation',\n      price: 3000,\n      duration: '3个工作日',\n      deliverables: ['数据分析', '数据清洗', '数据导入', '数据验证'],\n    },\n    {\n      id: 'advanced_training',\n      name: '高级培训服务',\n      description: '深度业务培训和最佳实践',\n      type: 'training',\n      price: 1500,\n      duration: '2天',\n      deliverables: ['现场培训', '培训材料', '操作手册', '后续答疑'],\n    },\n  ],\n  deploymentOptions: [\n    {\n      id: 'cloud',\n      name: '云端部署',\n      description: '金蝶云平台托管，免维护',\n      type: 'cloud',\n      priceImpact: 0,\n      features: ['自动备份', '弹性扩容', '7x24监控', '自动更新'],\n    },\n    {\n      id: 'private_cloud',\n      name: '私有云部署',\n      description: '独立云环境，更高安全性',\n      type: 'cloud',\n      priceImpact: 2000,\n      features: ['独立环境', '数据隔离', '定制配置', '专属支持'],\n    },\n  ],\n  supportLevels: [\n    {\n      id: 'standard',\n      name: '标准支持',\n      description: '工作日在线支持',\n      responseTime: '4小时内响应',\n      channels: ['在线客服', '电话支持', '邮件支持'],\n      price: 0,\n      priceType: 'fixed',\n    },\n    {\n      id: 'premium',\n      name: '高级支持',\n      description: '7x12小时专业支持',\n      responseTime: '2小时内响应',\n      channels: ['专属客服', '电话支持', '远程协助', '现场支持'],\n      price: 1000,\n      priceType: 'fixed',\n    },\n    {\n      id: 'enterprise',\n      name: '企业支持',\n      description: '7x24小时专属支持',\n      responseTime: '1小时内响应',\n      channels: ['专属经理', '技术专家', '现场支持', '定制开发'],\n      price: 3000,\n      priceType: 'fixed',\n    },\n  ],\n};\n\n// 金蝶精斗云产品定义\nexport const jdyProduct: SoftwareProduct = {\n  id: 'jdy',\n  name: '金蝶精斗云',\n  code: 'JDY',\n  description: '一体化云端企业管理软件，集成多业务模块',\n  category: 'erp',\n  targetCustomer: ['中小企业', '成长型企业', '连锁企业'],\n  basePrice: 2980,\n  priceUnit: 'user/year',\n  versions: [\n    {\n      id: 'basic',\n      name: '基础版',\n      description: '进销存+基础财务',\n      features: ['进销存管理', '基础财务', '库存管理', '销售管理'],\n      maxUsers: 10,\n      priceMultiplier: 0.8,\n      recommended: false,\n    },\n    {\n      id: 'professional',\n      name: '专业版',\n      description: '完整业务管理功能',\n      features: ['进销存', '财务管理', 'CRM', '项目管理', '报表分析'],\n      maxUsers: 50,\n      priceMultiplier: 1.0,\n      recommended: true,\n    },\n    {\n      id: 'enterprise',\n      name: '企业版',\n      description: '全功能企业管理平台',\n      features: ['全业务模块', '工作流', 'BI分析', 'API接口', '移动应用'],\n      maxUsers: 200,\n      priceMultiplier: 1.8,\n      recommended: false,\n    },\n  ],\n  modules: [\n    {\n      id: 'inventory',\n      name: '进销存管理',\n      description: '采购、销售、库存一体化管理',\n      category: '核心模块',\n      price: 0,\n      priceType: 'fixed',\n      required: true,\n    },\n    {\n      id: 'finance',\n      name: '财务管理',\n      description: '完整的财务核算功能',\n      category: '核心模块',\n      price: 800,\n      priceType: 'per_user',\n      required: false,\n    },\n    {\n      id: 'crm',\n      name: 'CRM客户管理',\n      description: '客户关系管理和销售跟进',\n      category: '业务模块',\n      price: 600,\n      priceType: 'per_user',\n      required: false,\n    },\n    {\n      id: 'project',\n      name: '项目管理',\n      description: '项目进度和成本管理',\n      category: '业务模块',\n      price: 500,\n      priceType: 'per_user',\n      required: false,\n    },\n    {\n      id: 'hr',\n      name: '人事管理',\n      description: '员工信息和考勤管理',\n      category: '扩展模块',\n      price: 400,\n      priceType: 'per_user',\n      required: false,\n    },\n  ],\n  services: [\n    {\n      id: 'standard_implementation',\n      name: '标准实施服务',\n      description: '完整的系统实施和培训',\n      type: 'implementation',\n      price: 5000,\n      duration: '10个工作日',\n      deliverables: ['需求调研', '系统配置', '数据导入', '用户培训', '上线支持'],\n    },\n    {\n      id: 'business_consulting',\n      name: '业务咨询服务',\n      description: '业务流程优化和最佳实践',\n      type: 'customization',\n      price: 8000,\n      duration: '15个工作日',\n      deliverables: ['流程梳理', '方案设计', '系统配置', '培训指导'],\n    },\n  ],\n  deploymentOptions: [\n    {\n      id: 'saas',\n      name: 'SaaS云服务',\n      description: '标准云服务，快速上线',\n      type: 'cloud',\n      priceImpact: 0,\n      features: ['即开即用', '弹性付费', '自动更新', '数据安全'],\n    },\n    {\n      id: 'dedicated_cloud',\n      name: '专属云服务',\n      description: '独立云环境，定制配置',\n      type: 'cloud',\n      priceImpact: 5000,\n      features: ['独立环境', '定制功能', '专属支持', '数据隔离'],\n    },\n  ],\n  supportLevels: [\n    {\n      id: 'standard',\n      name: '标准支持',\n      description: '工作日支持服务',\n      responseTime: '4小时内响应',\n      channels: ['在线客服', '电话支持'],\n      price: 0,\n      priceType: 'fixed',\n    },\n    {\n      id: 'premium',\n      name: '高级支持',\n      description: '扩展时间支持服务',\n      responseTime: '2小时内响应',\n      channels: ['专属客服', '电话支持', '远程协助'],\n      price: 2000,\n      priceType: 'fixed',\n    },\n  ],\n};\n\n// 所有软件产品\nexport const allSoftwareProducts: SoftwareProduct[] = [\n  kisCloudProduct,\n  jdyProduct,\n];\n\n// 软件报价计算函数\nexport function calculateSoftwareQuote(\n  productId: string,\n  versionId: string,\n  userCount: number,\n  selectedModules: string[],\n  selectedServices: string[],\n  deploymentId: string,\n  supportId: string,\n  servicePeriod: number = 1\n): {\n  baseAmount: number;\n  moduleAmount: number;\n  serviceAmount: number;\n  deploymentAmount: number;\n  supportAmount: number;\n  totalAmount: number;\n  breakdown: any[];\n} {\n  const product = allSoftwareProducts.find(p => p.id === productId);\n  if (!product) {\n    throw new Error('产品不存在');\n  }\n\n  const version = product.versions.find(v => v.id === versionId);\n  if (!version) {\n    throw new Error('版本不存在');\n  }\n\n  const breakdown: any[] = [];\n\n  // 基础软件费用\n  const baseAmount = product.basePrice * version.priceMultiplier * userCount * servicePeriod;\n  breakdown.push({\n    item: `${product.name} ${version.name}`,\n    quantity: userCount,\n    unit: '用户',\n    unitPrice: product.basePrice * version.priceMultiplier,\n    period: servicePeriod,\n    amount: baseAmount,\n  });\n\n  // 模块费用\n  let moduleAmount = 0;\n  selectedModules.forEach(moduleId => {\n    const module = product.modules.find(m => m.id === moduleId);\n    if (module && !module.required) {\n      let amount = 0;\n      if (module.priceType === 'per_user') {\n        amount = module.price * userCount * servicePeriod;\n      } else {\n        amount = module.price * servicePeriod;\n      }\n      moduleAmount += amount;\n      breakdown.push({\n        item: module.name,\n        quantity: module.priceType === 'per_user' ? userCount : 1,\n        unit: module.priceType === 'per_user' ? '用户' : '套',\n        unitPrice: module.price,\n        period: servicePeriod,\n        amount: amount,\n      });\n    }\n  });\n\n  // 服务费用\n  let serviceAmount = 0;\n  selectedServices.forEach(serviceId => {\n    const service = product.services.find(s => s.id === serviceId);\n    if (service) {\n      serviceAmount += service.price;\n      breakdown.push({\n        item: service.name,\n        quantity: 1,\n        unit: '项',\n        unitPrice: service.price,\n        period: 1,\n        amount: service.price,\n      });\n    }\n  });\n\n  // 部署费用\n  const deployment = product.deploymentOptions.find(d => d.id === deploymentId);\n  const deploymentAmount = deployment ? deployment.priceImpact * servicePeriod : 0;\n  if (deploymentAmount > 0) {\n    breakdown.push({\n      item: deployment!.name,\n      quantity: 1,\n      unit: '套',\n      unitPrice: deployment!.priceImpact,\n      period: servicePeriod,\n      amount: deploymentAmount,\n    });\n  }\n\n  // 支持费用\n  const support = product.supportLevels.find(s => s.id === supportId);\n  const supportAmount = support ? support.price * servicePeriod : 0;\n  if (supportAmount > 0) {\n    breakdown.push({\n      item: support!.name,\n      quantity: 1,\n      unit: '年',\n      unitPrice: support!.price,\n      period: servicePeriod,\n      amount: supportAmount,\n    });\n  }\n\n  const totalAmount = baseAmount + moduleAmount + serviceAmount + deploymentAmount + supportAmount;\n\n  return {\n    baseAmount,\n    moduleAmount,\n    serviceAmount,\n    deploymentAmount,\n    supportAmount,\n    totalAmount,\n    breakdown,\n  };\n}\n"], "mappings": "AAAA;;AAmEA;AACA,OAAO,MAAMA,eAAgC,GAAG;EAC9CC,EAAE,EAAE,WAAW;EACfC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EACzCC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,CACR;IACER,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,eAAe;IAC5BM,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAClCC,QAAQ,EAAE,CAAC;IACXC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,eAAe;IAC5BM,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IACnDC,QAAQ,EAAE,CAAC;IACXC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,cAAc;IAC3BM,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IACpDC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,OAAO,EAAE,CACP;IACEb,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,CAAC,gBAAgB;EACjC,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACElB,EAAE,EAAE,sBAAsB;IAC1BC,IAAI,EAAE,QAAQ;IACdE,WAAW,EAAE,aAAa;IAC1BgB,IAAI,EAAE,gBAAgB;IACtBL,KAAK,EAAE,IAAI;IACXM,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC/C,CAAC,EACD;IACErB,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,QAAQ;IACdE,WAAW,EAAE,UAAU;IACvBgB,IAAI,EAAE,gBAAgB;IACtBL,KAAK,EAAE,IAAI;IACXM,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC/C,CAAC,EACD;IACErB,EAAE,EAAE,mBAAmB;IACvBC,IAAI,EAAE,QAAQ;IACdE,WAAW,EAAE,aAAa;IAC1BgB,IAAI,EAAE,UAAU;IAChBL,KAAK,EAAE,IAAI;IACXM,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC/C,CAAC,CACF;EACDC,iBAAiB,EAAE,CACjB;IACEtB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,aAAa;IAC1BgB,IAAI,EAAE,OAAO;IACbI,WAAW,EAAE,CAAC;IACdd,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;EAC7C,CAAC,EACD;IACET,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,OAAO;IACbE,WAAW,EAAE,aAAa;IAC1BgB,IAAI,EAAE,OAAO;IACbI,WAAW,EAAE,IAAI;IACjBd,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC3C,CAAC,CACF;EACDe,aAAa,EAAE,CACb;IACExB,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,SAAS;IACtBsB,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAClCZ,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE;EACb,CAAC,EACD;IACEf,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,YAAY;IACzBsB,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC,EACD;IACEf,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,YAAY;IACzBsB,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAMY,UAA2B,GAAG;EACzC3B,EAAE,EAAE,KAAK;EACTC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EACzCC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,CACR;IACER,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,UAAU;IACvBM,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,UAAU;IACvBM,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IAChDC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,KAAK;IACXE,WAAW,EAAE,WAAW;IACxBM,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IACnDC,QAAQ,EAAE,GAAG;IACbC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,OAAO,EAAE,CACP;IACEb,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,OAAO;IACbE,WAAW,EAAE,eAAe;IAC5BC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,SAAS;IACfE,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,WAAW;IACxBC,QAAQ,EAAE,MAAM;IAChBU,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,CACF;EACDE,QAAQ,EAAE,CACR;IACElB,EAAE,EAAE,yBAAyB;IAC7BC,IAAI,EAAE,QAAQ;IACdE,WAAW,EAAE,YAAY;IACzBgB,IAAI,EAAE,gBAAgB;IACtBL,KAAK,EAAE,IAAI;IACXM,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EACvD,CAAC,EACD;IACErB,EAAE,EAAE,qBAAqB;IACzBC,IAAI,EAAE,QAAQ;IACdE,WAAW,EAAE,aAAa;IAC1BgB,IAAI,EAAE,eAAe;IACrBL,KAAK,EAAE,IAAI;IACXM,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC/C,CAAC,CACF;EACDC,iBAAiB,EAAE,CACjB;IACEtB,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,SAAS;IACfE,WAAW,EAAE,YAAY;IACzBgB,IAAI,EAAE,OAAO;IACbI,WAAW,EAAE,CAAC;IACdd,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC3C,CAAC,EACD;IACET,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,OAAO;IACbE,WAAW,EAAE,YAAY;IACzBgB,IAAI,EAAE,OAAO;IACbI,WAAW,EAAE,IAAI;IACjBd,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC3C,CAAC,CACF;EACDe,aAAa,EAAE,CACb;IACExB,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,SAAS;IACtBsB,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC1BZ,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE;EACb,CAAC,EACD;IACEf,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,MAAM;IACZE,WAAW,EAAE,UAAU;IACvBsB,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAClCZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAMa,mBAAsC,GAAG,CACpD7B,eAAe,EACf4B,UAAU,CACX;;AAED;AACA,OAAO,SAASE,sBAAsBA,CACpCC,SAAiB,EACjBC,SAAiB,EACjBC,SAAiB,EACjBC,eAAyB,EACzBC,gBAA0B,EAC1BC,YAAoB,EACpBC,SAAiB,EACjBC,aAAqB,GAAG,CAAC,EASzB;EACA,MAAMC,OAAO,GAAGV,mBAAmB,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAK8B,SAAS,CAAC;EACjE,IAAI,CAACQ,OAAO,EAAE;IACZ,MAAM,IAAIG,KAAK,CAAC,OAAO,CAAC;EAC1B;EAEA,MAAMC,OAAO,GAAGJ,OAAO,CAAC9B,QAAQ,CAAC+B,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC3C,EAAE,KAAK+B,SAAS,CAAC;EAC9D,IAAI,CAACW,OAAO,EAAE;IACZ,MAAM,IAAID,KAAK,CAAC,OAAO,CAAC;EAC1B;EAEA,MAAMG,SAAgB,GAAG,EAAE;;EAE3B;EACA,MAAMC,UAAU,GAAGP,OAAO,CAAChC,SAAS,GAAGoC,OAAO,CAAC/B,eAAe,GAAGqB,SAAS,GAAGK,aAAa;EAC1FO,SAAS,CAACE,IAAI,CAAC;IACbC,IAAI,EAAE,GAAGT,OAAO,CAACrC,IAAI,IAAIyC,OAAO,CAACzC,IAAI,EAAE;IACvC+C,QAAQ,EAAEhB,SAAS;IACnBiB,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEZ,OAAO,CAAChC,SAAS,GAAGoC,OAAO,CAAC/B,eAAe;IACtDwC,MAAM,EAAEd,aAAa;IACrBe,MAAM,EAAEP;EACV,CAAC,CAAC;;EAEF;EACA,IAAIQ,YAAY,GAAG,CAAC;EACpBpB,eAAe,CAACqB,OAAO,CAACC,QAAQ,IAAI;IAClC,MAAMC,MAAM,GAAGlB,OAAO,CAACzB,OAAO,CAAC0B,IAAI,CAACkB,CAAC,IAAIA,CAAC,CAACzD,EAAE,KAAKuD,QAAQ,CAAC;IAC3D,IAAIC,MAAM,IAAI,CAACA,MAAM,CAACxC,QAAQ,EAAE;MAC9B,IAAIoC,MAAM,GAAG,CAAC;MACd,IAAII,MAAM,CAACzC,SAAS,KAAK,UAAU,EAAE;QACnCqC,MAAM,GAAGI,MAAM,CAAC1C,KAAK,GAAGkB,SAAS,GAAGK,aAAa;MACnD,CAAC,MAAM;QACLe,MAAM,GAAGI,MAAM,CAAC1C,KAAK,GAAGuB,aAAa;MACvC;MACAgB,YAAY,IAAID,MAAM;MACtBR,SAAS,CAACE,IAAI,CAAC;QACbC,IAAI,EAAES,MAAM,CAACvD,IAAI;QACjB+C,QAAQ,EAAEQ,MAAM,CAACzC,SAAS,KAAK,UAAU,GAAGiB,SAAS,GAAG,CAAC;QACzDiB,IAAI,EAAEO,MAAM,CAACzC,SAAS,KAAK,UAAU,GAAG,IAAI,GAAG,GAAG;QAClDmC,SAAS,EAAEM,MAAM,CAAC1C,KAAK;QACvBqC,MAAM,EAAEd,aAAa;QACrBe,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,IAAIM,aAAa,GAAG,CAAC;EACrBxB,gBAAgB,CAACoB,OAAO,CAACK,SAAS,IAAI;IACpC,MAAMC,OAAO,GAAGtB,OAAO,CAACpB,QAAQ,CAACqB,IAAI,CAACsB,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAK2D,SAAS,CAAC;IAC9D,IAAIC,OAAO,EAAE;MACXF,aAAa,IAAIE,OAAO,CAAC9C,KAAK;MAC9B8B,SAAS,CAACE,IAAI,CAAC;QACbC,IAAI,EAAEa,OAAO,CAAC3D,IAAI;QAClB+C,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,GAAG;QACTC,SAAS,EAAEU,OAAO,CAAC9C,KAAK;QACxBqC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAEQ,OAAO,CAAC9C;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,MAAMgD,UAAU,GAAGxB,OAAO,CAAChB,iBAAiB,CAACiB,IAAI,CAACwB,CAAC,IAAIA,CAAC,CAAC/D,EAAE,KAAKmC,YAAY,CAAC;EAC7E,MAAM6B,gBAAgB,GAAGF,UAAU,GAAGA,UAAU,CAACvC,WAAW,GAAGc,aAAa,GAAG,CAAC;EAChF,IAAI2B,gBAAgB,GAAG,CAAC,EAAE;IACxBpB,SAAS,CAACE,IAAI,CAAC;MACbC,IAAI,EAAEe,UAAU,CAAE7D,IAAI;MACtB+C,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,GAAG;MACTC,SAAS,EAAEY,UAAU,CAAEvC,WAAW;MAClC4B,MAAM,EAAEd,aAAa;MACrBe,MAAM,EAAEY;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,OAAO,GAAG3B,OAAO,CAACd,aAAa,CAACe,IAAI,CAACsB,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAKoC,SAAS,CAAC;EACnE,MAAM8B,aAAa,GAAGD,OAAO,GAAGA,OAAO,CAACnD,KAAK,GAAGuB,aAAa,GAAG,CAAC;EACjE,IAAI6B,aAAa,GAAG,CAAC,EAAE;IACrBtB,SAAS,CAACE,IAAI,CAAC;MACbC,IAAI,EAAEkB,OAAO,CAAEhE,IAAI;MACnB+C,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,GAAG;MACTC,SAAS,EAAEe,OAAO,CAAEnD,KAAK;MACzBqC,MAAM,EAAEd,aAAa;MACrBe,MAAM,EAAEc;IACV,CAAC,CAAC;EACJ;EAEA,MAAMC,WAAW,GAAGtB,UAAU,GAAGQ,YAAY,GAAGK,aAAa,GAAGM,gBAAgB,GAAGE,aAAa;EAEhG,OAAO;IACLrB,UAAU;IACVQ,YAAY;IACZK,aAAa;IACbM,gBAAgB;IAChBE,aAAa;IACbC,WAAW;IACXvB;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}