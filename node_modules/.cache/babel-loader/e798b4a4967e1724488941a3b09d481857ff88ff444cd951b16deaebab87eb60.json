{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { get } from '../utils/js_utils.js';\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds.js';\nimport { reduce as dragOffset } from './dragOffset.js';\nimport { reduce as dragOperation } from './dragOperation.js';\nimport { reduce as refCount } from './refCount.js';\nimport { reduce as stateId } from './stateId.js';\nexport function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let action = arguments.length > 1 ? arguments[1] : undefined;\n  return {\n    dirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n      type: action.type,\n      payload: _objectSpread({}, action.payload, {\n        prevTargetIds: get(state, 'dragOperation.targetIds', [])\n      })\n    }),\n    dragOffset: dragOffset(state.dragOffset, action),\n    refCount: refCount(state.refCount, action),\n    dragOperation: dragOperation(state.dragOperation, action),\n    stateId: stateId(state.stateId)\n  };\n}", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "get", "reduce", "dirtyHandlerIds", "dragOffset", "dragOperation", "refCount", "stateId", "state", "arguments", "length", "undefined", "action", "type", "payload", "_objectSpread", "prevTargetIds"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/reducers/index.ts"], "sourcesContent": ["import type { Action } from '../interfaces.js'\nimport { get } from '../utils/js_utils.js'\nimport type { State as DirtyHandlerIdsState } from './dirtyHandlerIds.js'\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds.js'\nimport type { State as DragOffsetState } from './dragOffset.js'\nimport { reduce as dragOffset } from './dragOffset.js'\nimport type { State as DragOperationState } from './dragOperation.js'\nimport { reduce as dragOperation } from './dragOperation.js'\nimport type { State as RefCountState } from './refCount.js'\nimport { reduce as refCount } from './refCount.js'\nimport type { State as StateIdState } from './stateId.js'\nimport { reduce as stateId } from './stateId.js'\n\nexport interface State {\n\tdirtyHandlerIds: DirtyHandlerIdsState\n\tdragOffset: DragOffsetState\n\trefCount: RefCountState\n\tdragOperation: DragOperationState\n\tstateId: StateIdState\n}\n\nexport function reduce(state: State = {} as State, action: Action<any>): State {\n\treturn {\n\t\tdirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n\t\t\ttype: action.type,\n\t\t\tpayload: {\n\t\t\t\t...action.payload,\n\t\t\t\tprevTargetIds: get<string[]>(state, 'dragOperation.targetIds', []),\n\t\t\t},\n\t\t}),\n\t\tdragOffset: dragOffset(state.dragOffset, action),\n\t\trefCount: refCount(state.refCount, action),\n\t\tdragOperation: dragOperation(state.dragOperation, action),\n\t\tstateId: stateId(state.stateId),\n\t}\n}\n"], "mappings": "AAAA,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASC,GAAG,QAAQ,sBAAsB;AAE1C,SAASC,MAAM,IAAIC,eAAe,QAAQ,sBAAsB;AAEhE,SAASD,MAAM,IAAIE,UAAU,QAAQ,iBAAiB;AAEtD,SAASF,MAAM,IAAIG,aAAa,QAAQ,oBAAoB;AAE5D,SAASH,MAAM,IAAII,QAAQ,QAAQ,eAAe;AAElD,SAASJ,MAAM,IAAIK,OAAO,QAAQ,cAAc;AAUhD,OAAO,SAASL,MAAMA,CAAA,EAAyD;EAAA,IAAxDM,KAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAWG,MAAmB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACrE,OAAO;IACNR,eAAe,EAAEA,eAAe,CAACK,KAAK,CAACL,eAAe,EAAE;MACvDU,IAAI,EAAED,MAAM,CAACC,IAAI;MACjBC,OAAO,EAAEC,aAAA,KACLH,MAAM,CAACE,OAAO;QACjBE,aAAa,EAAEf,GAAG,CAAWO,KAAK,EAAE,yBAAyB,EAAE,EAAE;;KAElE,CAAC;IACFJ,UAAU,EAAEA,UAAU,CAACI,KAAK,CAACJ,UAAU,EAAEQ,MAAM,CAAC;IAChDN,QAAQ,EAAEA,QAAQ,CAACE,KAAK,CAACF,QAAQ,EAAEM,MAAM,CAAC;IAC1CP,aAAa,EAAEA,aAAa,CAACG,KAAK,CAACH,aAAa,EAAEO,MAAM,CAAC;IACzDL,OAAO,EAAEA,OAAO,CAACC,KAAK,CAACD,OAAO;GAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}