{"ast": null, "code": "import{createSlice,createAsyncThunk}from'@reduxjs/toolkit';import{mockApiService}from'@/services/api';const initialState={dashboardData:null,salesPerformance:null,productPerformance:null,customerBehavior:null,loading:false,error:null};export const fetchDashboardData=createAsyncThunk('analytics/fetchDashboardData',async()=>{const response=await mockApiService.getDashboardData();return response.data;});const analyticsSlice=createSlice({name:'analytics',initialState,reducers:{clearError:state=>{state.error=null;}},extraReducers:builder=>{builder.addCase(fetchDashboardData.pending,state=>{state.loading=true;state.error=null;}).addCase(fetchDashboardData.fulfilled,(state,action)=>{state.loading=false;state.dashboardData=action.payload;}).addCase(fetchDashboardData.rejected,(state,action)=>{state.loading=false;state.error=action.error.message||'获取仪表板数据失败';});}});export const{clearError}=analyticsSlice.actions;export default analyticsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "mockApiService", "initialState", "dashboardData", "salesPerformance", "productPerformance", "customerBehavior", "loading", "error", "fetchDashboardData", "response", "getDashboardData", "data", "analyticsSlice", "name", "reducers", "clearError", "state", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "message", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { mockApiService } from '@/services/api';\n\ninterface AnalyticsState {\n  dashboardData: any;\n  salesPerformance: any;\n  productPerformance: any;\n  customerBehavior: any;\n  loading: boolean;\n  error: string | null;\n}\n\nconst initialState: AnalyticsState = {\n  dashboardData: null,\n  salesPerformance: null,\n  productPerformance: null,\n  customerBehavior: null,\n  loading: false,\n  error: null,\n};\n\nexport const fetchDashboardData = createAsyncThunk(\n  'analytics/fetchDashboardData',\n  async () => {\n    const response = await mockApiService.getDashboardData();\n    return response.data;\n  }\n);\n\nconst analyticsSlice = createSlice({\n  name: 'analytics',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchDashboardData.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchDashboardData.fulfilled, (state, action) => {\n        state.loading = false;\n        state.dashboardData = action.payload;\n      })\n      .addCase(fetchDashboardData.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || '获取仪表板数据失败';\n      });\n  },\n});\n\nexport const { clearError } = analyticsSlice.actions;\nexport default analyticsSlice.reducer;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,gBAAgB,KAAQ,kBAAkB,CAChE,OAASC,cAAc,KAAQ,gBAAgB,CAW/C,KAAM,CAAAC,YAA4B,CAAG,CACnCC,aAAa,CAAE,IAAI,CACnBC,gBAAgB,CAAE,IAAI,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,gBAAgB,CAAE,IAAI,CACtBC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,IACT,CAAC,CAED,MAAO,MAAM,CAAAC,kBAAkB,CAAGT,gBAAgB,CAChD,8BAA8B,CAC9B,SAAY,CACV,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAT,cAAc,CAACU,gBAAgB,CAAC,CAAC,CACxD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGd,WAAW,CAAC,CACjCe,IAAI,CAAE,WAAW,CACjBZ,YAAY,CACZa,QAAQ,CAAE,CACRC,UAAU,CAAGC,KAAK,EAAK,CACrBA,KAAK,CAACT,KAAK,CAAG,IAAI,CACpB,CACF,CAAC,CACDU,aAAa,CAAGC,OAAO,EAAK,CAC1BA,OAAO,CACJC,OAAO,CAACX,kBAAkB,CAACY,OAAO,CAAGJ,KAAK,EAAK,CAC9CA,KAAK,CAACV,OAAO,CAAG,IAAI,CACpBU,KAAK,CAACT,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACDY,OAAO,CAACX,kBAAkB,CAACa,SAAS,CAAE,CAACL,KAAK,CAAEM,MAAM,GAAK,CACxDN,KAAK,CAACV,OAAO,CAAG,KAAK,CACrBU,KAAK,CAACd,aAAa,CAAGoB,MAAM,CAACC,OAAO,CACtC,CAAC,CAAC,CACDJ,OAAO,CAACX,kBAAkB,CAACgB,QAAQ,CAAE,CAACR,KAAK,CAAEM,MAAM,GAAK,CACvDN,KAAK,CAACV,OAAO,CAAG,KAAK,CACrBU,KAAK,CAACT,KAAK,CAAGe,MAAM,CAACf,KAAK,CAACkB,OAAO,EAAI,WAAW,CACnD,CAAC,CAAC,CACN,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEV,UAAW,CAAC,CAAGH,cAAc,CAACc,OAAO,CACpD,cAAe,CAAAd,cAAc,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}