{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/components/TeslaStyleConfigurator.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Typography, Space, Steps, Progress, Tag, Avatar, Statistic, Timeline, Alert, Badge } from 'antd';\nimport { UserOutlined, TeamOutlined, RocketOutlined, CheckCircleOutlined, StarOutlined, TrophyOutlined, BulbOutlined, DollarOutlined, ClockCircleOutlined, RiseOutlined } from '@ant-design/icons';\nimport { allIndustries, getRecommendedConfiguration } from '@/data/industryRoleConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Step\n} = Steps;\nconst TeslaStyleConfigurator = () => {\n  _s();\n  const [config, setConfig] = useState({\n    industry: '',\n    role: '',\n    companySize: 'small',\n    selectedPainPoints: [],\n    currentStep: 0\n  });\n  const [recommendation, setRecommendation] = useState(null);\n  const [selectedIndustry, setSelectedIndustry] = useState(null);\n  const [selectedRole, setSelectedRole] = useState(null);\n  useEffect(() => {\n    if (config.industry) {\n      const industry = allIndustries.find(i => i.id === config.industry);\n      setSelectedIndustry(industry || null);\n    }\n  }, [config.industry]);\n  useEffect(() => {\n    if (selectedIndustry && config.role) {\n      const role = selectedIndustry.commonRoles.find(r => r.id === config.role);\n      setSelectedRole(role || null);\n    }\n  }, [selectedIndustry, config.role]);\n  useEffect(() => {\n    if (config.industry && config.role && config.selectedPainPoints.length > 0) {\n      try {\n        const rec = getRecommendedConfiguration(config.industry, config.role, config.companySize, config.selectedPainPoints);\n        setRecommendation(rec);\n      } catch (error) {\n        console.error('获取推荐配置失败:', error);\n      }\n    }\n  }, [config]);\n  const handleNext = () => {\n    setConfig(prev => ({\n      ...prev,\n      currentStep: prev.currentStep + 1\n    }));\n  };\n  const handlePrev = () => {\n    setConfig(prev => ({\n      ...prev,\n      currentStep: prev.currentStep - 1\n    }));\n  };\n  const renderIndustrySelection = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '40px 0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      style: {\n        marginBottom: 8\n      },\n      children: \"\\u9009\\u62E9\\u60A8\\u7684\\u884C\\u4E1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      style: {\n        fontSize: 16,\n        color: '#666',\n        marginBottom: 40\n      },\n      children: \"\\u6211\\u4EEC\\u5C06\\u6839\\u636E\\u60A8\\u7684\\u884C\\u4E1A\\u7279\\u70B9\\uFF0C\\u4E3A\\u60A8\\u63A8\\u8350\\u6700\\u9002\\u5408\\u7684\\u89E3\\u51B3\\u65B9\\u6848\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      justify: \"center\",\n      children: allIndustries.map(industry => /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          className: config.industry === industry.id ? 'selected-card' : '',\n          onClick: () => setConfig(prev => ({\n            ...prev,\n            industry: industry.id,\n            role: '',\n            selectedPainPoints: []\n          })),\n          style: {\n            height: 280,\n            border: config.industry === industry.id ? '3px solid #1890ff' : '1px solid #d9d9d9',\n            borderRadius: 12,\n            transition: 'all 0.3s ease'\n          },\n          bodyStyle: {\n            padding: 24,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: 48,\n              marginBottom: 16\n            },\n            children: [industry.icon === 'factory' && '🏭', industry.icon === 'shop' && '🏪', industry.icon === 'customer-service' && '💼']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              marginBottom: 12\n            },\n            children: industry.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              color: '#666',\n              fontSize: 14,\n              lineHeight: 1.6\n            },\n            children: industry.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: [industry.painPoints.length, \" \\u4E2A\\u75DB\\u70B9\\u89E3\\u51B3\\u65B9\\u6848\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)\n      }, industry.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n  const renderRoleSelection = () => {\n    if (!selectedIndustry) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '40px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 40\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u60A8\\u5728\\u516C\\u53F8\\u7684\\u89D2\\u8272\\u662F\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: 16,\n            color: '#666'\n          },\n          children: \"\\u4E0D\\u540C\\u89D2\\u8272\\u5173\\u6CE8\\u7684\\u91CD\\u70B9\\u4E0D\\u540C\\uFF0C\\u6211\\u4EEC\\u5C06\\u4E3A\\u60A8\\u63D0\\u4F9B\\u4E2A\\u6027\\u5316\\u7684\\u529F\\u80FD\\u63A8\\u8350\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: selectedIndustry.commonRoles.map(role => /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            hoverable: true,\n            className: config.role === role.id ? 'selected-card' : '',\n            onClick: () => setConfig(prev => ({\n              ...prev,\n              role: role.id,\n              selectedPainPoints: []\n            })),\n            style: {\n              border: config.role === role.id ? '3px solid #1890ff' : '1px solid #d9d9d9',\n              borderRadius: 12,\n              height: 320\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                size: 64,\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 43\n                }, this),\n                style: {\n                  backgroundColor: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Title, {\n                  level: 4,\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: role.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  style: {\n                    color: '#666',\n                    marginBottom: 16\n                  },\n                  children: role.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 12\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      color: '#1890ff'\n                    },\n                    children: \"\\u4E3B\\u8981\\u804C\\u8D23\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 4\n                    },\n                    children: role.responsibilities.slice(0, 3).map((resp, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                      style: {\n                        marginBottom: 4\n                      },\n                      children: resp\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      color: '#f5222d'\n                    },\n                    children: \"\\u5173\\u6CE8\\u75DB\\u70B9\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 4\n                    },\n                    children: role.painPoints.slice(0, 2).map((pain, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"red\",\n                      style: {\n                        marginBottom: 4\n                      },\n                      children: pain\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, role.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  };\n  const renderPainPointSelection = () => {\n    if (!selectedIndustry) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '40px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 40\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u60A8\\u5F53\\u524D\\u9762\\u4E34\\u7684\\u4E3B\\u8981\\u6311\\u6218\\u662F\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: 16,\n            color: '#666'\n          },\n          children: \"\\u9009\\u62E9\\u60A8\\u6700\\u5173\\u5FC3\\u7684\\u4E1A\\u52A1\\u75DB\\u70B9\\uFF0C\\u6211\\u4EEC\\u5C06\\u4E3A\\u60A8\\u63A8\\u8350\\u9488\\u5BF9\\u6027\\u7684\\u89E3\\u51B3\\u65B9\\u6848\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: selectedIndustry.painPoints.map(painPoint => /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            hoverable: true,\n            className: config.selectedPainPoints.includes(painPoint.id) ? 'selected-card' : '',\n            onClick: () => {\n              const newPainPoints = config.selectedPainPoints.includes(painPoint.id) ? config.selectedPainPoints.filter(p => p !== painPoint.id) : [...config.selectedPainPoints, painPoint.id];\n              setConfig(prev => ({\n                ...prev,\n                selectedPainPoints: newPainPoints\n              }));\n            },\n            style: {\n              border: config.selectedPainPoints.includes(painPoint.id) ? '3px solid #1890ff' : '1px solid #d9d9d9',\n              borderRadius: 12\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 12,\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Title, {\n                    level: 4,\n                    style: {\n                      margin: 0\n                    },\n                    children: painPoint.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    count: `${painPoint.frequency}%`,\n                    style: {\n                      backgroundColor: painPoint.impact === 'high' ? '#f5222d' : '#faad14'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  style: {\n                    color: '#666',\n                    marginBottom: 16\n                  },\n                  children: painPoint.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), painPoint.solutions.map(solution => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(BulbOutlined, {\n                      style: {\n                        color: '#52c41a'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      style: {\n                        color: '#52c41a'\n                      },\n                      children: solution.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"green\",\n                      children: solution.estimatedROI\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)\n                }, solution.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Progress, {\n                  type: \"circle\",\n                  percent: painPoint.frequency,\n                  width: 60,\n                  format: () => `${painPoint.frequency}%`,\n                  strokeColor: painPoint.impact === 'high' ? '#f5222d' : '#faad14'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8,\n                    fontSize: 12,\n                    color: '#666'\n                  },\n                  children: \"\\u884C\\u4E1A\\u666E\\u904D\\u6027\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, painPoint.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  };\n  const renderRecommendation = () => {\n    if (!recommendation || !selectedIndustry || !selectedRole) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '40px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 40\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\uD83C\\uDF89 \\u4E3A\\u60A8\\u91CF\\u8EAB\\u5B9A\\u5236\\u7684\\u89E3\\u51B3\\u65B9\\u6848\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: 16,\n            color: '#666'\n          },\n          children: \"\\u57FA\\u4E8E\\u60A8\\u7684\\u884C\\u4E1A\\u7279\\u70B9\\u548C\\u89D2\\u8272\\u9700\\u6C42\\uFF0C\\u6211\\u4EEC\\u4E3A\\u60A8\\u63A8\\u8350\\u4EE5\\u4E0B\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 16,\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u63A8\\u8350\\u914D\\u7F6E\\u8BE6\\u60C5\",\n            style: {\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(Timeline, {\n              children: [/*#__PURE__*/_jsxDEV(Timeline.Item, {\n                dot: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                  style: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 24\n                }, this),\n                color: \"green\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u4EA7\\u54C1\\u9009\\u62E9\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: recommendation.recommendedProduct === 'kis-cloud' ? '金蝶云星辰' : recommendation.recommendedProduct === 'jdy' ? '金蝶精斗云' : 'K/3 Cloud'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8,\n                    color: '#666'\n                  },\n                  children: [\"\\u6700\\u9002\\u5408\", selectedIndustry.name, selectedRole.name, \"\\u7684\\u4E13\\u4E1A\\u89E3\\u51B3\\u65B9\\u6848\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Timeline.Item, {\n                dot: /*#__PURE__*/_jsxDEV(RocketOutlined, {\n                  style: {\n                    color: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 24\n                }, this),\n                color: \"blue\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u6838\\u5FC3\\u529F\\u80FD\\u6A21\\u5757\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: recommendation.recommendedModules.map((module, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    style: {\n                      marginBottom: 4\n                    },\n                    children: module\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Timeline.Item, {\n                dot: /*#__PURE__*/_jsxDEV(TeamOutlined, {\n                  style: {\n                    color: '#faad14'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 24\n                }, this),\n                color: \"orange\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5B9E\\u65BD\\u670D\\u52A1\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: recommendation.recommendedServices.map((service, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"orange\",\n                    style: {\n                      marginBottom: 4\n                    },\n                    children: service\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6295\\u8D44\\u56DE\\u62A5\\u9884\\u671F\",\n            description: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"\\u2022 \", recommendation.roi]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u2022 \\u9884\\u8BA1\\u63D0\\u5347\\u5DE5\\u4F5C\\u6548\\u7387 30-50%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u2022 \\u51CF\\u5C11\\u4EBA\\u5DE5\\u9519\\u8BEF 80% \\u4EE5\\u4E0A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u2022 \\u63D0\\u5347\\u51B3\\u7B56\\u6548\\u7387 60% \\u4EE5\\u4E0A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this),\n            type: \"success\",\n            showIcon: true,\n            icon: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u4EF7\\u683C\\u9884\\u4F30\",\n            style: {\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u9884\\u4F30\\u603B\\u4EF7\",\n                value: recommendation.estimatedPrice,\n                precision: 0,\n                prefix: \"\\xA5\",\n                valueStyle: {\n                  color: '#1890ff',\n                  fontSize: 32\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16,\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u63A8\\u8350\\u7528\\u6237\\u6570\\uFF1A\", recommendation.estimatedUsers, \" \\u4EBA\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u5305\\u542B\\u5B9E\\u65BD\\u670D\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u9996\\u5E74\\u4EF7\\u683C\\uFF0C\\u7EED\\u8D39\\u4EAB\\u4F18\\u60E0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u4E3A\\u4EC0\\u4E48\\u9009\\u62E9\\u6211\\u4EEC\\uFF1F\",\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(StarOutlined, {\n                  style: {\n                    color: '#faad14'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u884C\\u4E1A\\u9886\\u5148\\u7684\\u8D22\\u52A1\\u8F6F\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(RiseOutlined, {\n                  style: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"500\\u4E07+ \\u4F01\\u4E1A\\u7684\\u9009\\u62E9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n                  style: {\n                    color: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"7x24\\u5C0F\\u65F6\\u4E13\\u4E1A\\u652F\\u6301\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(DollarOutlined, {\n                  style: {\n                    color: '#722ed1'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u7075\\u6D3B\\u7684\\u4ED8\\u8D39\\u65B9\\u5F0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: 40\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            style: {\n              minWidth: 120\n            },\n            children: \"\\u7ACB\\u5373\\u8D2D\\u4E70\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            style: {\n              minWidth: 120\n            },\n            children: \"\\u7533\\u8BF7\\u8BD5\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            style: {\n              minWidth: 120\n            },\n            children: \"\\u8054\\u7CFB\\u987E\\u95EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this);\n  };\n  const steps = [{\n    title: '选择行业',\n    content: renderIndustrySelection()\n  }, {\n    title: '确定角色',\n    content: renderRoleSelection()\n  }, {\n    title: '识别痛点',\n    content: renderPainPointSelection()\n  }, {\n    title: '推荐方案',\n    content: renderRecommendation()\n  }];\n  const canProceed = () => {\n    switch (config.currentStep) {\n      case 0:\n        return config.industry !== '';\n      case 1:\n        return config.role !== '';\n      case 2:\n        return config.selectedPainPoints.length > 0;\n      default:\n        return true;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '40px 24px',\n        maxWidth: 1200,\n        margin: '0 auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        style: {\n          borderRadius: 16,\n          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '20px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Steps, {\n            current: config.currentStep,\n            style: {\n              marginBottom: 40\n            },\n            size: \"small\",\n            children: steps.map(item => /*#__PURE__*/_jsxDEV(Step, {\n              title: item.title\n            }, item.title, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              minHeight: 500\n            },\n            children: steps[config.currentStep].content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: 40,\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [config.currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n                size: \"large\",\n                onClick: handlePrev,\n                children: \"\\u4E0A\\u4E00\\u6B65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), config.currentStep < steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                size: \"large\",\n                onClick: handleNext,\n                disabled: !canProceed(),\n                children: \"\\u4E0B\\u4E00\\u6B65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .selected-card {\n          transform: translateY(-4px);\n          box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2) !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 433,\n    columnNumber: 5\n  }, this);\n};\n_s(TeslaStyleConfigurator, \"W3WYkT9zi8OXPFNG6P6tBngya9A=\");\n_c = TeslaStyleConfigurator;\nexport default TeslaStyleConfigurator;\nvar _c;\n$RefreshReg$(_c, \"TeslaStyleConfigurator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Typography", "Space", "Steps", "Progress", "Tag", "Avatar", "Statistic", "Timeline", "<PERSON><PERSON>", "Badge", "UserOutlined", "TeamOutlined", "RocketOutlined", "CheckCircleOutlined", "StarOutlined", "TrophyOutlined", "BulbOutlined", "DollarOutlined", "ClockCircleOutlined", "RiseOutlined", "allIndustries", "getRecommendedConfiguration", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Step", "TeslaStyleConfigurator", "_s", "config", "setConfig", "industry", "role", "companySize", "selectedPainPoints", "currentStep", "recommendation", "setRecommendation", "selectedIndustry", "setSelectedIndustry", "selectedR<PERSON>", "setSelectedRole", "find", "i", "id", "commonRoles", "r", "length", "rec", "error", "console", "handleNext", "prev", "handlePrev", "renderIndustrySelection", "style", "textAlign", "padding", "children", "level", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "gutter", "justify", "map", "span", "hoverable", "className", "onClick", "height", "border", "borderRadius", "transition", "bodyStyle", "icon", "name", "lineHeight", "description", "marginTop", "painPoints", "renderRoleSelection", "display", "alignItems", "gap", "size", "backgroundColor", "flex", "strong", "responsibilities", "slice", "resp", "index", "pain", "renderPainPointSelection", "painPoint", "includes", "newPainPoints", "filter", "p", "margin", "title", "count", "frequency", "impact", "solutions", "solution", "estimatedROI", "type", "percent", "width", "format", "strokeColor", "renderRecommendation", "<PERSON><PERSON>", "dot", "marginLeft", "recommendedProduct", "recommendedModules", "module", "recommendedServices", "service", "message", "direction", "roi", "showIcon", "value", "estimatedPrice", "precision", "prefix", "valueStyle", "estimatedUsers", "min<PERSON><PERSON><PERSON>", "steps", "content", "canProceed", "minHeight", "background", "max<PERSON><PERSON><PERSON>", "boxShadow", "current", "item", "borderTop", "paddingTop", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/components/TeslaStyleConfigurator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Typography,\n  Space,\n  Steps,\n  Radio,\n  Checkbox,\n  Progress,\n  Tag,\n  Avatar,\n  Statistic,\n  Timeline,\n  Alert,\n  Tooltip,\n  Badge,\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  RocketOutlined,\n  CheckCircleOutlined,\n  StarOutlined,\n  TrophyOutlined,\n  BulbOutlined,\n  DollarOutlined,\n  ClockCircleOutlined,\n  RiseOutlined,\n} from '@ant-design/icons';\nimport { allIndustries, getRecommendedConfiguration, Industry, Role, PainPoint } from '@/data/industryRoleConfig';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Step } = Steps;\n\ninterface ConfigurationState {\n  industry: string;\n  role: string;\n  companySize: 'small' | 'medium' | 'large';\n  selectedPainPoints: string[];\n  currentStep: number;\n}\n\nconst TeslaStyleConfigurator: React.FC = () => {\n  const [config, setConfig] = useState<ConfigurationState>({\n    industry: '',\n    role: '',\n    companySize: 'small',\n    selectedPainPoints: [],\n    currentStep: 0,\n  });\n\n  const [recommendation, setRecommendation] = useState<any>(null);\n  const [selectedIndustry, setSelectedIndustry] = useState<Industry | null>(null);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n\n  useEffect(() => {\n    if (config.industry) {\n      const industry = allIndustries.find(i => i.id === config.industry);\n      setSelectedIndustry(industry || null);\n    }\n  }, [config.industry]);\n\n  useEffect(() => {\n    if (selectedIndustry && config.role) {\n      const role = selectedIndustry.commonRoles.find(r => r.id === config.role);\n      setSelectedRole(role || null);\n    }\n  }, [selectedIndustry, config.role]);\n\n  useEffect(() => {\n    if (config.industry && config.role && config.selectedPainPoints.length > 0) {\n      try {\n        const rec = getRecommendedConfiguration(\n          config.industry,\n          config.role,\n          config.companySize,\n          config.selectedPainPoints\n        );\n        setRecommendation(rec);\n      } catch (error) {\n        console.error('获取推荐配置失败:', error);\n      }\n    }\n  }, [config]);\n\n  const handleNext = () => {\n    setConfig(prev => ({ ...prev, currentStep: prev.currentStep + 1 }));\n  };\n\n  const handlePrev = () => {\n    setConfig(prev => ({ ...prev, currentStep: prev.currentStep - 1 }));\n  };\n\n  const renderIndustrySelection = () => (\n    <div style={{ textAlign: 'center', padding: '40px 0' }}>\n      <Title level={2} style={{ marginBottom: 8 }}>选择您的行业</Title>\n      <Paragraph style={{ fontSize: 16, color: '#666', marginBottom: 40 }}>\n        我们将根据您的行业特点，为您推荐最适合的解决方案\n      </Paragraph>\n      \n      <Row gutter={[24, 24]} justify=\"center\">\n        {allIndustries.map(industry => (\n          <Col key={industry.id} span={8}>\n            <Card\n              hoverable\n              className={config.industry === industry.id ? 'selected-card' : ''}\n              onClick={() => setConfig(prev => ({ ...prev, industry: industry.id, role: '', selectedPainPoints: [] }))}\n              style={{\n                height: 280,\n                border: config.industry === industry.id ? '3px solid #1890ff' : '1px solid #d9d9d9',\n                borderRadius: 12,\n                transition: 'all 0.3s ease',\n              }}\n              bodyStyle={{ padding: 24, textAlign: 'center' }}\n            >\n              <div style={{ fontSize: 48, marginBottom: 16 }}>\n                {industry.icon === 'factory' && '🏭'}\n                {industry.icon === 'shop' && '🏪'}\n                {industry.icon === 'customer-service' && '💼'}\n              </div>\n              <Title level={4} style={{ marginBottom: 12 }}>{industry.name}</Title>\n              <Paragraph style={{ color: '#666', fontSize: 14, lineHeight: 1.6 }}>\n                {industry.description}\n              </Paragraph>\n              <div style={{ marginTop: 16 }}>\n                <Tag color=\"blue\">{industry.painPoints.length} 个痛点解决方案</Tag>\n              </div>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n    </div>\n  );\n\n  const renderRoleSelection = () => {\n    if (!selectedIndustry) return null;\n\n    return (\n      <div style={{ padding: '40px 0' }}>\n        <div style={{ textAlign: 'center', marginBottom: 40 }}>\n          <Title level={2}>您在公司的角色是？</Title>\n          <Paragraph style={{ fontSize: 16, color: '#666' }}>\n            不同角色关注的重点不同，我们将为您提供个性化的功能推荐\n          </Paragraph>\n        </div>\n\n        <Row gutter={[24, 24]}>\n          {selectedIndustry.commonRoles.map(role => (\n            <Col key={role.id} span={12}>\n              <Card\n                hoverable\n                className={config.role === role.id ? 'selected-card' : ''}\n                onClick={() => setConfig(prev => ({ ...prev, role: role.id, selectedPainPoints: [] }))}\n                style={{\n                  border: config.role === role.id ? '3px solid #1890ff' : '1px solid #d9d9d9',\n                  borderRadius: 12,\n                  height: 320,\n                }}\n              >\n                <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>\n                  <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />\n                  <div style={{ flex: 1 }}>\n                    <Title level={4} style={{ marginBottom: 8 }}>{role.name}</Title>\n                    <Paragraph style={{ color: '#666', marginBottom: 16 }}>\n                      {role.description}\n                    </Paragraph>\n                    \n                    <div style={{ marginBottom: 12 }}>\n                      <Text strong style={{ color: '#1890ff' }}>主要职责：</Text>\n                      <div style={{ marginTop: 4 }}>\n                        {role.responsibilities.slice(0, 3).map((resp, index) => (\n                          <Tag key={index} style={{ marginBottom: 4 }}>{resp}</Tag>\n                        ))}\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <Text strong style={{ color: '#f5222d' }}>关注痛点：</Text>\n                      <div style={{ marginTop: 4 }}>\n                        {role.painPoints.slice(0, 2).map((pain, index) => (\n                          <Tag key={index} color=\"red\" style={{ marginBottom: 4 }}>{pain}</Tag>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </Card>\n            </Col>\n          ))}\n        </Row>\n      </div>\n    );\n  };\n\n  const renderPainPointSelection = () => {\n    if (!selectedIndustry) return null;\n\n    return (\n      <div style={{ padding: '40px 0' }}>\n        <div style={{ textAlign: 'center', marginBottom: 40 }}>\n          <Title level={2}>您当前面临的主要挑战是？</Title>\n          <Paragraph style={{ fontSize: 16, color: '#666' }}>\n            选择您最关心的业务痛点，我们将为您推荐针对性的解决方案\n          </Paragraph>\n        </div>\n\n        <Row gutter={[24, 24]}>\n          {selectedIndustry.painPoints.map(painPoint => (\n            <Col key={painPoint.id} span={24}>\n              <Card\n                hoverable\n                className={config.selectedPainPoints.includes(painPoint.id) ? 'selected-card' : ''}\n                onClick={() => {\n                  const newPainPoints = config.selectedPainPoints.includes(painPoint.id)\n                    ? config.selectedPainPoints.filter(p => p !== painPoint.id)\n                    : [...config.selectedPainPoints, painPoint.id];\n                  setConfig(prev => ({ ...prev, selectedPainPoints: newPainPoints }));\n                }}\n                style={{\n                  border: config.selectedPainPoints.includes(painPoint.id) ? '3px solid #1890ff' : '1px solid #d9d9d9',\n                  borderRadius: 12,\n                }}\n              >\n                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>\n                  <div style={{ flex: 1 }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>\n                      <Title level={4} style={{ margin: 0 }}>{painPoint.title}</Title>\n                      <Badge \n                        count={`${painPoint.frequency}%`} \n                        style={{ backgroundColor: painPoint.impact === 'high' ? '#f5222d' : '#faad14' }}\n                      />\n                    </div>\n                    <Paragraph style={{ color: '#666', marginBottom: 16 }}>\n                      {painPoint.description}\n                    </Paragraph>\n                    \n                    {painPoint.solutions.map(solution => (\n                      <div key={solution.id} style={{ marginBottom: 8 }}>\n                        <Space>\n                          <BulbOutlined style={{ color: '#52c41a' }} />\n                          <Text strong style={{ color: '#52c41a' }}>{solution.title}</Text>\n                          <Tag color=\"green\">{solution.estimatedROI}</Tag>\n                        </Space>\n                      </div>\n                    ))}\n                  </div>\n                  \n                  <div style={{ textAlign: 'center' }}>\n                    <Progress\n                      type=\"circle\"\n                      percent={painPoint.frequency}\n                      width={60}\n                      format={() => `${painPoint.frequency}%`}\n                      strokeColor={painPoint.impact === 'high' ? '#f5222d' : '#faad14'}\n                    />\n                    <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>\n                      行业普遍性\n                    </div>\n                  </div>\n                </div>\n              </Card>\n            </Col>\n          ))}\n        </Row>\n      </div>\n    );\n  };\n\n  const renderRecommendation = () => {\n    if (!recommendation || !selectedIndustry || !selectedRole) return null;\n\n    return (\n      <div style={{ padding: '40px 0' }}>\n        <div style={{ textAlign: 'center', marginBottom: 40 }}>\n          <Title level={2}>🎉 为您量身定制的解决方案</Title>\n          <Paragraph style={{ fontSize: 16, color: '#666' }}>\n            基于您的行业特点和角色需求，我们为您推荐以下配置\n          </Paragraph>\n        </div>\n\n        <Row gutter={[24, 24]}>\n          <Col span={16}>\n            <Card title=\"推荐配置详情\" style={{ marginBottom: 24 }}>\n              <Timeline>\n                <Timeline.Item \n                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}\n                  color=\"green\"\n                >\n                  <div>\n                    <Text strong>产品选择：</Text>\n                    <Tag color=\"blue\" style={{ marginLeft: 8 }}>\n                      {recommendation.recommendedProduct === 'kis-cloud' ? '金蝶云星辰' : \n                       recommendation.recommendedProduct === 'jdy' ? '金蝶精斗云' : 'K/3 Cloud'}\n                    </Tag>\n                  </div>\n                  <div style={{ marginTop: 8, color: '#666' }}>\n                    最适合{selectedIndustry.name}{selectedRole.name}的专业解决方案\n                  </div>\n                </Timeline.Item>\n                \n                <Timeline.Item \n                  dot={<RocketOutlined style={{ color: '#1890ff' }} />}\n                  color=\"blue\"\n                >\n                  <div>\n                    <Text strong>核心功能模块：</Text>\n                  </div>\n                  <div style={{ marginTop: 8 }}>\n                    {recommendation.recommendedModules.map((module: string, index: number) => (\n                      <Tag key={index} color=\"blue\" style={{ marginBottom: 4 }}>\n                        {module}\n                      </Tag>\n                    ))}\n                  </div>\n                </Timeline.Item>\n                \n                <Timeline.Item \n                  dot={<TeamOutlined style={{ color: '#faad14' }} />}\n                  color=\"orange\"\n                >\n                  <div>\n                    <Text strong>实施服务：</Text>\n                  </div>\n                  <div style={{ marginTop: 8 }}>\n                    {recommendation.recommendedServices.map((service: string, index: number) => (\n                      <Tag key={index} color=\"orange\" style={{ marginBottom: 4 }}>\n                        {service}\n                      </Tag>\n                    ))}\n                  </div>\n                </Timeline.Item>\n              </Timeline>\n            </Card>\n\n            <Alert\n              message=\"投资回报预期\"\n              description={\n                <div>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <Text>• {recommendation.roi}</Text>\n                    <Text>• 预计提升工作效率 30-50%</Text>\n                    <Text>• 减少人工错误 80% 以上</Text>\n                    <Text>• 提升决策效率 60% 以上</Text>\n                  </Space>\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              icon={<TrophyOutlined />}\n            />\n          </Col>\n\n          <Col span={8}>\n            <Card title=\"价格预估\" style={{ marginBottom: 24 }}>\n              <div style={{ textAlign: 'center' }}>\n                <Statistic\n                  title=\"预估总价\"\n                  value={recommendation.estimatedPrice}\n                  precision={0}\n                  prefix=\"¥\"\n                  valueStyle={{ color: '#1890ff', fontSize: 32 }}\n                />\n                <div style={{ marginTop: 16, color: '#666' }}>\n                  <Space direction=\"vertical\" size=\"small\">\n                    <Text>推荐用户数：{recommendation.estimatedUsers} 人</Text>\n                    <Text>包含实施服务</Text>\n                    <Text>首年价格，续费享优惠</Text>\n                  </Space>\n                </div>\n              </div>\n            </Card>\n\n            <Card title=\"为什么选择我们？\" size=\"small\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                  <StarOutlined style={{ color: '#faad14' }} />\n                  <Text>行业领先的财务软件</Text>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                  <RiseOutlined style={{ color: '#52c41a' }} />\n                  <Text>500万+ 企业的选择</Text>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                  <ClockCircleOutlined style={{ color: '#1890ff' }} />\n                  <Text>7x24小时专业支持</Text>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                  <DollarOutlined style={{ color: '#722ed1' }} />\n                  <Text>灵活的付费方式</Text>\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        <div style={{ textAlign: 'center', marginTop: 40 }}>\n          <Space size=\"large\">\n            <Button type=\"primary\" size=\"large\" style={{ minWidth: 120 }}>\n              立即购买\n            </Button>\n            <Button size=\"large\" style={{ minWidth: 120 }}>\n              申请试用\n            </Button>\n            <Button size=\"large\" style={{ minWidth: 120 }}>\n              联系顾问\n            </Button>\n          </Space>\n        </div>\n      </div>\n    );\n  };\n\n  const steps = [\n    { title: '选择行业', content: renderIndustrySelection() },\n    { title: '确定角色', content: renderRoleSelection() },\n    { title: '识别痛点', content: renderPainPointSelection() },\n    { title: '推荐方案', content: renderRecommendation() },\n  ];\n\n  const canProceed = () => {\n    switch (config.currentStep) {\n      case 0: return config.industry !== '';\n      case 1: return config.role !== '';\n      case 2: return config.selectedPainPoints.length > 0;\n      default: return true;\n    }\n  };\n\n  return (\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>\n      <div style={{ padding: '40px 24px', maxWidth: 1200, margin: '0 auto' }}>\n        <Card style={{ borderRadius: 16, boxShadow: '0 20px 40px rgba(0,0,0,0.1)' }}>\n          <div style={{ padding: '20px 0' }}>\n            <Steps \n              current={config.currentStep} \n              style={{ marginBottom: 40 }}\n              size=\"small\"\n            >\n              {steps.map(item => (\n                <Step key={item.title} title={item.title} />\n              ))}\n            </Steps>\n\n            <div style={{ minHeight: 500 }}>\n              {steps[config.currentStep].content}\n            </div>\n\n            <div style={{ textAlign: 'center', marginTop: 40, borderTop: '1px solid #f0f0f0', paddingTop: 24 }}>\n              <Space>\n                {config.currentStep > 0 && (\n                  <Button size=\"large\" onClick={handlePrev}>\n                    上一步\n                  </Button>\n                )}\n                {config.currentStep < steps.length - 1 && (\n                  <Button \n                    type=\"primary\" \n                    size=\"large\" \n                    onClick={handleNext}\n                    disabled={!canProceed()}\n                  >\n                    下一步\n                  </Button>\n                )}\n              </Space>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      <style jsx>{`\n        .selected-card {\n          transform: translateY(-4px);\n          box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2) !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default TeslaStyleConfigurator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,EAGLC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,KAAK,EAELC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,aAAa,EAAEC,2BAA2B,QAAmC,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG1B,UAAU;AAC7C,MAAM;EAAE2B;AAAK,CAAC,GAAGzB,KAAK;AAUtB,MAAM0B,sBAAgC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAqB;IACvDsC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,OAAO;IACpBC,kBAAkB,EAAE,EAAE;IACtBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAc,IAAI,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd,IAAImC,MAAM,CAACE,QAAQ,EAAE;MACnB,MAAMA,QAAQ,GAAGZ,aAAa,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKf,MAAM,CAACE,QAAQ,CAAC;MAClEQ,mBAAmB,CAACR,QAAQ,IAAI,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACF,MAAM,CAACE,QAAQ,CAAC,CAAC;EAErBrC,SAAS,CAAC,MAAM;IACd,IAAI4C,gBAAgB,IAAIT,MAAM,CAACG,IAAI,EAAE;MACnC,MAAMA,IAAI,GAAGM,gBAAgB,CAACO,WAAW,CAACH,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKf,MAAM,CAACG,IAAI,CAAC;MACzES,eAAe,CAACT,IAAI,IAAI,IAAI,CAAC;IAC/B;EACF,CAAC,EAAE,CAACM,gBAAgB,EAAET,MAAM,CAACG,IAAI,CAAC,CAAC;EAEnCtC,SAAS,CAAC,MAAM;IACd,IAAImC,MAAM,CAACE,QAAQ,IAAIF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACK,kBAAkB,CAACa,MAAM,GAAG,CAAC,EAAE;MAC1E,IAAI;QACF,MAAMC,GAAG,GAAG5B,2BAA2B,CACrCS,MAAM,CAACE,QAAQ,EACfF,MAAM,CAACG,IAAI,EACXH,MAAM,CAACI,WAAW,EAClBJ,MAAM,CAACK,kBACT,CAAC;QACDG,iBAAiB,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF;EACF,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EAEZ,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvBrB,SAAS,CAACsB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjB,WAAW,EAAEiB,IAAI,CAACjB,WAAW,GAAG;IAAE,CAAC,CAAC,CAAC;EACrE,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBvB,SAAS,CAACsB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjB,WAAW,EAAEiB,IAAI,CAACjB,WAAW,GAAG;IAAE,CAAC,CAAC,CAAC;EACrE,CAAC;EAED,MAAMmB,uBAAuB,GAAGA,CAAA,kBAC9BhC,OAAA;IAAKiC,KAAK,EAAE;MAAEC,SAAS,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACrDpC,OAAA,CAACC,KAAK;MAACoC,KAAK,EAAE,CAAE;MAACJ,KAAK,EAAE;QAAEK,YAAY,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC3D1C,OAAA,CAACG,SAAS;MAAC8B,KAAK,EAAE;QAAEU,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEN,YAAY,EAAE;MAAG,CAAE;MAAAF,QAAA,EAAC;IAErE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAEZ1C,OAAA,CAAC1B,GAAG;MAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,OAAO,EAAC,QAAQ;MAAAV,QAAA,EACpCvC,aAAa,CAACkD,GAAG,CAACtC,QAAQ,iBACzBT,OAAA,CAACzB,GAAG;QAAmByE,IAAI,EAAE,CAAE;QAAAZ,QAAA,eAC7BpC,OAAA,CAAC3B,IAAI;UACH4E,SAAS;UACTC,SAAS,EAAE3C,MAAM,CAACE,QAAQ,KAAKA,QAAQ,CAACa,EAAE,GAAG,eAAe,GAAG,EAAG;UAClE6B,OAAO,EAAEA,CAAA,KAAM3C,SAAS,CAACsB,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErB,QAAQ,EAAEA,QAAQ,CAACa,EAAE;YAAEZ,IAAI,EAAE,EAAE;YAAEE,kBAAkB,EAAE;UAAG,CAAC,CAAC,CAAE;UACzGqB,KAAK,EAAE;YACLmB,MAAM,EAAE,GAAG;YACXC,MAAM,EAAE9C,MAAM,CAACE,QAAQ,KAAKA,QAAQ,CAACa,EAAE,GAAG,mBAAmB,GAAG,mBAAmB;YACnFgC,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAE;UACd,CAAE;UACFC,SAAS,EAAE;YAAErB,OAAO,EAAE,EAAE;YAAED,SAAS,EAAE;UAAS,CAAE;UAAAE,QAAA,gBAEhDpC,OAAA;YAAKiC,KAAK,EAAE;cAAEU,QAAQ,EAAE,EAAE;cAAEL,YAAY,EAAE;YAAG,CAAE;YAAAF,QAAA,GAC5C3B,QAAQ,CAACgD,IAAI,KAAK,SAAS,IAAI,IAAI,EACnChD,QAAQ,CAACgD,IAAI,KAAK,MAAM,IAAI,IAAI,EAChChD,QAAQ,CAACgD,IAAI,KAAK,kBAAkB,IAAI,IAAI;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN1C,OAAA,CAACC,KAAK;YAACoC,KAAK,EAAE,CAAE;YAACJ,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAAAF,QAAA,EAAE3B,QAAQ,CAACiD;UAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrE1C,OAAA,CAACG,SAAS;YAAC8B,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAED,QAAQ,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAI,CAAE;YAAAvB,QAAA,EAChE3B,QAAQ,CAACmD;UAAW;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACZ1C,OAAA;YAAKiC,KAAK,EAAE;cAAE4B,SAAS,EAAE;YAAG,CAAE;YAAAzB,QAAA,eAC5BpC,OAAA,CAACnB,GAAG;cAAC+D,KAAK,EAAC,MAAM;cAAAR,QAAA,GAAE3B,QAAQ,CAACqD,UAAU,CAACrC,MAAM,EAAC,6CAAQ;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAzBCjC,QAAQ,CAACa,EAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0BhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMqB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC/C,gBAAgB,EAAE,OAAO,IAAI;IAElC,oBACEhB,OAAA;MAAKiC,KAAK,EAAE;QAAEE,OAAO,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAChCpC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEI,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,gBACpDpC,OAAA,CAACC,KAAK;UAACoC,KAAK,EAAE,CAAE;UAAAD,QAAA,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClC1C,OAAA,CAACG,SAAS;UAAC8B,KAAK,EAAE;YAAEU,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN1C,OAAA,CAAC1B,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,EACnBpB,gBAAgB,CAACO,WAAW,CAACwB,GAAG,CAACrC,IAAI,iBACpCV,OAAA,CAACzB,GAAG;UAAeyE,IAAI,EAAE,EAAG;UAAAZ,QAAA,eAC1BpC,OAAA,CAAC3B,IAAI;YACH4E,SAAS;YACTC,SAAS,EAAE3C,MAAM,CAACG,IAAI,KAAKA,IAAI,CAACY,EAAE,GAAG,eAAe,GAAG,EAAG;YAC1D6B,OAAO,EAAEA,CAAA,KAAM3C,SAAS,CAACsB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEpB,IAAI,EAAEA,IAAI,CAACY,EAAE;cAAEV,kBAAkB,EAAE;YAAG,CAAC,CAAC,CAAE;YACvFqB,KAAK,EAAE;cACLoB,MAAM,EAAE9C,MAAM,CAACG,IAAI,KAAKA,IAAI,CAACY,EAAE,GAAG,mBAAmB,GAAG,mBAAmB;cAC3EgC,YAAY,EAAE,EAAE;cAChBF,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,eAEFpC,OAAA;cAAKiC,KAAK,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,YAAY;gBAAEC,GAAG,EAAE;cAAG,CAAE;cAAA9B,QAAA,gBACjEpC,OAAA,CAAClB,MAAM;gBAACqF,IAAI,EAAE,EAAG;gBAACV,IAAI,eAAEzD,OAAA,CAACb,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACT,KAAK,EAAE;kBAAEmC,eAAe,EAAE;gBAAU;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnF1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAEoC,IAAI,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBACtBpC,OAAA,CAACC,KAAK;kBAACoC,KAAK,EAAE,CAAE;kBAACJ,KAAK,EAAE;oBAAEK,YAAY,EAAE;kBAAE,CAAE;kBAAAF,QAAA,EAAE1B,IAAI,CAACgD;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChE1C,OAAA,CAACG,SAAS;kBAAC8B,KAAK,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEN,YAAY,EAAE;kBAAG,CAAE;kBAAAF,QAAA,EACnD1B,IAAI,CAACkD;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eAEZ1C,OAAA;kBAAKiC,KAAK,EAAE;oBAAEK,YAAY,EAAE;kBAAG,CAAE;kBAAAF,QAAA,gBAC/BpC,OAAA,CAACE,IAAI;oBAACoE,MAAM;oBAACrC,KAAK,EAAE;sBAAEW,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD1C,OAAA;oBAAKiC,KAAK,EAAE;sBAAE4B,SAAS,EAAE;oBAAE,CAAE;oBAAAzB,QAAA,EAC1B1B,IAAI,CAAC6D,gBAAgB,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzB,GAAG,CAAC,CAAC0B,IAAI,EAAEC,KAAK,kBACjD1E,OAAA,CAACnB,GAAG;sBAAaoD,KAAK,EAAE;wBAAEK,YAAY,EAAE;sBAAE,CAAE;sBAAAF,QAAA,EAAEqC;oBAAI,GAAxCC,KAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAyC,CACzD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN1C,OAAA;kBAAAoC,QAAA,gBACEpC,OAAA,CAACE,IAAI;oBAACoE,MAAM;oBAACrC,KAAK,EAAE;sBAAEW,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD1C,OAAA;oBAAKiC,KAAK,EAAE;sBAAE4B,SAAS,EAAE;oBAAE,CAAE;oBAAAzB,QAAA,EAC1B1B,IAAI,CAACoD,UAAU,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzB,GAAG,CAAC,CAAC4B,IAAI,EAAED,KAAK,kBAC3C1E,OAAA,CAACnB,GAAG;sBAAa+D,KAAK,EAAC,KAAK;sBAACX,KAAK,EAAE;wBAAEK,YAAY,EAAE;sBAAE,CAAE;sBAAAF,QAAA,EAAEuC;oBAAI,GAApDD,KAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAqD,CACrE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAtCChC,IAAI,CAACY,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMkC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC5D,gBAAgB,EAAE,OAAO,IAAI;IAElC,oBACEhB,OAAA;MAAKiC,KAAK,EAAE;QAAEE,OAAO,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAChCpC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEI,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,gBACpDpC,OAAA,CAACC,KAAK;UAACoC,KAAK,EAAE,CAAE;UAAAD,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrC1C,OAAA,CAACG,SAAS;UAAC8B,KAAK,EAAE;YAAEU,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN1C,OAAA,CAAC1B,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,EACnBpB,gBAAgB,CAAC8C,UAAU,CAACf,GAAG,CAAC8B,SAAS,iBACxC7E,OAAA,CAACzB,GAAG;UAAoByE,IAAI,EAAE,EAAG;UAAAZ,QAAA,eAC/BpC,OAAA,CAAC3B,IAAI;YACH4E,SAAS;YACTC,SAAS,EAAE3C,MAAM,CAACK,kBAAkB,CAACkE,QAAQ,CAACD,SAAS,CAACvD,EAAE,CAAC,GAAG,eAAe,GAAG,EAAG;YACnF6B,OAAO,EAAEA,CAAA,KAAM;cACb,MAAM4B,aAAa,GAAGxE,MAAM,CAACK,kBAAkB,CAACkE,QAAQ,CAACD,SAAS,CAACvD,EAAE,CAAC,GAClEf,MAAM,CAACK,kBAAkB,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,SAAS,CAACvD,EAAE,CAAC,GACzD,CAAC,GAAGf,MAAM,CAACK,kBAAkB,EAAEiE,SAAS,CAACvD,EAAE,CAAC;cAChDd,SAAS,CAACsB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAElB,kBAAkB,EAAEmE;cAAc,CAAC,CAAC,CAAC;YACrE,CAAE;YACF9C,KAAK,EAAE;cACLoB,MAAM,EAAE9C,MAAM,CAACK,kBAAkB,CAACkE,QAAQ,CAACD,SAAS,CAACvD,EAAE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB;cACpGgC,YAAY,EAAE;YAChB,CAAE;YAAAlB,QAAA,eAEFpC,OAAA;cAAKiC,KAAK,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAG,CAAE;cAAA9B,QAAA,gBAC7DpC,OAAA;gBAAKiC,KAAK,EAAE;kBAAEoC,IAAI,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBACtBpC,OAAA;kBAAKiC,KAAK,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE,EAAE;oBAAE5B,YAAY,EAAE;kBAAE,CAAE;kBAAAF,QAAA,gBAC9EpC,OAAA,CAACC,KAAK;oBAACoC,KAAK,EAAE,CAAE;oBAACJ,KAAK,EAAE;sBAAEiD,MAAM,EAAE;oBAAE,CAAE;oBAAA9C,QAAA,EAAEyC,SAAS,CAACM;kBAAK;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChE1C,OAAA,CAACd,KAAK;oBACJkG,KAAK,EAAE,GAAGP,SAAS,CAACQ,SAAS,GAAI;oBACjCpD,KAAK,EAAE;sBAAEmC,eAAe,EAAES,SAAS,CAACS,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG;oBAAU;kBAAE;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1C,OAAA,CAACG,SAAS;kBAAC8B,KAAK,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEN,YAAY,EAAE;kBAAG,CAAE;kBAAAF,QAAA,EACnDyC,SAAS,CAACjB;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EAEXmC,SAAS,CAACU,SAAS,CAACxC,GAAG,CAACyC,QAAQ,iBAC/BxF,OAAA;kBAAuBiC,KAAK,EAAE;oBAAEK,YAAY,EAAE;kBAAE,CAAE;kBAAAF,QAAA,eAChDpC,OAAA,CAACtB,KAAK;oBAAA0D,QAAA,gBACJpC,OAAA,CAACP,YAAY;sBAACwC,KAAK,EAAE;wBAAEW,KAAK,EAAE;sBAAU;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7C1C,OAAA,CAACE,IAAI;sBAACoE,MAAM;sBAACrC,KAAK,EAAE;wBAAEW,KAAK,EAAE;sBAAU,CAAE;sBAAAR,QAAA,EAAEoD,QAAQ,CAACL;oBAAK;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjE1C,OAAA,CAACnB,GAAG;sBAAC+D,KAAK,EAAC,OAAO;sBAAAR,QAAA,EAAEoD,QAAQ,CAACC;oBAAY;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC,GALA8C,QAAQ,CAAClE,EAAE;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMhB,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBAClCpC,OAAA,CAACpB,QAAQ;kBACP8G,IAAI,EAAC,QAAQ;kBACbC,OAAO,EAAEd,SAAS,CAACQ,SAAU;kBAC7BO,KAAK,EAAE,EAAG;kBACVC,MAAM,EAAEA,CAAA,KAAM,GAAGhB,SAAS,CAACQ,SAAS,GAAI;kBACxCS,WAAW,EAAEjB,SAAS,CAACS,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG;gBAAU;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACF1C,OAAA;kBAAKiC,KAAK,EAAE;oBAAE4B,SAAS,EAAE,CAAC;oBAAElB,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAR,QAAA,EAAC;gBAE3D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GApDCmC,SAAS,CAACvD,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqDjB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMqD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACjF,cAAc,IAAI,CAACE,gBAAgB,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEtE,oBACElB,OAAA;MAAKiC,KAAK,EAAE;QAAEE,OAAO,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAChCpC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEI,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,gBACpDpC,OAAA,CAACC,KAAK;UAACoC,KAAK,EAAE,CAAE;UAAAD,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvC1C,OAAA,CAACG,SAAS;UAAC8B,KAAK,EAAE;YAAEU,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN1C,OAAA,CAAC1B,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,gBACpBpC,OAAA,CAACzB,GAAG;UAACyE,IAAI,EAAE,EAAG;UAAAZ,QAAA,gBACZpC,OAAA,CAAC3B,IAAI;YAAC8G,KAAK,EAAC,sCAAQ;YAAClD,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAAAF,QAAA,eAC/CpC,OAAA,CAAChB,QAAQ;cAAAoD,QAAA,gBACPpC,OAAA,CAAChB,QAAQ,CAACgH,IAAI;gBACZC,GAAG,eAAEjG,OAAA,CAACV,mBAAmB;kBAAC2C,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1DE,KAAK,EAAC,OAAO;gBAAAR,QAAA,gBAEbpC,OAAA;kBAAAoC,QAAA,gBACEpC,OAAA,CAACE,IAAI;oBAACoE,MAAM;oBAAAlC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzB1C,OAAA,CAACnB,GAAG;oBAAC+D,KAAK,EAAC,MAAM;oBAACX,KAAK,EAAE;sBAAEiE,UAAU,EAAE;oBAAE,CAAE;oBAAA9D,QAAA,EACxCtB,cAAc,CAACqF,kBAAkB,KAAK,WAAW,GAAG,OAAO,GAC3DrF,cAAc,CAACqF,kBAAkB,KAAK,KAAK,GAAG,OAAO,GAAG;kBAAW;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1C,OAAA;kBAAKiC,KAAK,EAAE;oBAAE4B,SAAS,EAAE,CAAC;oBAAEjB,KAAK,EAAE;kBAAO,CAAE;kBAAAR,QAAA,GAAC,oBACxC,EAACpB,gBAAgB,CAAC0C,IAAI,EAAExC,YAAY,CAACwC,IAAI,EAAC,4CAC/C;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEhB1C,OAAA,CAAChB,QAAQ,CAACgH,IAAI;gBACZC,GAAG,eAAEjG,OAAA,CAACX,cAAc;kBAAC4C,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrDE,KAAK,EAAC,MAAM;gBAAAR,QAAA,gBAEZpC,OAAA;kBAAAoC,QAAA,eACEpC,OAAA,CAACE,IAAI;oBAACoE,MAAM;oBAAAlC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN1C,OAAA;kBAAKiC,KAAK,EAAE;oBAAE4B,SAAS,EAAE;kBAAE,CAAE;kBAAAzB,QAAA,EAC1BtB,cAAc,CAACsF,kBAAkB,CAACrD,GAAG,CAAC,CAACsD,MAAc,EAAE3B,KAAa,kBACnE1E,OAAA,CAACnB,GAAG;oBAAa+D,KAAK,EAAC,MAAM;oBAACX,KAAK,EAAE;sBAAEK,YAAY,EAAE;oBAAE,CAAE;oBAAAF,QAAA,EACtDiE;kBAAM,GADC3B,KAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEhB1C,OAAA,CAAChB,QAAQ,CAACgH,IAAI;gBACZC,GAAG,eAAEjG,OAAA,CAACZ,YAAY;kBAAC6C,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnDE,KAAK,EAAC,QAAQ;gBAAAR,QAAA,gBAEdpC,OAAA;kBAAAoC,QAAA,eACEpC,OAAA,CAACE,IAAI;oBAACoE,MAAM;oBAAAlC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN1C,OAAA;kBAAKiC,KAAK,EAAE;oBAAE4B,SAAS,EAAE;kBAAE,CAAE;kBAAAzB,QAAA,EAC1BtB,cAAc,CAACwF,mBAAmB,CAACvD,GAAG,CAAC,CAACwD,OAAe,EAAE7B,KAAa,kBACrE1E,OAAA,CAACnB,GAAG;oBAAa+D,KAAK,EAAC,QAAQ;oBAACX,KAAK,EAAE;sBAAEK,YAAY,EAAE;oBAAE,CAAE;oBAAAF,QAAA,EACxDmE;kBAAO,GADA7B,KAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEP1C,OAAA,CAACf,KAAK;YACJuH,OAAO,EAAC,sCAAQ;YAChB5C,WAAW,eACT5D,OAAA;cAAAoC,QAAA,eACEpC,OAAA,CAACtB,KAAK;gBAAC+H,SAAS,EAAC,UAAU;gBAACxE,KAAK,EAAE;kBAAE2D,KAAK,EAAE;gBAAO,CAAE;gBAAAxD,QAAA,gBACnDpC,OAAA,CAACE,IAAI;kBAAAkC,QAAA,GAAC,SAAE,EAACtB,cAAc,CAAC4F,GAAG;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnC1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9B1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;YACDgD,IAAI,EAAC,SAAS;YACdiB,QAAQ;YACRlD,IAAI,eAAEzD,OAAA,CAACR,cAAc;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA,CAACzB,GAAG;UAACyE,IAAI,EAAE,CAAE;UAAAZ,QAAA,gBACXpC,OAAA,CAAC3B,IAAI;YAAC8G,KAAK,EAAC,0BAAM;YAAClD,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAAAF,QAAA,eAC7CpC,OAAA;cAAKiC,KAAK,EAAE;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAE,QAAA,gBAClCpC,OAAA,CAACjB,SAAS;gBACRoG,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAE9F,cAAc,CAAC+F,cAAe;gBACrCC,SAAS,EAAE,CAAE;gBACbC,MAAM,EAAC,MAAG;gBACVC,UAAU,EAAE;kBAAEpE,KAAK,EAAE,SAAS;kBAAED,QAAQ,EAAE;gBAAG;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAE4B,SAAS,EAAE,EAAE;kBAAEjB,KAAK,EAAE;gBAAO,CAAE;gBAAAR,QAAA,eAC3CpC,OAAA,CAACtB,KAAK;kBAAC+H,SAAS,EAAC,UAAU;kBAACtC,IAAI,EAAC,OAAO;kBAAA/B,QAAA,gBACtCpC,OAAA,CAACE,IAAI;oBAAAkC,QAAA,GAAC,sCAAM,EAACtB,cAAc,CAACmG,cAAc,EAAC,SAAE;kBAAA;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpD1C,OAAA,CAACE,IAAI;oBAAAkC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnB1C,OAAA,CAACE,IAAI;oBAAAkC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP1C,OAAA,CAAC3B,IAAI;YAAC8G,KAAK,EAAC,kDAAU;YAAChB,IAAI,EAAC,OAAO;YAAA/B,QAAA,eACjCpC,OAAA,CAACtB,KAAK;cAAC+H,SAAS,EAAC,UAAU;cAACxE,KAAK,EAAE;gBAAE2D,KAAK,EAAE;cAAO,CAAE;cAAAxD,QAAA,gBACnDpC,OAAA;gBAAKiC,KAAK,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBAC5DpC,OAAA,CAACT,YAAY;kBAAC0C,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7C1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACN1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBAC5DpC,OAAA,CAACJ,YAAY;kBAACqC,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7C1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACN1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBAC5DpC,OAAA,CAACL,mBAAmB;kBAACsC,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACN1C,OAAA;gBAAKiC,KAAK,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBAC5DpC,OAAA,CAACN,cAAc;kBAACuC,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/C1C,OAAA,CAACE,IAAI;kBAAAkC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1C,OAAA;QAAKiC,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAE2B,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eACjDpC,OAAA,CAACtB,KAAK;UAACyF,IAAI,EAAC,OAAO;UAAA/B,QAAA,gBACjBpC,OAAA,CAACxB,MAAM;YAACkH,IAAI,EAAC,SAAS;YAACvB,IAAI,EAAC,OAAO;YAAClC,KAAK,EAAE;cAAEiF,QAAQ,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAE9D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA,CAACxB,MAAM;YAAC2F,IAAI,EAAC,OAAO;YAAClC,KAAK,EAAE;cAAEiF,QAAQ,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAE/C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA,CAACxB,MAAM;YAAC2F,IAAI,EAAC,OAAO;YAAClC,KAAK,EAAE;cAAEiF,QAAQ,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAE/C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMyE,KAAK,GAAG,CACZ;IAAEhC,KAAK,EAAE,MAAM;IAAEiC,OAAO,EAAEpF,uBAAuB,CAAC;EAAE,CAAC,EACrD;IAAEmD,KAAK,EAAE,MAAM;IAAEiC,OAAO,EAAErD,mBAAmB,CAAC;EAAE,CAAC,EACjD;IAAEoB,KAAK,EAAE,MAAM;IAAEiC,OAAO,EAAExC,wBAAwB,CAAC;EAAE,CAAC,EACtD;IAAEO,KAAK,EAAE,MAAM;IAAEiC,OAAO,EAAErB,oBAAoB,CAAC;EAAE,CAAC,CACnD;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ9G,MAAM,CAACM,WAAW;MACxB,KAAK,CAAC;QAAE,OAAON,MAAM,CAACE,QAAQ,KAAK,EAAE;MACrC,KAAK,CAAC;QAAE,OAAOF,MAAM,CAACG,IAAI,KAAK,EAAE;MACjC,KAAK,CAAC;QAAE,OAAOH,MAAM,CAACK,kBAAkB,CAACa,MAAM,GAAG,CAAC;MACnD;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,oBACEzB,OAAA;IAAKiC,KAAK,EAAE;MAAEqF,SAAS,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAoD,CAAE;IAAAnF,QAAA,gBAClGpC,OAAA;MAAKiC,KAAK,EAAE;QAAEE,OAAO,EAAE,WAAW;QAAEqF,QAAQ,EAAE,IAAI;QAAEtC,MAAM,EAAE;MAAS,CAAE;MAAA9C,QAAA,eACrEpC,OAAA,CAAC3B,IAAI;QAAC4D,KAAK,EAAE;UAAEqB,YAAY,EAAE,EAAE;UAAEmE,SAAS,EAAE;QAA8B,CAAE;QAAArF,QAAA,eAC1EpC,OAAA;UAAKiC,KAAK,EAAE;YAAEE,OAAO,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAChCpC,OAAA,CAACrB,KAAK;YACJ+I,OAAO,EAAEnH,MAAM,CAACM,WAAY;YAC5BoB,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAC5B6B,IAAI,EAAC,OAAO;YAAA/B,QAAA,EAEX+E,KAAK,CAACpE,GAAG,CAAC4E,IAAI,iBACb3H,OAAA,CAACI,IAAI;cAAkB+E,KAAK,EAAEwC,IAAI,CAACxC;YAAM,GAA9BwC,IAAI,CAACxC,KAAK;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsB,CAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAER1C,OAAA;YAAKiC,KAAK,EAAE;cAAEqF,SAAS,EAAE;YAAI,CAAE;YAAAlF,QAAA,EAC5B+E,KAAK,CAAC5G,MAAM,CAACM,WAAW,CAAC,CAACuG;UAAO;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eAEN1C,OAAA;YAAKiC,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAE2B,SAAS,EAAE,EAAE;cAAE+D,SAAS,EAAE,mBAAmB;cAAEC,UAAU,EAAE;YAAG,CAAE;YAAAzF,QAAA,eACjGpC,OAAA,CAACtB,KAAK;cAAA0D,QAAA,GACH7B,MAAM,CAACM,WAAW,GAAG,CAAC,iBACrBb,OAAA,CAACxB,MAAM;gBAAC2F,IAAI,EAAC,OAAO;gBAAChB,OAAO,EAAEpB,UAAW;gBAAAK,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,EACAnC,MAAM,CAACM,WAAW,GAAGsG,KAAK,CAAC1F,MAAM,GAAG,CAAC,iBACpCzB,OAAA,CAACxB,MAAM;gBACLkH,IAAI,EAAC,SAAS;gBACdvB,IAAI,EAAC,OAAO;gBACZhB,OAAO,EAAEtB,UAAW;gBACpBiG,QAAQ,EAAE,CAACT,UAAU,CAAC,CAAE;gBAAAjF,QAAA,EACzB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN1C,OAAA;MAAO+H,GAAG;MAAA3F,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpC,EAAA,CApbID,sBAAgC;AAAA2H,EAAA,GAAhC3H,sBAAgC;AAsbtC,eAAeA,sBAAsB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}