{"ast": null, "code": "import { ADD_SOURCE, ADD_TARGET, REMOVE_SOURCE, REMOVE_TARGET } from '../actions/registry.js';\nexport function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case ADD_SOURCE:\n    case ADD_TARGET:\n      return state + 1;\n    case REMOVE_SOURCE:\n    case REMOVE_TARGET:\n      return state - 1;\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "reduce", "state", "arguments", "length", "undefined", "action", "type"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/reducers/refCount.ts"], "sourcesContent": ["import {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\n\nexport type State = number\n\nexport function reduce(state: State = 0, action: Action<any>): State {\n\tswitch (action.type) {\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\t\treturn state + 1\n\t\tcase REMOVE_SOURCE:\n\t\tcase REMOVE_TARGET:\n\t\t\treturn state - 1\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"], "mappings": "AAAA,SACCA,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,aAAa,QACP,wBAAwB;AAK/B,OAAO,SAASC,MAAMA,CAAA,EAA+C;EAAA,IAA9CC,KAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,MAAmB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC3D,QAAQC,MAAM,CAACC,IAAI;IAClB,KAAKV,UAAU;IACf,KAAKC,UAAU;MACd,OAAOI,KAAK,GAAG,CAAC;IACjB,KAAKH,aAAa;IAClB,KAAKC,aAAa;MACjB,OAAOE,KAAK,GAAG,CAAC;IACjB;MACC,OAAOA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}