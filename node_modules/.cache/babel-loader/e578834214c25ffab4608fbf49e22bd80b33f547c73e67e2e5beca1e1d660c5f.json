{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { invariant } from '@react-dnd/invariant';\nimport { isObject } from '../../utils/js_utils.js';\nimport { DROP } from './types.js';\nexport function createDrop(manager) {\n  return function drop() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    verifyInvariants(monitor);\n    const targetIds = getDroppableTargets(monitor);\n    // Multiple actions are dispatched here, which is why this doesn't return an action\n    targetIds.forEach((targetId, index) => {\n      const dropResult = determineDropResult(targetId, index, registry, monitor);\n      const action = {\n        type: DROP,\n        payload: {\n          dropResult: _objectSpread({}, options, dropResult)\n        }\n      };\n      manager.dispatch(action);\n    });\n  };\n}\nfunction verifyInvariants(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call drop while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n  const target = registry.getTarget(targetId);\n  let dropResult = target ? target.drop(monitor, targetId) : undefined;\n  verifyDropResultType(dropResult);\n  if (typeof dropResult === 'undefined') {\n    dropResult = index === 0 ? {} : monitor.getDropResult();\n  }\n  return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n  invariant(typeof dropResult === 'undefined' || isObject(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n  const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n  targetIds.reverse();\n  return targetIds;\n}", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "invariant", "isObject", "DROP", "createDrop", "manager", "drop", "options", "arguments", "length", "undefined", "monitor", "getMonitor", "registry", "getRegistry", "verifyInvariants", "targetIds", "getDroppableTargets", "for<PERSON>ach", "targetId", "index", "dropResult", "determineDropResult", "action", "type", "payload", "_objectSpread", "dispatch", "isDragging", "didDrop", "target", "get<PERSON><PERSON><PERSON>", "verifyDropResultType", "getDropResult", "getTargetIds", "filter", "canDropOnTarget", "reverse"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/actions/dragDrop/drop.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tDropPayload,\n\tHandlerRegistry,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { DROP } from './types.js'\n\nexport function createDrop(manager: DragDropManager) {\n\treturn function drop(options = {}): void {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyInvariants(monitor)\n\t\tconst targetIds = getDroppableTargets(monitor)\n\n\t\t// Multiple actions are dispatched here, which is why this doesn't return an action\n\t\ttargetIds.forEach((targetId, index) => {\n\t\t\tconst dropResult = determineDropResult(targetId, index, registry, monitor)\n\t\t\tconst action: Action<DropPayload> = {\n\t\t\t\ttype: DROP,\n\t\t\t\tpayload: {\n\t\t\t\t\tdropResult: {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\t...dropResult,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t}\n\t\t\tmanager.dispatch(action)\n\t\t})\n\t}\n}\n\nfunction verifyInvariants(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call drop while not dragging.')\n\tinvariant(\n\t\t!monitor.didDrop(),\n\t\t'Cannot call drop twice during one drag operation.',\n\t)\n}\n\nfunction determineDropResult(\n\ttargetId: Identifier,\n\tindex: number,\n\tregistry: HandlerRegistry,\n\tmonitor: DragDropMonitor,\n) {\n\tconst target = registry.getTarget(targetId)\n\tlet dropResult = target ? target.drop(monitor, targetId) : undefined\n\tverifyDropResultType(dropResult)\n\tif (typeof dropResult === 'undefined') {\n\t\tdropResult = index === 0 ? {} : monitor.getDropResult()\n\t}\n\treturn dropResult\n}\n\nfunction verifyDropResultType(dropResult: any) {\n\tinvariant(\n\t\ttypeof dropResult === 'undefined' || isObject(dropResult),\n\t\t'Drop result must either be an object or undefined.',\n\t)\n}\n\nfunction getDroppableTargets(monitor: DragDropMonitor) {\n\tconst targetIds = monitor\n\t\t.getTargetIds()\n\t\t.filter(monitor.canDropOnTarget, monitor)\n\ttargetIds.reverse()\n\treturn targetIds\n}\n"], "mappings": "AAAA,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASC,SAAS,QAAQ,sBAAsB;AAUhD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,QAAQ,YAAY;AAEjC,OAAO,SAASC,UAAUA,CAACC,OAAwB,EAAE;EACpD,OAAO,SAASC,IAAIA,CAAA,EAAqB;IAAA,IAApBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAChC,MAAMG,OAAO,GAAGN,OAAO,CAACO,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGR,OAAO,CAACS,WAAW,EAAE;IACtCC,gBAAgB,CAACJ,OAAO,CAAC;IACzB,MAAMK,SAAS,GAAGC,mBAAmB,CAACN,OAAO,CAAC;IAE9C;IACAK,SAAS,CAACE,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MACtC,MAAMC,UAAU,GAAGC,mBAAmB,CAACH,QAAQ,EAAEC,KAAK,EAAEP,QAAQ,EAAEF,OAAO,CAAC;MAC1E,MAAMY,MAAM,GAAwB;QACnCC,IAAI,EAAErB,IAAI;QACVsB,OAAO,EAAE;UACRJ,UAAU,EAAEK,aAAA,KACRnB,OAAO,EACPc,UAAU;;OAGf;MACDhB,OAAO,CAACsB,QAAQ,CAACJ,MAAM,CAAC;KACxB,CAAC;GACF;;AAGF,SAASR,gBAAgBA,CAACJ,OAAwB,EAAE;EACnDV,SAAS,CAACU,OAAO,CAACiB,UAAU,EAAE,EAAE,sCAAsC,CAAC;EACvE3B,SAAS,CACR,CAACU,OAAO,CAACkB,OAAO,EAAE,EAClB,mDAAmD,CACnD;;AAGF,SAASP,mBAAmBA,CAC3BH,QAAoB,EACpBC,KAAa,EACbP,QAAyB,EACzBF,OAAwB,EACvB;EACD,MAAMmB,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACZ,QAAQ,CAAC;EAC3C,IAAIE,UAAU,GAAGS,MAAM,GAAGA,MAAM,CAACxB,IAAI,CAACK,OAAO,EAAEQ,QAAQ,CAAC,GAAGT,SAAS;EACpEsB,oBAAoB,CAACX,UAAU,CAAC;EAChC,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;IACtCA,UAAU,GAAGD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGT,OAAO,CAACsB,aAAa,EAAE;;EAExD,OAAOZ,UAAU;;AAGlB,SAASW,oBAAoBA,CAACX,UAAe,EAAE;EAC9CpB,SAAS,CACR,OAAOoB,UAAU,KAAK,WAAW,IAAInB,QAAQ,CAACmB,UAAU,CAAC,EACzD,oDAAoD,CACpD;;AAGF,SAASJ,mBAAmBA,CAACN,OAAwB,EAAE;EACtD,MAAMK,SAAS,GAAGL,OAAO,CACvBuB,YAAY,EAAE,CACdC,MAAM,CAACxB,OAAO,CAACyB,eAAe,EAAEzB,OAAO,CAAC;EAC1CK,SAAS,CAACqB,OAAO,EAAE;EACnB,OAAOrB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}