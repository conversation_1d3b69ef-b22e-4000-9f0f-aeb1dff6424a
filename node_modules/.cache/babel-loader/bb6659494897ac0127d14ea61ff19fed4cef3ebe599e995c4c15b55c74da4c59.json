{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { isObject } from '../../utils/js_utils.js';\nimport { setClientOffset } from './local/setClientOffset.js';\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js';\nconst ResetCoordinatesAction = {\n  type: INIT_COORDS,\n  payload: {\n    clientOffset: null,\n    sourceClientOffset: null\n  }\n};\nexport function createBeginDrag(manager) {\n  return function beginDrag() {\n    let sourceIds = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      publishSource: true\n    };\n    const {\n      publishSource = true,\n      clientOffset,\n      getSourceClientOffset\n    } = options;\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    // Initialize the coordinates using the client offset\n    manager.dispatch(setClientOffset(clientOffset));\n    verifyInvariants(sourceIds, monitor, registry);\n    // Get the draggable source\n    const sourceId = getDraggableSource(sourceIds, monitor);\n    if (sourceId == null) {\n      manager.dispatch(ResetCoordinatesAction);\n      return;\n    }\n    // Get the source client offset\n    let sourceClientOffset = null;\n    if (clientOffset) {\n      if (!getSourceClientOffset) {\n        throw new Error('getSourceClientOffset must be defined');\n      }\n      verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n      sourceClientOffset = getSourceClientOffset(sourceId);\n    }\n    // Initialize the full coordinates\n    manager.dispatch(setClientOffset(clientOffset, sourceClientOffset));\n    const source = registry.getSource(sourceId);\n    const item = source.beginDrag(monitor, sourceId);\n    // If source.beginDrag returns null, this is an indicator to cancel the drag\n    if (item == null) {\n      return undefined;\n    }\n    verifyItemIsObject(item);\n    registry.pinSource(sourceId);\n    const itemType = registry.getSourceType(sourceId);\n    return {\n      type: BEGIN_DRAG,\n      payload: {\n        itemType,\n        item,\n        sourceId,\n        clientOffset: clientOffset || null,\n        sourceClientOffset: sourceClientOffset || null,\n        isSourcePublic: !!publishSource\n      }\n    };\n  };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n  invariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n  sourceIds.forEach(function (sourceId) {\n    invariant(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n  });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n  invariant(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n  invariant(isObject(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n  let sourceId = null;\n  for (let i = sourceIds.length - 1; i >= 0; i--) {\n    if (monitor.canDragSource(sourceIds[i])) {\n      sourceId = sourceIds[i];\n      break;\n    }\n  }\n  return sourceId;\n}", "map": {"version": 3, "names": ["invariant", "isObject", "setClientOffset", "BEGIN_DRAG", "INIT_COORDS", "ResetCoordinatesAction", "type", "payload", "clientOffset", "sourceClientOffset", "createBeginDrag", "manager", "beginDrag", "sourceIds", "arguments", "length", "undefined", "options", "publishSource", "getSourceClientOffset", "monitor", "getMonitor", "registry", "getRegistry", "dispatch", "verifyInvariants", "sourceId", "getDraggableSource", "Error", "verifyGetSourceClientOffsetIsFunction", "source", "getSource", "item", "verifyItemIsObject", "pinSource", "itemType", "getSourceType", "isSourcePublic", "isDragging", "for<PERSON>ach", "i", "canDragSource"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/actions/dragDrop/beginDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tBeginDragOptions,\n\tBeginDragPayload,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tXYCoord,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { setClientOffset } from './local/setClientOffset.js'\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js'\n\nconst ResetCoordinatesAction = {\n\ttype: INIT_COORDS,\n\tpayload: {\n\t\tclientOffset: null,\n\t\tsourceClientOffset: null,\n\t},\n}\n\nexport function createBeginDrag(manager: DragDropManager) {\n\treturn function beginDrag(\n\t\tsourceIds: Identifier[] = [],\n\t\toptions: BeginDragOptions = {\n\t\t\tpublishSource: true,\n\t\t},\n\t): Action<BeginDragPayload> | undefined {\n\t\tconst {\n\t\t\tpublishSource = true,\n\t\t\tclientOffset,\n\t\t\tgetSourceClientOffset,\n\t\t}: BeginDragOptions = options\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\n\t\t// Initialize the coordinates using the client offset\n\t\tmanager.dispatch(setClientOffset(clientOffset))\n\n\t\tverifyInvariants(sourceIds, monitor, registry)\n\n\t\t// Get the draggable source\n\t\tconst sourceId = getDraggableSource(sourceIds, monitor)\n\t\tif (sourceId == null) {\n\t\t\tmanager.dispatch(ResetCoordinatesAction)\n\t\t\treturn\n\t\t}\n\n\t\t// Get the source client offset\n\t\tlet sourceClientOffset: XYCoord | null = null\n\t\tif (clientOffset) {\n\t\t\tif (!getSourceClientOffset) {\n\t\t\t\tthrow new Error('getSourceClientOffset must be defined')\n\t\t\t}\n\t\t\tverifyGetSourceClientOffsetIsFunction(getSourceClientOffset)\n\t\t\tsourceClientOffset = getSourceClientOffset(sourceId)\n\t\t}\n\n\t\t// Initialize the full coordinates\n\t\tmanager.dispatch(setClientOffset(clientOffset, sourceClientOffset))\n\n\t\tconst source = registry.getSource(sourceId)\n\t\tconst item = source.beginDrag(monitor, sourceId)\n\t\t// If source.beginDrag returns null, this is an indicator to cancel the drag\n\t\tif (item == null) {\n\t\t\treturn undefined\n\t\t}\n\t\tverifyItemIsObject(item)\n\t\tregistry.pinSource(sourceId)\n\n\t\tconst itemType = registry.getSourceType(sourceId)\n\t\treturn {\n\t\t\ttype: BEGIN_DRAG,\n\t\t\tpayload: {\n\t\t\t\titemType,\n\t\t\t\titem,\n\t\t\t\tsourceId,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\t\tisSourcePublic: !!publishSource,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyInvariants(\n\tsourceIds: Identifier[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.')\n\tsourceIds.forEach(function (sourceId) {\n\t\tinvariant(\n\t\t\tregistry.getSource(sourceId),\n\t\t\t'Expected sourceIds to be registered.',\n\t\t)\n\t})\n}\n\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset: any) {\n\tinvariant(\n\t\ttypeof getSourceClientOffset === 'function',\n\t\t'When clientOffset is provided, getSourceClientOffset must be a function.',\n\t)\n}\n\nfunction verifyItemIsObject(item: any) {\n\tinvariant(isObject(item), 'Item must be an object.')\n}\n\nfunction getDraggableSource(sourceIds: Identifier[], monitor: DragDropMonitor) {\n\tlet sourceId = null\n\tfor (let i = sourceIds.length - 1; i >= 0; i--) {\n\t\tif (monitor.canDragSource(sourceIds[i])) {\n\t\t\tsourceId = sourceIds[i]\n\t\t\tbreak\n\t\t}\n\t}\n\treturn sourceId\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAYhD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,UAAU,EAAEC,WAAW,QAAQ,YAAY;AAEpD,MAAMC,sBAAsB,GAAG;EAC9BC,IAAI,EAAEF,WAAW;EACjBG,OAAO,EAAE;IACRC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE;;CAErB;AAED,OAAO,SAASC,eAAeA,CAACC,OAAwB,EAAE;EACzD,OAAO,SAASC,SAASA,CAAA,EAKe;IAAA,IAJvCC,SAAuB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAA,IAC5BG,OAAyB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;MAC3BI,aAAa,EAAE;KACf;IAED,MAAM;MACLA,aAAa,GAAG,IAAI;MACpBV,YAAY;MACZW;IAAqB,CACrB,GAAqBF,OAAO;IAC7B,MAAMG,OAAO,GAAGT,OAAO,CAACU,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGX,OAAO,CAACY,WAAW,EAAE;IAEtC;IACAZ,OAAO,CAACa,QAAQ,CAACtB,eAAe,CAACM,YAAY,CAAC,CAAC;IAE/CiB,gBAAgB,CAACZ,SAAS,EAAEO,OAAO,EAAEE,QAAQ,CAAC;IAE9C;IACA,MAAMI,QAAQ,GAAGC,kBAAkB,CAACd,SAAS,EAAEO,OAAO,CAAC;IACvD,IAAIM,QAAQ,IAAI,IAAI,EAAE;MACrBf,OAAO,CAACa,QAAQ,CAACnB,sBAAsB,CAAC;MACxC;;IAGD;IACA,IAAII,kBAAkB,GAAmB,IAAI;IAC7C,IAAID,YAAY,EAAE;MACjB,IAAI,CAACW,qBAAqB,EAAE;QAC3B,MAAM,IAAIS,KAAK,CAAC,uCAAuC,CAAC;;MAEzDC,qCAAqC,CAACV,qBAAqB,CAAC;MAC5DV,kBAAkB,GAAGU,qBAAqB,CAACO,QAAQ,CAAC;;IAGrD;IACAf,OAAO,CAACa,QAAQ,CAACtB,eAAe,CAACM,YAAY,EAAEC,kBAAkB,CAAC,CAAC;IAEnE,MAAMqB,MAAM,GAAGR,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC;IAC3C,MAAMM,IAAI,GAAGF,MAAM,CAAClB,SAAS,CAACQ,OAAO,EAAEM,QAAQ,CAAC;IAChD;IACA,IAAIM,IAAI,IAAI,IAAI,EAAE;MACjB,OAAOhB,SAAS;;IAEjBiB,kBAAkB,CAACD,IAAI,CAAC;IACxBV,QAAQ,CAACY,SAAS,CAACR,QAAQ,CAAC;IAE5B,MAAMS,QAAQ,GAAGb,QAAQ,CAACc,aAAa,CAACV,QAAQ,CAAC;IACjD,OAAO;MACNpB,IAAI,EAAEH,UAAU;MAChBI,OAAO,EAAE;QACR4B,QAAQ;QACRH,IAAI;QACJN,QAAQ;QACRlB,YAAY,EAAEA,YAAY,IAAI,IAAI;QAClCC,kBAAkB,EAAEA,kBAAkB,IAAI,IAAI;QAC9C4B,cAAc,EAAE,CAAC,CAACnB;;KAEnB;GACD;;AAGF,SAASO,gBAAgBA,CACxBZ,SAAuB,EACvBO,OAAwB,EACxBE,QAAyB,EACxB;EACDtB,SAAS,CAAC,CAACoB,OAAO,CAACkB,UAAU,EAAE,EAAE,uCAAuC,CAAC;EACzEzB,SAAS,CAAC0B,OAAO,CAAC,UAAUb,QAAQ,EAAE;IACrC1B,SAAS,CACRsB,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC,EAC5B,sCAAsC,CACtC;GACD,CAAC;;AAGH,SAASG,qCAAqCA,CAACV,qBAA0B,EAAE;EAC1EnB,SAAS,CACR,OAAOmB,qBAAqB,KAAK,UAAU,EAC3C,0EAA0E,CAC1E;;AAGF,SAASc,kBAAkBA,CAACD,IAAS,EAAE;EACtChC,SAAS,CAACC,QAAQ,CAAC+B,IAAI,CAAC,EAAE,yBAAyB,CAAC;;AAGrD,SAASL,kBAAkBA,CAACd,SAAuB,EAAEO,OAAwB,EAAE;EAC9E,IAAIM,QAAQ,GAAG,IAAI;EACnB,KAAK,IAAIc,CAAC,GAAG3B,SAAS,CAACE,MAAM,GAAG,CAAC,EAAEyB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,IAAIpB,OAAO,CAACqB,aAAa,CAAC5B,SAAS,CAAC2B,CAAC,CAAC,CAAC,EAAE;MACxCd,QAAQ,GAAGb,SAAS,CAAC2B,CAAC,CAAC;MACvB;;;EAGF,OAAOd,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}