{"ast": null, "code": "// 行业角色驱动的配置数据模型\n\n// 制造业配置\nexport const manufacturingIndustry = {\n  id: 'manufacturing',\n  name: '制造业',\n  description: '生产制造企业，注重成本控制、质量管理和供应链优化',\n  icon: 'factory',\n  painPoints: [{\n    id: 'cost_control',\n    title: '成本核算复杂',\n    description: '原材料成本波动大，生产成本难以精确核算，影响定价决策',\n    impact: 'high',\n    frequency: 95,\n    solutions: [{\n      id: 'cost_accounting',\n      title: '精细化成本核算',\n      description: '实时跟踪原材料、人工、制造费用，提供多维度成本分析',\n      productModules: ['cost_accounting', 'inventory_management', 'production_management'],\n      estimatedROI: '降低成本15-25%',\n      implementationTime: '2-3个月'\n    }]\n  }, {\n    id: 'inventory_management',\n    title: '库存管理困难',\n    description: '原材料、半成品、成品库存复杂，容易出现积压或缺货',\n    impact: 'high',\n    frequency: 90,\n    solutions: [{\n      id: 'smart_inventory',\n      title: '智能库存管理',\n      description: '自动补货提醒、库存预警、ABC分类管理',\n      productModules: ['inventory_management', 'purchase_management', 'sales_management'],\n      estimatedROI: '减少库存20-30%',\n      implementationTime: '1-2个月'\n    }]\n  }, {\n    id: 'quality_control',\n    title: '质量追溯困难',\n    description: '产品质量问题难以快速定位，影响客户满意度和品牌形象',\n    impact: 'high',\n    frequency: 85,\n    solutions: [{\n      id: 'quality_traceability',\n      title: '全程质量追溯',\n      description: '从原材料到成品的全程质量跟踪和追溯',\n      productModules: ['quality_management', 'production_management', 'inventory_management'],\n      estimatedROI: '减少质量成本30-40%',\n      implementationTime: '3-4个月'\n    }]\n  }],\n  commonRoles: [{\n    id: 'production_manager',\n    name: '生产经理',\n    description: '负责生产计划、进度控制和质量管理',\n    responsibilities: ['生产计划制定', '生产进度监控', '质量控制', '成本管理'],\n    painPoints: ['生产计划不准确', '质量问题频发', '成本超支'],\n    preferredFeatures: ['生产计划', '质量管理', '成本核算', '设备管理'],\n    decisionFactors: ['功能完整性', '易用性', '集成能力']\n  }, {\n    id: 'supply_chain_manager',\n    name: '供应链经理',\n    description: '负责采购、库存和供应商管理',\n    responsibilities: ['采购管理', '库存控制', '供应商管理', '物流协调'],\n    painPoints: ['库存积压', '供应商管理混乱', '采购成本高'],\n    preferredFeatures: ['采购管理', '库存管理', '供应商管理', '成本分析'],\n    decisionFactors: ['成本效益', '供应链可视化', '数据准确性']\n  }],\n  recommendedProducts: ['jdy', 'kis-cloud'],\n  businessScenarios: [{\n    id: 'small_manufacturing',\n    name: '小型制造企业',\n    description: '50人以下的制造企业，注重成本控制和效率提升',\n    challenges: ['手工记账效率低', '库存管理混乱', '成本核算不准确'],\n    requiredFeatures: ['财务管理', '库存管理', '采购管理'],\n    optionalFeatures: ['生产管理', '质量管理', 'CRM'],\n    estimatedUsers: 10,\n    complexity: 'simple'\n  }, {\n    id: 'medium_manufacturing',\n    name: '中型制造企业',\n    description: '50-200人的制造企业，需要完整的ERP解决方案',\n    challenges: ['部门协作困难', '数据孤岛严重', '决策缺乏数据支持'],\n    requiredFeatures: ['完整ERP', '生产管理', '质量管理', '成本核算'],\n    optionalFeatures: ['BI分析', 'MES集成', '移动应用'],\n    estimatedUsers: 50,\n    complexity: 'complex'\n  }]\n};\n\n// 零售业配置\nexport const retailIndustry = {\n  id: 'retail',\n  name: '零售业',\n  description: '零售连锁企业，注重客户体验、库存周转和销售分析',\n  icon: 'shop',\n  painPoints: [{\n    id: 'customer_management',\n    title: '客户管理分散',\n    description: '客户信息分散在各个渠道，难以形成统一的客户画像',\n    impact: 'high',\n    frequency: 90,\n    solutions: [{\n      id: 'unified_crm',\n      title: '统一客户管理',\n      description: '整合线上线下客户数据，建立完整客户档案',\n      productModules: ['crm', 'sales_management', 'marketing_automation'],\n      estimatedROI: '提升客户满意度25%',\n      implementationTime: '1-2个月'\n    }]\n  }, {\n    id: 'inventory_turnover',\n    title: '库存周转慢',\n    description: '商品库存周转率低，资金占用大，影响现金流',\n    impact: 'high',\n    frequency: 85,\n    solutions: [{\n      id: 'smart_replenishment',\n      title: '智能补货系统',\n      description: '基于销售数据和季节性分析的智能补货建议',\n      productModules: ['inventory_management', 'sales_analysis', 'purchase_management'],\n      estimatedROI: '提升库存周转率30%',\n      implementationTime: '2-3个月'\n    }]\n  }],\n  commonRoles: [{\n    id: 'store_manager',\n    name: '门店经理',\n    description: '负责门店日常运营和销售管理',\n    responsibilities: ['门店运营', '销售管理', '客户服务', '库存控制'],\n    painPoints: ['销售数据不及时', '库存管理困难', '客户流失'],\n    preferredFeatures: ['销售管理', 'POS系统', '客户管理', '库存管理'],\n    decisionFactors: ['易用性', '移动支持', '实时性']\n  }],\n  recommendedProducts: ['jdy'],\n  businessScenarios: [{\n    id: 'chain_retail',\n    name: '连锁零售',\n    description: '多门店连锁经营，需要统一管理',\n    challenges: ['门店数据不统一', '库存调配困难', '促销活动难以协调'],\n    requiredFeatures: ['多门店管理', '统一库存', 'POS系统', 'CRM'],\n    optionalFeatures: ['会员管理', '促销管理', '移动应用'],\n    estimatedUsers: 30,\n    complexity: 'medium'\n  }]\n};\n\n// 服务业配置\nexport const serviceIndustry = {\n  id: 'service',\n  name: '服务业',\n  description: '专业服务企业，注重项目管理、客户关系和服务质量',\n  icon: 'customer-service',\n  painPoints: [{\n    id: 'project_management',\n    title: '项目管理混乱',\n    description: '项目进度难以跟踪，资源分配不合理，影响交付质量',\n    impact: 'high',\n    frequency: 88,\n    solutions: [{\n      id: 'project_tracking',\n      title: '项目全程跟踪',\n      description: '项目计划、进度、成本、质量的全方位管理',\n      productModules: ['project_management', 'time_tracking', 'resource_management'],\n      estimatedROI: '提升项目成功率40%',\n      implementationTime: '1-2个月'\n    }]\n  }],\n  commonRoles: [{\n    id: 'project_manager',\n    name: '项目经理',\n    description: '负责项目规划、执行和交付',\n    responsibilities: ['项目规划', '进度控制', '资源协调', '客户沟通'],\n    painPoints: ['项目进度延误', '资源冲突', '客户满意度低'],\n    preferredFeatures: ['项目管理', '时间跟踪', '客户管理', '文档管理'],\n    decisionFactors: ['项目可视化', '协作能力', '移动支持']\n  }],\n  recommendedProducts: ['jdy'],\n  businessScenarios: [{\n    id: 'consulting_firm',\n    name: '咨询公司',\n    description: '专业咨询服务，注重知识管理和客户关系',\n    challenges: ['项目经验难以沉淀', '客户关系管理不系统', '时间成本核算困难'],\n    requiredFeatures: ['项目管理', 'CRM', '时间跟踪', '知识管理'],\n    optionalFeatures: ['合同管理', '发票管理', '移动办公'],\n    estimatedUsers: 20,\n    complexity: 'medium'\n  }]\n};\n\n// 所有行业配置\nexport const allIndustries = [manufacturingIndustry, retailIndustry, serviceIndustry];\n\n// 根据行业和角色推荐配置\nexport function getRecommendedConfiguration(industryId, roleId, companySize, painPoints) {\n  const industry = allIndustries.find(i => i.id === industryId);\n  const role = industry === null || industry === void 0 ? void 0 : industry.commonRoles.find(r => r.id === roleId);\n  if (!industry || !role) {\n    throw new Error('行业或角色不存在');\n  }\n\n  // 基于公司规模推荐产品\n  let recommendedProduct = 'kis-cloud';\n  let estimatedUsers = 5;\n  if (companySize === 'medium') {\n    recommendedProduct = 'jdy';\n    estimatedUsers = 20;\n  } else if (companySize === 'large') {\n    recommendedProduct = 'k3-cloud';\n    estimatedUsers = 50;\n  }\n\n  // 基于痛点推荐模块\n  const recommendedModules = [];\n  const recommendedServices = [];\n  painPoints.forEach(painPointId => {\n    const painPoint = industry.painPoints.find(p => p.id === painPointId);\n    if (painPoint) {\n      painPoint.solutions.forEach(solution => {\n        recommendedModules.push(...solution.productModules);\n      });\n    }\n  });\n\n  // 去重\n  const uniqueModules = Array.from(new Set(recommendedModules));\n\n  // 基础实施服务\n  recommendedServices.push('basic_implementation');\n\n  // 估算价格（简化计算）\n  const basePrice = recommendedProduct === 'kis-cloud' ? 1980 : recommendedProduct === 'jdy' ? 2980 : 5000;\n  const estimatedPrice = basePrice * estimatedUsers + uniqueModules.length * 500;\n  return {\n    recommendedProduct,\n    recommendedModules: uniqueModules,\n    recommendedServices,\n    estimatedUsers,\n    estimatedPrice,\n    roi: '预计12-18个月回收投资'\n  };\n}", "map": {"version": 3, "names": ["manufacturingIndustry", "id", "name", "description", "icon", "painPoints", "title", "impact", "frequency", "solutions", "productModules", "estimatedROI", "implementationTime", "commonRoles", "responsibilities", "preferredFeatures", "decisionFactors", "recommendedProducts", "businessScenarios", "challenges", "requiredFeatures", "optionalFeatures", "estimatedUsers", "complexity", "retailIndustry", "serviceIndustry", "allIndustries", "getRecommendedConfiguration", "industryId", "roleId", "companySize", "industry", "find", "i", "role", "r", "Error", "recommendedProduct", "recommendedModules", "recommendedServices", "for<PERSON>ach", "painPointId", "painPoint", "p", "solution", "push", "uniqueModules", "Array", "from", "Set", "basePrice", "estimatedPrice", "length", "roi"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/data/industryRoleConfig.ts"], "sourcesContent": ["// 行业角色驱动的配置数据模型\nexport interface Industry {\n  id: string;\n  name: string;\n  description: string;\n  icon: string;\n  painPoints: PainPoint[];\n  commonRoles: Role[];\n  recommendedProducts: string[];\n  businessScenarios: BusinessScenario[];\n}\n\nexport interface Role {\n  id: string;\n  name: string;\n  description: string;\n  responsibilities: string[];\n  painPoints: string[];\n  preferredFeatures: string[];\n  decisionFactors: string[];\n}\n\nexport interface PainPoint {\n  id: string;\n  title: string;\n  description: string;\n  impact: 'high' | 'medium' | 'low';\n  frequency: number; // 0-100 表示在该行业中的普遍程度\n  solutions: Solution[];\n}\n\nexport interface Solution {\n  id: string;\n  title: string;\n  description: string;\n  productModules: string[];\n  estimatedROI: string;\n  implementationTime: string;\n}\n\nexport interface BusinessScenario {\n  id: string;\n  name: string;\n  description: string;\n  challenges: string[];\n  requiredFeatures: string[];\n  optionalFeatures: string[];\n  estimatedUsers: number;\n  complexity: 'simple' | 'medium' | 'complex';\n}\n\n// 制造业配置\nexport const manufacturingIndustry: Industry = {\n  id: 'manufacturing',\n  name: '制造业',\n  description: '生产制造企业，注重成本控制、质量管理和供应链优化',\n  icon: 'factory',\n  painPoints: [\n    {\n      id: 'cost_control',\n      title: '成本核算复杂',\n      description: '原材料成本波动大，生产成本难以精确核算，影响定价决策',\n      impact: 'high',\n      frequency: 95,\n      solutions: [\n        {\n          id: 'cost_accounting',\n          title: '精细化成本核算',\n          description: '实时跟踪原材料、人工、制造费用，提供多维度成本分析',\n          productModules: ['cost_accounting', 'inventory_management', 'production_management'],\n          estimatedROI: '降低成本15-25%',\n          implementationTime: '2-3个月',\n        },\n      ],\n    },\n    {\n      id: 'inventory_management',\n      title: '库存管理困难',\n      description: '原材料、半成品、成品库存复杂，容易出现积压或缺货',\n      impact: 'high',\n      frequency: 90,\n      solutions: [\n        {\n          id: 'smart_inventory',\n          title: '智能库存管理',\n          description: '自动补货提醒、库存预警、ABC分类管理',\n          productModules: ['inventory_management', 'purchase_management', 'sales_management'],\n          estimatedROI: '减少库存20-30%',\n          implementationTime: '1-2个月',\n        },\n      ],\n    },\n    {\n      id: 'quality_control',\n      title: '质量追溯困难',\n      description: '产品质量问题难以快速定位，影响客户满意度和品牌形象',\n      impact: 'high',\n      frequency: 85,\n      solutions: [\n        {\n          id: 'quality_traceability',\n          title: '全程质量追溯',\n          description: '从原材料到成品的全程质量跟踪和追溯',\n          productModules: ['quality_management', 'production_management', 'inventory_management'],\n          estimatedROI: '减少质量成本30-40%',\n          implementationTime: '3-4个月',\n        },\n      ],\n    },\n  ],\n  commonRoles: [\n    {\n      id: 'production_manager',\n      name: '生产经理',\n      description: '负责生产计划、进度控制和质量管理',\n      responsibilities: ['生产计划制定', '生产进度监控', '质量控制', '成本管理'],\n      painPoints: ['生产计划不准确', '质量问题频发', '成本超支'],\n      preferredFeatures: ['生产计划', '质量管理', '成本核算', '设备管理'],\n      decisionFactors: ['功能完整性', '易用性', '集成能力'],\n    },\n    {\n      id: 'supply_chain_manager',\n      name: '供应链经理',\n      description: '负责采购、库存和供应商管理',\n      responsibilities: ['采购管理', '库存控制', '供应商管理', '物流协调'],\n      painPoints: ['库存积压', '供应商管理混乱', '采购成本高'],\n      preferredFeatures: ['采购管理', '库存管理', '供应商管理', '成本分析'],\n      decisionFactors: ['成本效益', '供应链可视化', '数据准确性'],\n    },\n  ],\n  recommendedProducts: ['jdy', 'kis-cloud'],\n  businessScenarios: [\n    {\n      id: 'small_manufacturing',\n      name: '小型制造企业',\n      description: '50人以下的制造企业，注重成本控制和效率提升',\n      challenges: ['手工记账效率低', '库存管理混乱', '成本核算不准确'],\n      requiredFeatures: ['财务管理', '库存管理', '采购管理'],\n      optionalFeatures: ['生产管理', '质量管理', 'CRM'],\n      estimatedUsers: 10,\n      complexity: 'simple',\n    },\n    {\n      id: 'medium_manufacturing',\n      name: '中型制造企业',\n      description: '50-200人的制造企业，需要完整的ERP解决方案',\n      challenges: ['部门协作困难', '数据孤岛严重', '决策缺乏数据支持'],\n      requiredFeatures: ['完整ERP', '生产管理', '质量管理', '成本核算'],\n      optionalFeatures: ['BI分析', 'MES集成', '移动应用'],\n      estimatedUsers: 50,\n      complexity: 'complex',\n    },\n  ],\n};\n\n// 零售业配置\nexport const retailIndustry: Industry = {\n  id: 'retail',\n  name: '零售业',\n  description: '零售连锁企业，注重客户体验、库存周转和销售分析',\n  icon: 'shop',\n  painPoints: [\n    {\n      id: 'customer_management',\n      title: '客户管理分散',\n      description: '客户信息分散在各个渠道，难以形成统一的客户画像',\n      impact: 'high',\n      frequency: 90,\n      solutions: [\n        {\n          id: 'unified_crm',\n          title: '统一客户管理',\n          description: '整合线上线下客户数据，建立完整客户档案',\n          productModules: ['crm', 'sales_management', 'marketing_automation'],\n          estimatedROI: '提升客户满意度25%',\n          implementationTime: '1-2个月',\n        },\n      ],\n    },\n    {\n      id: 'inventory_turnover',\n      title: '库存周转慢',\n      description: '商品库存周转率低，资金占用大，影响现金流',\n      impact: 'high',\n      frequency: 85,\n      solutions: [\n        {\n          id: 'smart_replenishment',\n          title: '智能补货系统',\n          description: '基于销售数据和季节性分析的智能补货建议',\n          productModules: ['inventory_management', 'sales_analysis', 'purchase_management'],\n          estimatedROI: '提升库存周转率30%',\n          implementationTime: '2-3个月',\n        },\n      ],\n    },\n  ],\n  commonRoles: [\n    {\n      id: 'store_manager',\n      name: '门店经理',\n      description: '负责门店日常运营和销售管理',\n      responsibilities: ['门店运营', '销售管理', '客户服务', '库存控制'],\n      painPoints: ['销售数据不及时', '库存管理困难', '客户流失'],\n      preferredFeatures: ['销售管理', 'POS系统', '客户管理', '库存管理'],\n      decisionFactors: ['易用性', '移动支持', '实时性'],\n    },\n  ],\n  recommendedProducts: ['jdy'],\n  businessScenarios: [\n    {\n      id: 'chain_retail',\n      name: '连锁零售',\n      description: '多门店连锁经营，需要统一管理',\n      challenges: ['门店数据不统一', '库存调配困难', '促销活动难以协调'],\n      requiredFeatures: ['多门店管理', '统一库存', 'POS系统', 'CRM'],\n      optionalFeatures: ['会员管理', '促销管理', '移动应用'],\n      estimatedUsers: 30,\n      complexity: 'medium',\n    },\n  ],\n};\n\n// 服务业配置\nexport const serviceIndustry: Industry = {\n  id: 'service',\n  name: '服务业',\n  description: '专业服务企业，注重项目管理、客户关系和服务质量',\n  icon: 'customer-service',\n  painPoints: [\n    {\n      id: 'project_management',\n      title: '项目管理混乱',\n      description: '项目进度难以跟踪，资源分配不合理，影响交付质量',\n      impact: 'high',\n      frequency: 88,\n      solutions: [\n        {\n          id: 'project_tracking',\n          title: '项目全程跟踪',\n          description: '项目计划、进度、成本、质量的全方位管理',\n          productModules: ['project_management', 'time_tracking', 'resource_management'],\n          estimatedROI: '提升项目成功率40%',\n          implementationTime: '1-2个月',\n        },\n      ],\n    },\n  ],\n  commonRoles: [\n    {\n      id: 'project_manager',\n      name: '项目经理',\n      description: '负责项目规划、执行和交付',\n      responsibilities: ['项目规划', '进度控制', '资源协调', '客户沟通'],\n      painPoints: ['项目进度延误', '资源冲突', '客户满意度低'],\n      preferredFeatures: ['项目管理', '时间跟踪', '客户管理', '文档管理'],\n      decisionFactors: ['项目可视化', '协作能力', '移动支持'],\n    },\n  ],\n  recommendedProducts: ['jdy'],\n  businessScenarios: [\n    {\n      id: 'consulting_firm',\n      name: '咨询公司',\n      description: '专业咨询服务，注重知识管理和客户关系',\n      challenges: ['项目经验难以沉淀', '客户关系管理不系统', '时间成本核算困难'],\n      requiredFeatures: ['项目管理', 'CRM', '时间跟踪', '知识管理'],\n      optionalFeatures: ['合同管理', '发票管理', '移动办公'],\n      estimatedUsers: 20,\n      complexity: 'medium',\n    },\n  ],\n};\n\n// 所有行业配置\nexport const allIndustries: Industry[] = [\n  manufacturingIndustry,\n  retailIndustry,\n  serviceIndustry,\n];\n\n// 根据行业和角色推荐配置\nexport function getRecommendedConfiguration(\n  industryId: string,\n  roleId: string,\n  companySize: 'small' | 'medium' | 'large',\n  painPoints: string[]\n): {\n  recommendedProduct: string;\n  recommendedModules: string[];\n  recommendedServices: string[];\n  estimatedUsers: number;\n  estimatedPrice: number;\n  roi: string;\n} {\n  const industry = allIndustries.find(i => i.id === industryId);\n  const role = industry?.commonRoles.find(r => r.id === roleId);\n  \n  if (!industry || !role) {\n    throw new Error('行业或角色不存在');\n  }\n\n  // 基于公司规模推荐产品\n  let recommendedProduct = 'kis-cloud';\n  let estimatedUsers = 5;\n  \n  if (companySize === 'medium') {\n    recommendedProduct = 'jdy';\n    estimatedUsers = 20;\n  } else if (companySize === 'large') {\n    recommendedProduct = 'k3-cloud';\n    estimatedUsers = 50;\n  }\n\n  // 基于痛点推荐模块\n  const recommendedModules: string[] = [];\n  const recommendedServices: string[] = [];\n  \n  painPoints.forEach(painPointId => {\n    const painPoint = industry.painPoints.find(p => p.id === painPointId);\n    if (painPoint) {\n      painPoint.solutions.forEach(solution => {\n        recommendedModules.push(...solution.productModules);\n      });\n    }\n  });\n\n  // 去重\n  const uniqueModules = Array.from(new Set(recommendedModules));\n  \n  // 基础实施服务\n  recommendedServices.push('basic_implementation');\n  \n  // 估算价格（简化计算）\n  const basePrice = recommendedProduct === 'kis-cloud' ? 1980 : \n                   recommendedProduct === 'jdy' ? 2980 : 5000;\n  const estimatedPrice = basePrice * estimatedUsers + uniqueModules.length * 500;\n  \n  return {\n    recommendedProduct,\n    recommendedModules: uniqueModules,\n    recommendedServices,\n    estimatedUsers,\n    estimatedPrice,\n    roi: '预计12-18个月回收投资',\n  };\n}\n"], "mappings": "AAAA;;AAmDA;AACA,OAAO,MAAMA,qBAA+B,GAAG;EAC7CC,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,0BAA0B;EACvCC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,CACV;IACEJ,EAAE,EAAE,cAAc;IAClBK,KAAK,EAAE,QAAQ;IACfH,WAAW,EAAE,4BAA4B;IACzCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,iBAAiB;MACrBK,KAAK,EAAE,SAAS;MAChBH,WAAW,EAAE,2BAA2B;MACxCO,cAAc,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,uBAAuB,CAAC;MACpFC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,EACD;IACEX,EAAE,EAAE,sBAAsB;IAC1BK,KAAK,EAAE,QAAQ;IACfH,WAAW,EAAE,0BAA0B;IACvCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,iBAAiB;MACrBK,KAAK,EAAE,QAAQ;MACfH,WAAW,EAAE,qBAAqB;MAClCO,cAAc,EAAE,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,kBAAkB,CAAC;MACnFC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,EACD;IACEX,EAAE,EAAE,iBAAiB;IACrBK,KAAK,EAAE,QAAQ;IACfH,WAAW,EAAE,2BAA2B;IACxCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,sBAAsB;MAC1BK,KAAK,EAAE,QAAQ;MACfH,WAAW,EAAE,mBAAmB;MAChCO,cAAc,EAAE,CAAC,oBAAoB,EAAE,uBAAuB,EAAE,sBAAsB,CAAC;MACvFC,YAAY,EAAE,cAAc;MAC5BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,CACF;EACDC,WAAW,EAAE,CACX;IACEZ,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,kBAAkB;IAC/BW,gBAAgB,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;IACtDT,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;IACzCU,iBAAiB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACnDC,eAAe,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM;EAC1C,CAAC,EACD;IACEf,EAAE,EAAE,sBAAsB;IAC1BC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,eAAe;IAC5BW,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IACnDT,UAAU,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;IACxCU,iBAAiB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IACpDC,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO;EAC7C,CAAC,CACF;EACDC,mBAAmB,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC;EACzCC,iBAAiB,EAAE,CACjB;IACEjB,EAAE,EAAE,qBAAqB;IACzBC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,wBAAwB;IACrCgB,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC5CC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;IACzCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE;EACd,CAAC,EACD;IACEtB,EAAE,EAAE,sBAAsB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,2BAA2B;IACxCgB,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC5CC,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACnDC,gBAAgB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IAC3CC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE;EACd,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAMC,cAAwB,GAAG;EACtCvB,EAAE,EAAE,QAAQ;EACZC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,yBAAyB;EACtCC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,CACV;IACEJ,EAAE,EAAE,qBAAqB;IACzBK,KAAK,EAAE,QAAQ;IACfH,WAAW,EAAE,yBAAyB;IACtCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,aAAa;MACjBK,KAAK,EAAE,QAAQ;MACfH,WAAW,EAAE,qBAAqB;MAClCO,cAAc,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;MACnEC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,EACD;IACEX,EAAE,EAAE,oBAAoB;IACxBK,KAAK,EAAE,OAAO;IACdH,WAAW,EAAE,sBAAsB;IACnCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,qBAAqB;MACzBK,KAAK,EAAE,QAAQ;MACfH,WAAW,EAAE,qBAAqB;MAClCO,cAAc,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;MACjFC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,CACF;EACDC,WAAW,EAAE,CACX;IACEZ,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,eAAe;IAC5BW,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAClDT,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;IACzCU,iBAAiB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IACpDC,eAAe,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK;EACxC,CAAC,CACF;EACDC,mBAAmB,EAAE,CAAC,KAAK,CAAC;EAC5BC,iBAAiB,EAAE,CACjB;IACEjB,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,gBAAgB;IAC7BgB,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC7CC,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;IACnDC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE;EACd,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAME,eAAyB,GAAG;EACvCxB,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,yBAAyB;EACtCC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE,CACV;IACEJ,EAAE,EAAE,oBAAoB;IACxBK,KAAK,EAAE,QAAQ;IACfH,WAAW,EAAE,yBAAyB;IACtCI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,CACT;MACER,EAAE,EAAE,kBAAkB;MACtBK,KAAK,EAAE,QAAQ;MACfH,WAAW,EAAE,qBAAqB;MAClCO,cAAc,EAAE,CAAC,oBAAoB,EAAE,eAAe,EAAE,qBAAqB,CAAC;MAC9EC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE;IACtB,CAAC;EAEL,CAAC,CACF;EACDC,WAAW,EAAE,CACX;IACEZ,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,cAAc;IAC3BW,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAClDT,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;IACxCU,iBAAiB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACnDC,eAAe,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM;EAC3C,CAAC,CACF;EACDC,mBAAmB,EAAE,CAAC,KAAK,CAAC;EAC5BC,iBAAiB,EAAE,CACjB;IACEjB,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,oBAAoB;IACjCgB,UAAU,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACjDC,gBAAgB,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACjDC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE;EACd,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAMG,aAAyB,GAAG,CACvC1B,qBAAqB,EACrBwB,cAAc,EACdC,eAAe,CAChB;;AAED;AACA,OAAO,SAASE,2BAA2BA,CACzCC,UAAkB,EAClBC,MAAc,EACdC,WAAyC,EACzCzB,UAAoB,EAQpB;EACA,MAAM0B,QAAQ,GAAGL,aAAa,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAK2B,UAAU,CAAC;EAC7D,MAAMM,IAAI,GAAGH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAElB,WAAW,CAACmB,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAK4B,MAAM,CAAC;EAE7D,IAAI,CAACE,QAAQ,IAAI,CAACG,IAAI,EAAE;IACtB,MAAM,IAAIE,KAAK,CAAC,UAAU,CAAC;EAC7B;;EAEA;EACA,IAAIC,kBAAkB,GAAG,WAAW;EACpC,IAAIf,cAAc,GAAG,CAAC;EAEtB,IAAIQ,WAAW,KAAK,QAAQ,EAAE;IAC5BO,kBAAkB,GAAG,KAAK;IAC1Bf,cAAc,GAAG,EAAE;EACrB,CAAC,MAAM,IAAIQ,WAAW,KAAK,OAAO,EAAE;IAClCO,kBAAkB,GAAG,UAAU;IAC/Bf,cAAc,GAAG,EAAE;EACrB;;EAEA;EACA,MAAMgB,kBAA4B,GAAG,EAAE;EACvC,MAAMC,mBAA6B,GAAG,EAAE;EAExClC,UAAU,CAACmC,OAAO,CAACC,WAAW,IAAI;IAChC,MAAMC,SAAS,GAAGX,QAAQ,CAAC1B,UAAU,CAAC2B,IAAI,CAACW,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKwC,WAAW,CAAC;IACrE,IAAIC,SAAS,EAAE;MACbA,SAAS,CAACjC,SAAS,CAAC+B,OAAO,CAACI,QAAQ,IAAI;QACtCN,kBAAkB,CAACO,IAAI,CAAC,GAAGD,QAAQ,CAAClC,cAAc,CAAC;MACrD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,MAAMoC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACX,kBAAkB,CAAC,CAAC;;EAE7D;EACAC,mBAAmB,CAACM,IAAI,CAAC,sBAAsB,CAAC;;EAEhD;EACA,MAAMK,SAAS,GAAGb,kBAAkB,KAAK,WAAW,GAAG,IAAI,GAC1CA,kBAAkB,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI;EAC3D,MAAMc,cAAc,GAAGD,SAAS,GAAG5B,cAAc,GAAGwB,aAAa,CAACM,MAAM,GAAG,GAAG;EAE9E,OAAO;IACLf,kBAAkB;IAClBC,kBAAkB,EAAEQ,aAAa;IACjCP,mBAAmB;IACnBjB,cAAc;IACd6B,cAAc;IACdE,GAAG,EAAE;EACP,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}