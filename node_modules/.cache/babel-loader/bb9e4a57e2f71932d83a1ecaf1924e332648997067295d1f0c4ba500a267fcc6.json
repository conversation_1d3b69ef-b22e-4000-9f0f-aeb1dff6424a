{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout, Menu, Avatar, Dropdown, Badge, Button, Space, Typography } from 'antd';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, DashboardOutlined, AppstoreOutlined, SettingOutlined, ShoppingCartOutlined, DollarOutlined, FileTextOutlined, UserOutlined, BarChartOutlined, TeamOutlined, ApiOutlined, BellOutlined, LogoutOutlined, ProfileOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAppSelector, useAppDispatch } from '@/store';\nimport { logout } from '@/store/slices/authSlice';\nimport { setTheme } from '@/store/slices/appSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst AppLayout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const {\n    user\n  } = useAppSelector(state => state.auth);\n  const {\n    notifications,\n    theme\n  } = useAppSelector(state => state.app);\n  const menuItems = [{\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    label: '仪表板'\n  }, {\n    key: '/products',\n    icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this),\n    label: '产品管理',\n    children: [{\n      key: '/products/list',\n      label: '产品列表'\n    }, {\n      key: '/products/categories',\n      label: '产品分类'\n    }]\n  }, {\n    key: '/configurator',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    label: '产品配置'\n  }, {\n    key: '/bundles',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: '产品组合'\n  }, {\n    key: '/pricing',\n    icon: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this),\n    label: '价格管理'\n  }, {\n    key: '/quotes',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this),\n    label: '报价管理'\n  }, {\n    key: '/customer-portal',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this),\n    label: '客户门户'\n  }, {\n    key: '/analytics',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    label: '数据分析'\n  }, {\n    key: '/users',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this),\n    label: '用户管理'\n  }, {\n    key: '/integration',\n    icon: /*#__PURE__*/_jsxDEV(ApiOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this),\n    label: '系统集成'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n  const toggleTheme = () => {\n    dispatch(setTheme({\n      mode: theme.mode === 'light' ? 'dark' : 'light'\n    }));\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(ProfileOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this),\n    label: '个人资料',\n    onClick: () => navigate('/profile')\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 13\n    }, this),\n    label: '系统设置',\n    onClick: () => navigate('/settings')\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  const unreadNotifications = notifications.filter(n => !n.read).length;\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"app-layout\",\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      className: \"app-sider\",\n      width: 240,\n      collapsedWidth: 80,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 64,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: collapsed ? 'center' : 'flex-start',\n          padding: collapsed ? 0 : '0 24px',\n          borderBottom: '1px solid #f0f0f0'\n        },\n        children: [!collapsed && /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          style: {\n            margin: 0,\n            color: '#1890ff'\n          },\n          children: \"Link CPQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), collapsed && /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          style: {\n            margin: 0,\n            color: '#1890ff'\n          },\n          children: \"CPQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"light\",\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: handleMenuClick,\n        style: {\n          borderRight: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        className: \"app-header\",\n        style: {\n          padding: '0 24px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          background: '#fff'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 56\n          }, this),\n          onClick: () => setCollapsed(!collapsed),\n          style: {\n            fontSize: '16px',\n            width: 64,\n            height: 64\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            count: unreadNotifications,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 23\n              }, this),\n              style: {\n                fontSize: '16px'\n              },\n              onClick: () => navigate('/notifications')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            onClick: toggleTheme,\n            style: {\n              fontSize: '16px'\n            },\n            children: theme.mode === 'light' ? '🌙' : '☀️'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: user === null || user === void 0 ? void 0 : user.avatar,\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 25\n                }, this),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"app-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(AppLayout, \"OpQnko2TRGsTFdggFf+9TgA7cgA=\", false, function () {\n  return [useNavigate, useLocation, useAppDispatch, useAppSelector, useAppSelector];\n});\n_c = AppLayout;\nexport default AppLayout;\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "<PERSON><PERSON>", "Avatar", "Dropdown", "Badge", "<PERSON><PERSON>", "Space", "Typography", "MenuFoldOutlined", "MenuUnfoldOutlined", "DashboardOutlined", "AppstoreOutlined", "SettingOutlined", "ShoppingCartOutlined", "DollarOutlined", "FileTextOutlined", "UserOutlined", "BarChartOutlined", "TeamOutlined", "ApiOutlined", "BellOutlined", "LogoutOutlined", "ProfileOutlined", "useNavigate", "useLocation", "useAppSelector", "useAppDispatch", "logout", "setTheme", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "AppLayout", "children", "_s", "collapsed", "setCollapsed", "navigate", "location", "dispatch", "user", "state", "auth", "notifications", "theme", "app", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "handleLogout", "toggleTheme", "mode", "userMenuItems", "onClick", "type", "unreadNotifications", "filter", "n", "read", "length", "className", "trigger", "collapsible", "width", "collapsedWidth", "style", "height", "display", "alignItems", "justifyContent", "padding", "borderBottom", "level", "margin", "color", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "borderRight", "background", "fontSize", "size", "count", "menu", "placement", "arrow", "cursor", "src", "avatar", "firstName", "lastName", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout, Menu, Avatar, Dropdown, Badge, Button, Space, Typography } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  AppstoreOutlined,\n  SettingOutlined,\n  ShoppingCartOutlined,\n  DollarOutlined,\n  FileTextOutlined,\n  UserOutlined,\n  Bar<PERSON>hartOutlined,\n  TeamOutlined,\n  ApiOutlined,\n  BellOutlined,\n  LogoutOutlined,\n  ProfileOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAppSelector, useAppDispatch } from '@/store';\nimport { logout } from '@/store/slices/authSlice';\nimport { setTheme } from '@/store/slices/appSlice';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AppLayout: React.FC<AppLayoutProps> = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  \n  const { user } = useAppSelector((state) => state.auth);\n  const { notifications, theme } = useAppSelector((state) => state.app);\n\n  const menuItems = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表板',\n    },\n    {\n      key: '/products',\n      icon: <AppstoreOutlined />,\n      label: '产品管理',\n      children: [\n        { key: '/products/list', label: '产品列表' },\n        { key: '/products/categories', label: '产品分类' },\n      ],\n    },\n    {\n      key: '/configurator',\n      icon: <SettingOutlined />,\n      label: '产品配置',\n    },\n    {\n      key: '/bundles',\n      icon: <ShoppingCartOutlined />,\n      label: '产品组合',\n    },\n    {\n      key: '/pricing',\n      icon: <DollarOutlined />,\n      label: '价格管理',\n    },\n    {\n      key: '/quotes',\n      icon: <FileTextOutlined />,\n      label: '报价管理',\n    },\n    {\n      key: '/customer-portal',\n      icon: <UserOutlined />,\n      label: '客户门户',\n    },\n    {\n      key: '/analytics',\n      icon: <BarChartOutlined />,\n      label: '数据分析',\n    },\n    {\n      key: '/users',\n      icon: <TeamOutlined />,\n      label: '用户管理',\n    },\n    {\n      key: '/integration',\n      icon: <ApiOutlined />,\n      label: '系统集成',\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const toggleTheme = () => {\n    dispatch(setTheme({\n      mode: theme.mode === 'light' ? 'dark' : 'light',\n    }));\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <ProfileOutlined />,\n      label: '个人资料',\n      onClick: () => navigate('/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n      onClick: () => navigate('/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  const unreadNotifications = notifications.filter(n => !n.read).length;\n\n  return (\n    <Layout className=\"app-layout\">\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        className=\"app-sider\"\n        width={240}\n        collapsedWidth={80}\n      >\n        <div style={{ \n          height: 64, \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: collapsed ? 'center' : 'flex-start',\n          padding: collapsed ? 0 : '0 24px',\n          borderBottom: '1px solid #f0f0f0'\n        }}>\n          {!collapsed && (\n            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\n              Link CPQ\n            </Title>\n          )}\n          {collapsed && (\n            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\n              CPQ\n            </Title>\n          )}\n        </div>\n        \n        <Menu\n          theme=\"light\"\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          style={{ borderRight: 0 }}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header className=\"app-header\" style={{ \n          padding: '0 24px', \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'space-between',\n          background: '#fff'\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{ fontSize: '16px', width: 64, height: 64 }}\n          />\n          \n          <Space size=\"large\">\n            <Badge count={unreadNotifications} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                style={{ fontSize: '16px' }}\n                onClick={() => navigate('/notifications')}\n              />\n            </Badge>\n            \n            <Button\n              type=\"text\"\n              onClick={toggleTheme}\n              style={{ fontSize: '16px' }}\n            >\n              {theme.mode === 'light' ? '🌙' : '☀️'}\n            </Button>\n            \n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar\n                  src={user?.avatar}\n                  icon={<UserOutlined />}\n                  size=\"small\"\n                />\n                <span>{user?.firstName} {user?.lastName}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content className=\"app-content\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AppLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AACvF,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,cAAc,EAAEC,cAAc,QAAQ,SAAS;AACxD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,QAAQ,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGjC,MAAM;AACzC,MAAM;EAAEkC;AAAM,CAAC,GAAG3B,UAAU;AAM5B,MAAM4B,SAAmC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMyC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGhB,cAAc,CAAC,CAAC;EAEjC,MAAM;IAAEiB;EAAK,CAAC,GAAGlB,cAAc,CAAEmB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EACtD,MAAM;IAAEC,aAAa;IAAEC;EAAM,CAAC,GAAGtB,cAAc,CAAEmB,KAAK,IAAKA,KAAK,CAACI,GAAG,CAAC;EAErE,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAErB,OAAA,CAACpB,iBAAiB;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAErB,OAAA,CAACnB,gBAAgB;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,CACR;MAAEc,GAAG,EAAE,gBAAgB;MAAEM,KAAK,EAAE;IAAO,CAAC,EACxC;MAAEN,GAAG,EAAE,sBAAsB;MAAEM,KAAK,EAAE;IAAO,CAAC;EAElD,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAErB,OAAA,CAAClB,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAErB,OAAA,CAACjB,oBAAoB;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAErB,OAAA,CAAChB,cAAc;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAErB,OAAA,CAACf,gBAAgB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAErB,OAAA,CAACd,YAAY;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAErB,OAAA,CAACb,gBAAgB;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAErB,OAAA,CAACZ,YAAY;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAErB,OAAA,CAACX,WAAW;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAC;IAAEP;EAAqB,CAAC,KAAK;IACpDV,QAAQ,CAACU,GAAG,CAAC;EACf,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBhB,QAAQ,CAACf,MAAM,CAAC,CAAC,CAAC;IAClBa,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMmB,WAAW,GAAGA,CAAA,KAAM;IACxBjB,QAAQ,CAACd,QAAQ,CAAC;MAChBgC,IAAI,EAAEb,KAAK,CAACa,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,aAAa,GAAG,CACpB;IACEX,GAAG,EAAE,SAAS;IACdC,IAAI,eAAErB,OAAA,CAACR,eAAe;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbM,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,UAAU;EACpC,CAAC,EACD;IACEU,GAAG,EAAE,UAAU;IACfC,IAAI,eAAErB,OAAA,CAAClB,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbM,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,WAAW;EACrC,CAAC,EACD;IACEuB,IAAI,EAAE;EACR,CAAC,EACD;IACEb,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAErB,OAAA,CAACT,cAAc;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbM,OAAO,EAAEJ;EACX,CAAC,CACF;EAED,MAAMM,mBAAmB,GAAGlB,aAAa,CAACmB,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,CAAC,CAACC,MAAM;EAErE,oBACEtC,OAAA,CAAC9B,MAAM;IAACqE,SAAS,EAAC,YAAY;IAAAjC,QAAA,gBAC5BN,OAAA,CAACE,KAAK;MACJsC,OAAO,EAAE,IAAK;MACdC,WAAW;MACXjC,SAAS,EAAEA,SAAU;MACrB+B,SAAS,EAAC,WAAW;MACrBG,KAAK,EAAE,GAAI;MACXC,cAAc,EAAE,EAAG;MAAArC,QAAA,gBAEnBN,OAAA;QAAK4C,KAAK,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAExC,SAAS,GAAG,QAAQ,GAAG,YAAY;UACnDyC,OAAO,EAAEzC,SAAS,GAAG,CAAC,GAAG,QAAQ;UACjC0C,YAAY,EAAE;QAChB,CAAE;QAAA5C,QAAA,GACC,CAACE,SAAS,iBACTR,OAAA,CAACI,KAAK;UAAC+C,KAAK,EAAE,CAAE;UAACP,KAAK,EAAE;YAAEQ,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAA/C,QAAA,EAAC;QAEzD;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,EACAjB,SAAS,iBACRR,OAAA,CAACI,KAAK;UAAC+C,KAAK,EAAE,CAAE;UAACP,KAAK,EAAE;YAAEQ,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAA/C,QAAA,EAAC;QAEzD;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENzB,OAAA,CAAC7B,IAAI;QACH8C,KAAK,EAAC,OAAO;QACba,IAAI,EAAC,QAAQ;QACbwB,YAAY,EAAE,CAAC3C,QAAQ,CAAC4C,QAAQ,CAAE;QAClCC,KAAK,EAAErC,SAAU;QACjBa,OAAO,EAAEL,eAAgB;QACzBiB,KAAK,EAAE;UAAEa,WAAW,EAAE;QAAE;MAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERzB,OAAA,CAAC9B,MAAM;MAAAoC,QAAA,gBACLN,OAAA,CAACC,MAAM;QAACsC,SAAS,EAAC,YAAY;QAACK,KAAK,EAAE;UACpCK,OAAO,EAAE,QAAQ;UACjBH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BU,UAAU,EAAE;QACd,CAAE;QAAApD,QAAA,gBACAN,OAAA,CAACzB,MAAM;UACL0D,IAAI,EAAC,MAAM;UACXZ,IAAI,EAAEb,SAAS,gBAAGR,OAAA,CAACrB,kBAAkB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzB,OAAA,CAACtB,gBAAgB;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChEO,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAAC,CAACD,SAAS,CAAE;UACxCoC,KAAK,EAAE;YAAEe,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,EAAE;YAAEG,MAAM,EAAE;UAAG;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAEFzB,OAAA,CAACxB,KAAK;UAACoF,IAAI,EAAC,OAAO;UAAAtD,QAAA,gBACjBN,OAAA,CAAC1B,KAAK;YAACuF,KAAK,EAAE3B,mBAAoB;YAAC0B,IAAI,EAAC,OAAO;YAAAtD,QAAA,eAC7CN,OAAA,CAACzB,MAAM;cACL0D,IAAI,EAAC,MAAM;cACXZ,IAAI,eAAErB,OAAA,CAACV,YAAY;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBmB,KAAK,EAAE;gBAAEe,QAAQ,EAAE;cAAO,CAAE;cAC5B3B,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,gBAAgB;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERzB,OAAA,CAACzB,MAAM;YACL0D,IAAI,EAAC,MAAM;YACXD,OAAO,EAAEH,WAAY;YACrBe,KAAK,EAAE;cAAEe,QAAQ,EAAE;YAAO,CAAE;YAAArD,QAAA,EAE3BW,KAAK,CAACa,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG;UAAI;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eAETzB,OAAA,CAAC3B,QAAQ;YACPyF,IAAI,EAAE;cAAEN,KAAK,EAAEzB;YAAc,CAAE;YAC/BgC,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAA1D,QAAA,eAELN,OAAA,CAACxB,KAAK;cAACoE,KAAK,EAAE;gBAAEqB,MAAM,EAAE;cAAU,CAAE;cAAA3D,QAAA,gBAClCN,OAAA,CAAC5B,MAAM;gBACL8F,GAAG,EAAErD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,MAAO;gBAClB9C,IAAI,eAAErB,OAAA,CAACd,YAAY;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBmC,IAAI,EAAC;cAAO;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACFzB,OAAA;gBAAAM,QAAA,GAAOO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,SAAS,EAAC,GAAC,EAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,QAAQ;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETzB,OAAA,CAACG,OAAO;QAACoC,SAAS,EAAC,aAAa;QAAAjC,QAAA,EAC7BA;MAAQ;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAAClB,EAAA,CA3MIF,SAAmC;EAAA,QAEtBZ,WAAW,EACXC,WAAW,EACXE,cAAc,EAEdD,cAAc,EACEA,cAAc;AAAA;AAAA2E,EAAA,GAP3CjE,SAAmC;AA6MzC,eAAeA,SAAS;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}