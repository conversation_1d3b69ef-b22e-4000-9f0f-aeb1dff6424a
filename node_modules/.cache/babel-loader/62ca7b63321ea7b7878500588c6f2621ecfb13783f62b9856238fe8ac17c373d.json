{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { END_DRAG } from './types.js';\nexport function createEndDrag(manager) {\n  return function endDrag() {\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    verifyIsDragging(monitor);\n    const sourceId = monitor.getSourceId();\n    if (sourceId != null) {\n      const source = registry.getSource(sourceId, true);\n      source.endDrag(monitor, sourceId);\n      registry.unpinSource();\n    }\n    return {\n      type: END_DRAG\n    };\n  };\n}\nfunction verifyIsDragging(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}", "map": {"version": 3, "names": ["invariant", "END_DRAG", "createEndDrag", "manager", "endDrag", "monitor", "getMonitor", "registry", "getRegistry", "verifyIsDragging", "sourceId", "getSourceId", "source", "getSource", "unpinSource", "type", "isDragging"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/actions/dragDrop/endDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tDragDropManager,\n\tDragDropMonitor,\n\tSentinelAction,\n} from '../../interfaces.js'\nimport { END_DRAG } from './types.js'\n\nexport function createEndDrag(manager: DragDropManager) {\n\treturn function endDrag(): SentinelAction {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyIsDragging(monitor)\n\n\t\tconst sourceId = monitor.getSourceId()\n\t\tif (sourceId != null) {\n\t\t\tconst source = registry.getSource(sourceId, true)\n\t\t\tsource.endDrag(monitor, sourceId)\n\t\t\tregistry.unpinSource()\n\t\t}\n\t\treturn { type: END_DRAG }\n\t}\n}\n\nfunction verifyIsDragging(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call endDrag while not dragging.')\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAOhD,SAASC,QAAQ,QAAQ,YAAY;AAErC,OAAO,SAASC,aAAaA,CAACC,OAAwB,EAAE;EACvD,OAAO,SAASC,OAAOA,CAAA,EAAmB;IACzC,MAAMC,OAAO,GAAGF,OAAO,CAACG,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,WAAW,EAAE;IACtCC,gBAAgB,CAACJ,OAAO,CAAC;IAEzB,MAAMK,QAAQ,GAAGL,OAAO,CAACM,WAAW,EAAE;IACtC,IAAID,QAAQ,IAAI,IAAI,EAAE;MACrB,MAAME,MAAM,GAAGL,QAAQ,CAACM,SAAS,CAACH,QAAQ,EAAE,IAAI,CAAC;MACjDE,MAAM,CAACR,OAAO,CAACC,OAAO,EAAEK,QAAQ,CAAC;MACjCH,QAAQ,CAACO,WAAW,EAAE;;IAEvB,OAAO;MAAEC,IAAI,EAAEd;KAAU;GACzB;;AAGF,SAASQ,gBAAgBA,CAACJ,OAAwB,EAAE;EACnDL,SAAS,CAACK,OAAO,CAACW,UAAU,EAAE,EAAE,yCAAyC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}