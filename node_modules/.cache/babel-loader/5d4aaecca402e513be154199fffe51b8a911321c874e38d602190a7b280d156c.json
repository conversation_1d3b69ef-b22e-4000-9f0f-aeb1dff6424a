{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Row, Col, Button, Tag, Typography, Space, Divider, Tabs, List, Modal, Form, Select, InputNumber, Alert, Statistic } from 'antd';\nimport { StarOutlined, CloudOutlined, TeamOutlined, SettingOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { mockProducts } from '@/data/mockData';\nimport { calculateKingsoftPrice } from '@/data/kingsoftPricingRules';\nimport { allKingsoftConfigurations, recommendConfiguration } from '@/data/kingsoftConfigurations';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Option\n} = Select;\nconst PriceCalculator = ({\n  productSku,\n  basePrice\n}) => {\n  _s();\n  const [quantity, setQuantity] = useState(1);\n  const [customerTier, setCustomerTier] = useState('standard');\n  const [isRenewal, setIsRenewal] = useState(false);\n  const [industry, setIndustry] = useState();\n  const priceResult = calculateKingsoftPrice(basePrice, productSku, quantity, customerTier, isRenewal, undefined, industry);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u4EF7\\u683C\\u8BA1\\u7B97\\u5668\",\n    size: \"small\",\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u8D2D\\u4E70\\u6570\\u91CF\",\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              value: quantity,\n              onChange: value => setQuantity(value || 1),\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u5BA2\\u6237\\u7C7B\\u578B\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: customerTier,\n              onChange: setCustomerTier,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"standard\",\n                children: \"\\u6807\\u51C6\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"gold\",\n                children: \"\\u91D1\\u724C\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"platinum\",\n                children: \"\\u767D\\u91D1\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"enterprise\",\n                children: \"\\u4F01\\u4E1A\\u5BA2\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u662F\\u5426\\u7EED\\u8D39\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: isRenewal,\n              onChange: setIsRenewal,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: false,\n                children: \"\\u65B0\\u8D2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: true,\n                children: \"\\u7EED\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u884C\\u4E1A\\u7C7B\\u578B\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: industry,\n              onChange: setIndustry,\n              allowClear: true,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"manufacturing\",\n                children: \"\\u5236\\u9020\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"retail\",\n                children: \"\\u96F6\\u552E\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"service\",\n                children: \"\\u670D\\u52A1\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"technology\",\n                children: \"\\u79D1\\u6280\\u4E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u539F\\u4EF7\",\n          value: priceResult.originalPrice,\n          precision: 2,\n          prefix: \"\\xA5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u4F18\\u60E0\\u91D1\\u989D\",\n          value: priceResult.totalDiscount,\n          precision: 2,\n          prefix: \"\\xA5\",\n          valueStyle: {\n            color: '#cf1322'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u6700\\u7EC8\\u4EF7\\u683C\",\n          value: priceResult.finalPrice,\n          precision: 2,\n          prefix: \"\\xA5\",\n          valueStyle: {\n            color: '#3f8600'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), priceResult.appliedRules.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u5DF2\\u5E94\\u7528\\u7684\\u4F18\\u60E0\\u89C4\\u5219\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        size: \"small\",\n        dataSource: priceResult.appliedRules,\n        renderItem: rule => /*#__PURE__*/_jsxDEV(List.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: rule.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: rule.discount.type === 'percentage' ? `${rule.discount.value}%折扣` : `¥${rule.discount.value}优惠`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(PriceCalculator, \"mCyjl3Ke7J8/LFNKf5v19mYPPSk=\");\n_c = PriceCalculator;\nconst KingsoftProductShowcase = () => {\n  _s2();\n  var _currentProduct$speci, _currentProduct$tags;\n  const [selectedProduct, setSelectedProduct] = useState('1');\n  const [configModalVisible, setConfigModalVisible] = useState(false);\n  const kingsoftProducts = mockProducts.filter(p => {\n    var _p$sku, _p$sku2, _p$sku3;\n    return ((_p$sku = p.sku) === null || _p$sku === void 0 ? void 0 : _p$sku.includes('KIS')) || ((_p$sku2 = p.sku) === null || _p$sku2 === void 0 ? void 0 : _p$sku2.includes('JDY')) || ((_p$sku3 = p.sku) === null || _p$sku3 === void 0 ? void 0 : _p$sku3.includes('K3'));\n  });\n  const currentProduct = kingsoftProducts.find(p => p.id === selectedProduct);\n  const productConfigurations = allKingsoftConfigurations.filter(config => config.productId === selectedProduct);\n  const handleConfigureProduct = () => {\n    setConfigModalVisible(true);\n  };\n  const handleRecommendation = () => {\n    const recommendations = recommendConfiguration('小微企业', 'technology', 10000, ['财务管理', '进销存']);\n    Modal.info({\n      title: '推荐配置',\n      width: 600,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n          children: \"\\u6839\\u636E\\u60A8\\u7684\\u9700\\u6C42\\uFF0C\\u6211\\u4EEC\\u4E3A\\u60A8\\u63A8\\u8350\\u4EE5\\u4E0B\\u914D\\u7F6E\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), recommendations.map(config => /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          style: {\n            marginBottom: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: config.configurationName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: config.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u4EF7\\u683C: \\xA5\", config.basePrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, config.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u91D1\\u8776\\u4EA7\\u54C1\\u7CFB\\u5217\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: kingsoftProducts,\n            renderItem: product => {\n              var _product$tags;\n              return /*#__PURE__*/_jsxDEV(List.Item, {\n                style: {\n                  cursor: 'pointer',\n                  backgroundColor: selectedProduct === product.id ? '#f0f0f0' : 'transparent',\n                  padding: '8px',\n                  borderRadius: '4px'\n                },\n                onClick: () => setSelectedProduct(product.id),\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  title: product.name,\n                  description: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\xA5\", product.basePrice]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 25\n                    }, this), (_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n                      children: tag\n                    }, tag, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 18,\n        children: currentProduct && /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CloudOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this), currentProduct.name, /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: currentProduct.sku\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 48\n              }, this),\n              onClick: handleConfigureProduct,\n              children: \"\\u4EA7\\u54C1\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 33\n              }, this),\n              onClick: handleRecommendation,\n              children: \"\\u667A\\u80FD\\u63A8\\u8350\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 16,\n              children: /*#__PURE__*/_jsxDEV(Tabs, {\n                defaultActiveKey: \"overview\",\n                items: [{\n                  key: 'overview',\n                  label: '产品概述',\n                  children: /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                      children: currentProduct.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Title, {\n                      level: 5,\n                      children: \"\\u4EA7\\u54C1\\u7279\\u8272\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      gutter: 16,\n                      children: (_currentProduct$speci = currentProduct.specifications) === null || _currentProduct$speci === void 0 ? void 0 : _currentProduct$speci.map((spec, index) => /*#__PURE__*/_jsxDEV(Col, {\n                        span: 12,\n                        style: {\n                          marginBottom: 16\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Card, {\n                          size: \"small\",\n                          children: /*#__PURE__*/_jsxDEV(Space, {\n                            children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 280,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(Text, {\n                                strong: true,\n                                children: spec.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 282,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 283,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                                type: \"secondary\",\n                                children: spec.value\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 284,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 281,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 279,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 278,\n                          columnNumber: 35\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Title, {\n                      level: 5,\n                      children: \"\\u4EA7\\u54C1\\u6807\\u7B7E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Space, {\n                      wrap: true,\n                      children: (_currentProduct$tags = currentProduct.tags) === null || _currentProduct$tags === void 0 ? void 0 : _currentProduct$tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"blue\",\n                        children: tag\n                      }, tag, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)\n                }, {\n                  key: 'configurations',\n                  label: '配置方案',\n                  children: /*#__PURE__*/_jsxDEV(Row, {\n                    gutter: 16,\n                    children: productConfigurations.map(config => /*#__PURE__*/_jsxDEV(Col, {\n                      span: 12,\n                      style: {\n                        marginBottom: 16\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        size: \"small\",\n                        title: config.configurationName,\n                        extra: /*#__PURE__*/_jsxDEV(Text, {\n                          strong: true,\n                          children: [\"\\xA5\", config.basePrice]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 311,\n                          columnNumber: 42\n                        }, this),\n                        children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                          ellipsis: {\n                            rows: 2\n                          },\n                          children: config.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 313,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Space, {\n                          wrap: true,\n                          children: config.recommendedFor.map(rec => /*#__PURE__*/_jsxDEV(Tag, {\n                            children: rec\n                          }, rec, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 319,\n                            columnNumber: 39\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 317,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 323,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(List, {\n                          size: \"small\",\n                          dataSource: config.features.slice(0, 4),\n                          renderItem: feature => /*#__PURE__*/_jsxDEV(List.Item, {\n                            style: {\n                              padding: '2px 0'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Space, {\n                              children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                                style: {\n                                  color: '#52c41a'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 331,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                                style: {\n                                  fontSize: '12px'\n                                },\n                                children: feature\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 332,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 330,\n                              columnNumber: 41\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 329,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 325,\n                          columnNumber: 35\n                        }, this), config.features.length > 4 && /*#__PURE__*/_jsxDEV(Text, {\n                          type: \"secondary\",\n                          style: {\n                            fontSize: '12px'\n                          },\n                          children: [\"\\u8FD8\\u6709 \", config.features.length - 4, \" \\u9879\\u529F\\u80FD...\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 339,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 33\n                      }, this)\n                    }, config.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 27\n                  }, this)\n                }, {\n                  key: 'support',\n                  label: '技术支持',\n                  children: /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Alert, {\n                      message: \"\\u4E13\\u4E1A\\u6280\\u672F\\u652F\\u6301\",\n                      description: \"\\u91D1\\u8776\\u63D0\\u4F9B\\u5168\\u65B9\\u4F4D\\u7684\\u6280\\u672F\\u652F\\u6301\\u670D\\u52A1\\uFF0C\\u786E\\u4FDD\\u60A8\\u7684\\u4E1A\\u52A1\\u987A\\u5229\\u8FD0\\u884C\",\n                      type: \"info\",\n                      showIcon: true,\n                      style: {\n                        marginBottom: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      gutter: 16,\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Card, {\n                          size: \"small\",\n                          title: \"\\u5728\\u7EBF\\u652F\\u6301\",\n                          children: /*#__PURE__*/_jsxDEV(Space, {\n                            direction: \"vertical\",\n                            children: [/*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u5DE5\\u4F5C\\u65E5 9:00-18:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 366,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u5728\\u7EBF\\u5BA2\\u670D\\u54CD\\u5E94\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 367,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u8FDC\\u7A0B\\u534F\\u52A9\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 368,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 365,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Card, {\n                          size: \"small\",\n                          title: \"\\u7535\\u8BDD\\u652F\\u6301\",\n                          children: /*#__PURE__*/_jsxDEV(Space, {\n                            direction: \"vertical\",\n                            children: [/*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 400\\u5BA2\\u670D\\u70ED\\u7EBF\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 375,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u6280\\u672F\\u4E13\\u5BB6\\u652F\\u6301\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 376,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u7D27\\u6025\\u95EE\\u9898\\u5904\\u7406\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 377,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 374,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 373,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Card, {\n                          size: \"small\",\n                          title: \"\\u57F9\\u8BAD\\u670D\\u52A1\",\n                          children: /*#__PURE__*/_jsxDEV(Space, {\n                            direction: \"vertical\",\n                            children: [/*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u4EA7\\u54C1\\u4F7F\\u7528\\u57F9\\u8BAD\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 384,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u5728\\u7EBF\\u89C6\\u9891\\u6559\\u7A0B\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 385,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Text, {\n                              children: \"\\u2022 \\u73B0\\u573A\\u5B9E\\u65BD\\u6307\\u5BFC\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 386,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 383,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 382,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(PriceCalculator, {\n                productSku: currentProduct.sku || '',\n                basePrice: currentProduct.basePrice\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EA7\\u54C1\\u914D\\u7F6E\\u5411\\u5BFC\",\n      visible: configModalVisible,\n      onCancel: () => setConfigModalVisible(false),\n      width: 800,\n      footer: null,\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u914D\\u7F6E\\u5411\\u5BFC\",\n        description: \"\\u8BF7\\u6839\\u636E\\u60A8\\u7684\\u4E1A\\u52A1\\u9700\\u6C42\\u9009\\u62E9\\u5408\\u9002\\u7684\\u4EA7\\u54C1\\u914D\\u7F6E\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: \"\\u914D\\u7F6E\\u5411\\u5BFC\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n};\n_s2(KingsoftProductShowcase, \"QLMPAF6/gWXQMEgor7dPkMD2mt4=\");\n_c2 = KingsoftProductShowcase;\nexport default KingsoftProductShowcase;\nvar _c, _c2;\n$RefreshReg$(_c, \"PriceCalculator\");\n$RefreshReg$(_c2, \"KingsoftProductShowcase\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Row", "Col", "<PERSON><PERSON>", "Tag", "Typography", "Space", "Divider", "Tabs", "List", "Modal", "Form", "Select", "InputNumber", "<PERSON><PERSON>", "Statistic", "StarOutlined", "CloudOutlined", "TeamOutlined", "SettingOutlined", "CheckCircleOutlined", "mockProducts", "calculateKingsoftPrice", "allKingsoftConfigurations", "recommendConfiguration", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "Option", "PriceCalculator", "productSku", "basePrice", "_s", "quantity", "setQuantity", "customerTier", "setCustomerTier", "isRenewal", "set<PERSON>s<PERSON>enewal", "industry", "setIndustry", "priceResult", "undefined", "title", "size", "children", "layout", "gutter", "span", "<PERSON><PERSON>", "label", "min", "value", "onChange", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "allowClear", "originalPrice", "precision", "prefix", "totalDiscount", "valueStyle", "color", "finalPrice", "appliedRules", "length", "strong", "dataSource", "renderItem", "rule", "name", "discount", "type", "_c", "KingsoftProductShowcase", "_s2", "_currentProduct$speci", "_currentProduct$tags", "selectedProduct", "setSelectedProduct", "configModalVisible", "setConfigModalVisible", "kingsoftProducts", "filter", "p", "_p$sku", "_p$sku2", "_p$sku3", "sku", "includes", "currentProduct", "find", "id", "productConfigurations", "config", "productId", "handleConfigureProduct", "handleRecommendation", "recommendations", "info", "content", "map", "marginBottom", "level", "configurationName", "description", "padding", "product", "_product$tags", "cursor", "backgroundColor", "borderRadius", "onClick", "Meta", "tags", "tag", "extra", "icon", "defaultActiveKey", "items", "key", "specifications", "spec", "index", "wrap", "ellipsis", "rows", "recommendedFor", "rec", "features", "slice", "feature", "fontSize", "message", "showIcon", "direction", "visible", "onCancel", "footer", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/src/components/KingsoftProductShowcase.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Tag,\n  Typography,\n  Space,\n  Divider,\n  Tabs,\n  List,\n  Badge,\n  Tooltip,\n  Modal,\n  Form,\n  Select,\n  InputNumber,\n  Alert,\n  Statistic,\n} from 'antd';\nimport {\n  ShoppingCartOutlined,\n  StarOutlined,\n  CloudOutlined,\n  SafetyOutlined,\n  TeamOutlined,\n  SettingOutlined,\n  CheckCircleOutlined,\n  InfoCircleOutlined,\n} from '@ant-design/icons';\nimport { mockProducts } from '@/data/mockData';\nimport { calculateKingsoftPrice } from '@/data/kingsoftPricingRules';\nimport { allKingsoftConfigurations, recommendConfiguration } from '@/data/kingsoftConfigurations';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\ninterface PriceCalculatorProps {\n  productSku: string;\n  basePrice: number;\n}\n\nconst PriceCalculator: React.FC<PriceCalculatorProps> = ({ productSku, basePrice }) => {\n  const [quantity, setQuantity] = useState(1);\n  const [customerTier, setCustomerTier] = useState('standard');\n  const [isRenewal, setIsRenewal] = useState(false);\n  const [industry, setIndustry] = useState<string>();\n\n  const priceResult = calculateKingsoftPrice(\n    basePrice,\n    productSku,\n    quantity,\n    customerTier,\n    isRenewal,\n    undefined,\n    industry\n  );\n\n  return (\n    <Card title=\"价格计算器\" size=\"small\">\n      <Form layout=\"vertical\">\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item label=\"购买数量\">\n              <InputNumber\n                min={1}\n                value={quantity}\n                onChange={(value) => setQuantity(value || 1)}\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item label=\"客户类型\">\n              <Select value={customerTier} onChange={setCustomerTier}>\n                <Option value=\"standard\">标准客户</Option>\n                <Option value=\"gold\">金牌客户</Option>\n                <Option value=\"platinum\">白金客户</Option>\n                <Option value=\"enterprise\">企业客户</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n        <Row gutter={16}>\n          <Col span={12}>\n            <Form.Item label=\"是否续费\">\n              <Select value={isRenewal} onChange={setIsRenewal}>\n                <Option value={false}>新购</Option>\n                <Option value={true}>续费</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item label=\"行业类型\">\n              <Select value={industry} onChange={setIndustry} allowClear>\n                <Option value=\"manufacturing\">制造业</Option>\n                <Option value=\"retail\">零售业</Option>\n                <Option value=\"service\">服务业</Option>\n                <Option value=\"technology\">科技业</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      </Form>\n\n      <Divider />\n\n      <Row gutter={16}>\n        <Col span={8}>\n          <Statistic\n            title=\"原价\"\n            value={priceResult.originalPrice}\n            precision={2}\n            prefix=\"¥\"\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"优惠金额\"\n            value={priceResult.totalDiscount}\n            precision={2}\n            prefix=\"¥\"\n            valueStyle={{ color: '#cf1322' }}\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"最终价格\"\n            value={priceResult.finalPrice}\n            precision={2}\n            prefix=\"¥\"\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Col>\n      </Row>\n\n      {priceResult.appliedRules.length > 0 && (\n        <>\n          <Divider />\n          <Text strong>已应用的优惠规则：</Text>\n          <List\n            size=\"small\"\n            dataSource={priceResult.appliedRules}\n            renderItem={(rule) => (\n              <List.Item>\n                <Space>\n                  <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                  <Text>{rule.name}</Text>\n                  <Tag color=\"green\">\n                    {rule.discount.type === 'percentage' ? `${rule.discount.value}%折扣` : `¥${rule.discount.value}优惠`}\n                  </Tag>\n                </Space>\n              </List.Item>\n            )}\n          />\n        </>\n      )}\n    </Card>\n  );\n};\n\nconst KingsoftProductShowcase: React.FC = () => {\n  const [selectedProduct, setSelectedProduct] = useState<string>('1');\n  const [configModalVisible, setConfigModalVisible] = useState(false);\n\n  const kingsoftProducts = mockProducts.filter(p => \n    p.sku?.includes('KIS') || p.sku?.includes('JDY') || p.sku?.includes('K3')\n  );\n\n  const currentProduct = kingsoftProducts.find(p => p.id === selectedProduct);\n  const productConfigurations = allKingsoftConfigurations.filter(\n    config => config.productId === selectedProduct\n  );\n\n  const handleConfigureProduct = () => {\n    setConfigModalVisible(true);\n  };\n\n  const handleRecommendation = () => {\n    const recommendations = recommendConfiguration(\n      '小微企业',\n      'technology',\n      10000,\n      ['财务管理', '进销存']\n    );\n    \n    Modal.info({\n      title: '推荐配置',\n      width: 600,\n      content: (\n        <div>\n          <Paragraph>根据您的需求，我们为您推荐以下配置：</Paragraph>\n          {recommendations.map(config => (\n            <Card key={config.id} size=\"small\" style={{ marginBottom: 8 }}>\n              <Title level={5}>{config.configurationName}</Title>\n              <Text>{config.description}</Text>\n              <br />\n              <Text strong>价格: ¥{config.basePrice}</Text>\n            </Card>\n          ))}\n        </div>\n      ),\n    });\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Row gutter={24}>\n        <Col span={6}>\n          <Card title=\"金蝶产品系列\" size=\"small\">\n            <List\n              dataSource={kingsoftProducts}\n              renderItem={(product) => (\n                <List.Item\n                  style={{\n                    cursor: 'pointer',\n                    backgroundColor: selectedProduct === product.id ? '#f0f0f0' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                  }}\n                  onClick={() => setSelectedProduct(product.id)}\n                >\n                  <List.Item.Meta\n                    title={product.name}\n                    description={\n                      <Space>\n                        <Text type=\"secondary\">¥{product.basePrice}</Text>\n                        {product.tags?.map(tag => (\n                          <Tag key={tag}>{tag}</Tag>\n                        ))}\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        <Col span={18}>\n          {currentProduct && (\n            <Card\n              title={\n                <Space>\n                  <CloudOutlined />\n                  {currentProduct.name}\n                  <Tag color=\"blue\">{currentProduct.sku}</Tag>\n                </Space>\n              }\n              extra={\n                <Space>\n                  <Button type=\"primary\" icon={<SettingOutlined />} onClick={handleConfigureProduct}>\n                    产品配置\n                  </Button>\n                  <Button icon={<StarOutlined />} onClick={handleRecommendation}>\n                    智能推荐\n                  </Button>\n                </Space>\n              }\n            >\n              <Row gutter={24}>\n                <Col span={16}>\n                  <Tabs\n                    defaultActiveKey=\"overview\"\n                    items={[\n                      {\n                        key: 'overview',\n                        label: '产品概述',\n                        children: (\n                          <>\n                            <Paragraph>{currentProduct.description}</Paragraph>\n\n                            <Title level={5}>产品特色</Title>\n                            <Row gutter={16}>\n                              {currentProduct.specifications?.map((spec, index) => (\n                                <Col span={12} key={index} style={{ marginBottom: 16 }}>\n                                  <Card size=\"small\">\n                                    <Space>\n                                      <TeamOutlined />\n                                      <div>\n                                        <Text strong>{spec.name}</Text>\n                                        <br />\n                                        <Text type=\"secondary\">{spec.value}</Text>\n                                      </div>\n                                    </Space>\n                                  </Card>\n                                </Col>\n                              ))}\n                            </Row>\n\n                            <Title level={5}>产品标签</Title>\n                            <Space wrap>\n                              {currentProduct.tags?.map(tag => (\n                                <Tag key={tag} color=\"blue\">{tag}</Tag>\n                              ))}\n                            </Space>\n                          </>\n                        ),\n                      },\n                      {\n                        key: 'configurations',\n                        label: '配置方案',\n                        children: (\n                          <Row gutter={16}>\n                            {productConfigurations.map(config => (\n                              <Col span={12} key={config.id} style={{ marginBottom: 16 }}>\n                                <Card\n                                  size=\"small\"\n                                  title={config.configurationName}\n                                  extra={<Text strong>¥{config.basePrice}</Text>}\n                                >\n                                  <Paragraph ellipsis={{ rows: 2 }}>\n                                    {config.description}\n                                  </Paragraph>\n\n                                  <Space wrap>\n                                    {config.recommendedFor.map(rec => (\n                                      <Tag key={rec}>{rec}</Tag>\n                                    ))}\n                                  </Space>\n\n                                  <Divider />\n\n                                  <List\n                                    size=\"small\"\n                                    dataSource={config.features.slice(0, 4)}\n                                    renderItem={(feature) => (\n                                      <List.Item style={{ padding: '2px 0' }}>\n                                        <Space>\n                                          <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                                          <Text style={{ fontSize: '12px' }}>{feature}</Text>\n                                        </Space>\n                                      </List.Item>\n                                    )}\n                                  />\n\n                                  {config.features.length > 4 && (\n                                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                                      还有 {config.features.length - 4} 项功能...\n                                    </Text>\n                                  )}\n                                </Card>\n                              </Col>\n                            ))}\n                          </Row>\n                        ),\n                      },\n                      {\n                        key: 'support',\n                        label: '技术支持',\n                        children: (\n                          <>\n                            <Alert\n                              message=\"专业技术支持\"\n                              description=\"金蝶提供全方位的技术支持服务，确保您的业务顺利运行\"\n                              type=\"info\"\n                              showIcon\n                              style={{ marginBottom: 16 }}\n                            />\n\n                            <Row gutter={16}>\n                              <Col span={8}>\n                                <Card size=\"small\" title=\"在线支持\">\n                                  <Space direction=\"vertical\">\n                                    <Text>• 工作日 9:00-18:00</Text>\n                                    <Text>• 在线客服响应</Text>\n                                    <Text>• 远程协助</Text>\n                                  </Space>\n                                </Card>\n                              </Col>\n                              <Col span={8}>\n                                <Card size=\"small\" title=\"电话支持\">\n                                  <Space direction=\"vertical\">\n                                    <Text>• 400客服热线</Text>\n                                    <Text>• 技术专家支持</Text>\n                                    <Text>• 紧急问题处理</Text>\n                                  </Space>\n                                </Card>\n                              </Col>\n                              <Col span={8}>\n                                <Card size=\"small\" title=\"培训服务\">\n                                  <Space direction=\"vertical\">\n                                    <Text>• 产品使用培训</Text>\n                                    <Text>• 在线视频教程</Text>\n                                    <Text>• 现场实施指导</Text>\n                                  </Space>\n                                </Card>\n                              </Col>\n                            </Row>\n                          </>\n                        ),\n                      },\n                    ]}\n                  />\n                </Col>\n\n                <Col span={8}>\n                  <PriceCalculator\n                    productSku={currentProduct.sku || ''}\n                    basePrice={currentProduct.basePrice}\n                  />\n                </Col>\n              </Row>\n            </Card>\n          )}\n        </Col>\n      </Row>\n\n      <Modal\n        title=\"产品配置向导\"\n        visible={configModalVisible}\n        onCancel={() => setConfigModalVisible(false)}\n        width={800}\n        footer={null}\n      >\n        <Alert\n          message=\"配置向导\"\n          description=\"请根据您的业务需求选择合适的产品配置\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        {/* 这里可以添加配置向导的具体内容 */}\n        <Text>配置向导功能正在开发中...</Text>\n      </Modal>\n    </div>\n  );\n};\n\nexport default KingsoftProductShowcase;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,IAAI,EAGJC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,SAAS,QACJ,MAAM;AACb,SAEEC,YAAY,EACZC,aAAa,EAEbC,YAAY,EACZC,eAAe,EACfC,mBAAmB,QAEd,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,yBAAyB,EAAEC,sBAAsB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElG,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG1B,UAAU;AAC7C,MAAM;EAAE2B;AAAO,CAAC,GAAGpB,MAAM;AAOzB,MAAMqB,eAA+C,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAS,CAAC;EAElD,MAAM8C,WAAW,GAAGvB,sBAAsB,CACxCa,SAAS,EACTD,UAAU,EACVG,QAAQ,EACRE,YAAY,EACZE,SAAS,EACTK,SAAS,EACTH,QACF,CAAC;EAED,oBACEjB,OAAA,CAAC1B,IAAI;IAAC+C,KAAK,EAAC,gCAAO;IAACC,IAAI,EAAC,OAAO;IAAAC,QAAA,gBAC9BvB,OAAA,CAACf,IAAI;MAACuC,MAAM,EAAC,UAAU;MAAAD,QAAA,gBACrBvB,OAAA,CAACzB,GAAG;QAACkD,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;UAACkD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZvB,OAAA,CAACf,IAAI,CAAC0C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBvB,OAAA,CAACb,WAAW;cACV0C,GAAG,EAAE,CAAE;cACPC,KAAK,EAAEnB,QAAS;cAChBoB,QAAQ,EAAGD,KAAK,IAAKlB,WAAW,CAACkB,KAAK,IAAI,CAAC,CAAE;cAC7CE,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNrC,OAAA,CAACxB,GAAG;UAACkD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZvB,OAAA,CAACf,IAAI,CAAC0C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBvB,OAAA,CAACd,MAAM;cAAC4C,KAAK,EAAEjB,YAAa;cAACkB,QAAQ,EAAEjB,eAAgB;cAAAS,QAAA,gBACrDvB,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA,CAACzB,GAAG;QAACkD,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;UAACkD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZvB,OAAA,CAACf,IAAI,CAAC0C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBvB,OAAA,CAACd,MAAM;cAAC4C,KAAK,EAAEf,SAAU;cAACgB,QAAQ,EAAEf,YAAa;cAAAO,QAAA,gBAC/CvB,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAE,KAAM;gBAAAP,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAE,IAAK;gBAAAP,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNrC,OAAA,CAACxB,GAAG;UAACkD,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZvB,OAAA,CAACf,IAAI,CAAC0C,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAL,QAAA,eACrBvB,OAAA,CAACd,MAAM;cAAC4C,KAAK,EAAEb,QAAS;cAACc,QAAQ,EAAEb,WAAY;cAACoB,UAAU;cAAAf,QAAA,gBACxDvB,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,eAAe;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,SAAS;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCrC,OAAA,CAACM,MAAM;gBAACwB,KAAK,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPrC,OAAA,CAACnB,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXrC,OAAA,CAACzB,GAAG;MAACkD,MAAM,EAAE,EAAG;MAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;QAACkD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXvB,OAAA,CAACX,SAAS;UACRgC,KAAK,EAAC,cAAI;UACVS,KAAK,EAAEX,WAAW,CAACoB,aAAc;UACjCC,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNrC,OAAA,CAACxB,GAAG;QAACkD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXvB,OAAA,CAACX,SAAS;UACRgC,KAAK,EAAC,0BAAM;UACZS,KAAK,EAAEX,WAAW,CAACuB,aAAc;UACjCF,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC,MAAG;UACVE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNrC,OAAA,CAACxB,GAAG;QAACkD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXvB,OAAA,CAACX,SAAS;UACRgC,KAAK,EAAC,0BAAM;UACZS,KAAK,EAAEX,WAAW,CAAC0B,UAAW;UAC9BL,SAAS,EAAE,CAAE;UACbC,MAAM,EAAC,MAAG;UACVE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlB,WAAW,CAAC2B,YAAY,CAACC,MAAM,GAAG,CAAC,iBAClC/C,OAAA,CAAAE,SAAA;MAAAqB,QAAA,gBACEvB,OAAA,CAACnB,OAAO;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXrC,OAAA,CAACI,IAAI;QAAC4C,MAAM;QAAAzB,QAAA,EAAC;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7BrC,OAAA,CAACjB,IAAI;QACHuC,IAAI,EAAC,OAAO;QACZ2B,UAAU,EAAE9B,WAAW,CAAC2B,YAAa;QACrCI,UAAU,EAAGC,IAAI,iBACfnD,OAAA,CAACjB,IAAI,CAAC4C,IAAI;UAAAJ,QAAA,eACRvB,OAAA,CAACpB,KAAK;YAAA2C,QAAA,gBACJvB,OAAA,CAACN,mBAAmB;cAACsC,KAAK,EAAE;gBAAEY,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDrC,OAAA,CAACI,IAAI;cAAAmB,QAAA,EAAE4B,IAAI,CAACC;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBrC,OAAA,CAACtB,GAAG;cAACkE,KAAK,EAAC,OAAO;cAAArB,QAAA,EACf4B,IAAI,CAACE,QAAQ,CAACC,IAAI,KAAK,YAAY,GAAG,GAAGH,IAAI,CAACE,QAAQ,CAACvB,KAAK,KAAK,GAAG,IAAIqB,IAAI,CAACE,QAAQ,CAACvB,KAAK;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACF,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC3B,EAAA,CArHIH,eAA+C;AAAAgD,EAAA,GAA/ChD,eAA+C;AAuHrD,MAAMiD,uBAAiC,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAS,GAAG,CAAC;EACnE,MAAM,CAACyF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM2F,gBAAgB,GAAGrE,YAAY,CAACsE,MAAM,CAACC,CAAC;IAAA,IAAAC,MAAA,EAAAC,OAAA,EAAAC,OAAA;IAAA,OAC5C,EAAAF,MAAA,GAAAD,CAAC,CAACI,GAAG,cAAAH,MAAA,uBAALA,MAAA,CAAOI,QAAQ,CAAC,KAAK,CAAC,OAAAH,OAAA,GAAIF,CAAC,CAACI,GAAG,cAAAF,OAAA,uBAALA,OAAA,CAAOG,QAAQ,CAAC,KAAK,CAAC,OAAAF,OAAA,GAAIH,CAAC,CAACI,GAAG,cAAAD,OAAA,uBAALA,OAAA,CAAOE,QAAQ,CAAC,IAAI,CAAC;EAAA,CAC3E,CAAC;EAED,MAAMC,cAAc,GAAGR,gBAAgB,CAACS,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACQ,EAAE,KAAKd,eAAe,CAAC;EAC3E,MAAMe,qBAAqB,GAAG9E,yBAAyB,CAACoE,MAAM,CAC5DW,MAAM,IAAIA,MAAM,CAACC,SAAS,KAAKjB,eACjC,CAAC;EAED,MAAMkB,sBAAsB,GAAGA,CAAA,KAAM;IACnCf,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMgB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,eAAe,GAAGlF,sBAAsB,CAC5C,MAAM,EACN,YAAY,EACZ,KAAK,EACL,CAAC,MAAM,EAAE,KAAK,CAChB,CAAC;IAEDd,KAAK,CAACiG,IAAI,CAAC;MACT5D,KAAK,EAAE,MAAM;MACbY,KAAK,EAAE,GAAG;MACViD,OAAO,eACLlF,OAAA;QAAAuB,QAAA,gBACEvB,OAAA,CAACK,SAAS;UAAAkB,QAAA,EAAC;QAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,EACxC2C,eAAe,CAACG,GAAG,CAACP,MAAM,iBACzB5E,OAAA,CAAC1B,IAAI;UAAiBgD,IAAI,EAAC,OAAO;UAACU,KAAK,EAAE;YAAEoD,YAAY,EAAE;UAAE,CAAE;UAAA7D,QAAA,gBAC5DvB,OAAA,CAACG,KAAK;YAACkF,KAAK,EAAE,CAAE;YAAA9D,QAAA,EAAEqD,MAAM,CAACU;UAAiB;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnDrC,OAAA,CAACI,IAAI;YAAAmB,QAAA,EAAEqD,MAAM,CAACW;UAAW;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjCrC,OAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrC,OAAA,CAACI,IAAI;YAAC4C,MAAM;YAAAzB,QAAA,GAAC,oBAAK,EAACqD,MAAM,CAACnE,SAAS;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAJlCuC,MAAM,CAACF,EAAE;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKd,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAET,CAAC,CAAC;EACJ,CAAC;EAED,oBACErC,OAAA;IAAKgC,KAAK,EAAE;MAAEwD,OAAO,EAAE;IAAO,CAAE;IAAAjE,QAAA,gBAC9BvB,OAAA,CAACzB,GAAG;MAACkD,MAAM,EAAE,EAAG;MAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;QAACkD,IAAI,EAAE,CAAE;QAAAH,QAAA,eACXvB,OAAA,CAAC1B,IAAI;UAAC+C,KAAK,EAAC,sCAAQ;UAACC,IAAI,EAAC,OAAO;UAAAC,QAAA,eAC/BvB,OAAA,CAACjB,IAAI;YACHkE,UAAU,EAAEe,gBAAiB;YAC7Bd,UAAU,EAAGuC,OAAO;cAAA,IAAAC,aAAA;cAAA,oBAClB1F,OAAA,CAACjB,IAAI,CAAC4C,IAAI;gBACRK,KAAK,EAAE;kBACL2D,MAAM,EAAE,SAAS;kBACjBC,eAAe,EAAEhC,eAAe,KAAK6B,OAAO,CAACf,EAAE,GAAG,SAAS,GAAG,aAAa;kBAC3Ec,OAAO,EAAE,KAAK;kBACdK,YAAY,EAAE;gBAChB,CAAE;gBACFC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC4B,OAAO,CAACf,EAAE,CAAE;gBAAAnD,QAAA,eAE9CvB,OAAA,CAACjB,IAAI,CAAC4C,IAAI,CAACoE,IAAI;kBACb1E,KAAK,EAAEoE,OAAO,CAACrC,IAAK;kBACpBmC,WAAW,eACTvF,OAAA,CAACpB,KAAK;oBAAA2C,QAAA,gBACJvB,OAAA,CAACI,IAAI;sBAACkD,IAAI,EAAC,WAAW;sBAAA/B,QAAA,GAAC,MAAC,EAACkE,OAAO,CAAChF,SAAS;oBAAA;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,GAAAqD,aAAA,GACjDD,OAAO,CAACO,IAAI,cAAAN,aAAA,uBAAZA,aAAA,CAAcP,GAAG,CAACc,GAAG,iBACpBjG,OAAA,CAACtB,GAAG;sBAAA6C,QAAA,EAAY0E;oBAAG,GAATA,GAAG;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;UACZ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENrC,OAAA,CAACxB,GAAG;QAACkD,IAAI,EAAE,EAAG;QAAAH,QAAA,EACXiD,cAAc,iBACbxE,OAAA,CAAC1B,IAAI;UACH+C,KAAK,eACHrB,OAAA,CAACpB,KAAK;YAAA2C,QAAA,gBACJvB,OAAA,CAACT,aAAa;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChBmC,cAAc,CAACpB,IAAI,eACpBpD,OAAA,CAACtB,GAAG;cAACkE,KAAK,EAAC,MAAM;cAAArB,QAAA,EAAEiD,cAAc,CAACF;YAAG;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACR;UACD6D,KAAK,eACHlG,OAAA,CAACpB,KAAK;YAAA2C,QAAA,gBACJvB,OAAA,CAACvB,MAAM;cAAC6E,IAAI,EAAC,SAAS;cAAC6C,IAAI,eAAEnG,OAAA,CAACP,eAAe;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACyD,OAAO,EAAEhB,sBAAuB;cAAAvD,QAAA,EAAC;YAEnF;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrC,OAAA,CAACvB,MAAM;cAAC0H,IAAI,eAAEnG,OAAA,CAACV,YAAY;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACyD,OAAO,EAAEf,oBAAqB;cAAAxD,QAAA,EAAC;YAE/D;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAd,QAAA,eAEDvB,OAAA,CAACzB,GAAG;YAACkD,MAAM,EAAE,EAAG;YAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;cAACkD,IAAI,EAAE,EAAG;cAAAH,QAAA,eACZvB,OAAA,CAAClB,IAAI;gBACHsH,gBAAgB,EAAC,UAAU;gBAC3BC,KAAK,EAAE,CACL;kBACEC,GAAG,EAAE,UAAU;kBACf1E,KAAK,EAAE,MAAM;kBACbL,QAAQ,eACNvB,OAAA,CAAAE,SAAA;oBAAAqB,QAAA,gBACEvB,OAAA,CAACK,SAAS;sBAAAkB,QAAA,EAAEiD,cAAc,CAACe;oBAAW;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAEnDrC,OAAA,CAACG,KAAK;sBAACkF,KAAK,EAAE,CAAE;sBAAA9D,QAAA,EAAC;oBAAI;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7BrC,OAAA,CAACzB,GAAG;sBAACkD,MAAM,EAAE,EAAG;sBAAAF,QAAA,GAAAmC,qBAAA,GACbc,cAAc,CAAC+B,cAAc,cAAA7C,qBAAA,uBAA7BA,qBAAA,CAA+ByB,GAAG,CAAC,CAACqB,IAAI,EAAEC,KAAK,kBAC9CzG,OAAA,CAACxB,GAAG;wBAACkD,IAAI,EAAE,EAAG;wBAAaM,KAAK,EAAE;0BAAEoD,YAAY,EAAE;wBAAG,CAAE;wBAAA7D,QAAA,eACrDvB,OAAA,CAAC1B,IAAI;0BAACgD,IAAI,EAAC,OAAO;0BAAAC,QAAA,eAChBvB,OAAA,CAACpB,KAAK;4BAAA2C,QAAA,gBACJvB,OAAA,CAACR,YAAY;8BAAA0C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAChBrC,OAAA;8BAAAuB,QAAA,gBACEvB,OAAA,CAACI,IAAI;gCAAC4C,MAAM;gCAAAzB,QAAA,EAAEiF,IAAI,CAACpD;8BAAI;gCAAAlB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eAC/BrC,OAAA;gCAAAkC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eACNrC,OAAA,CAACI,IAAI;gCAACkD,IAAI,EAAC,WAAW;gCAAA/B,QAAA,EAAEiF,IAAI,CAAC1E;8BAAK;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC,GAVWoE,KAAK;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWpB,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAENrC,OAAA,CAACG,KAAK;sBAACkF,KAAK,EAAE,CAAE;sBAAA9D,QAAA,EAAC;oBAAI;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7BrC,OAAA,CAACpB,KAAK;sBAAC8H,IAAI;sBAAAnF,QAAA,GAAAoC,oBAAA,GACRa,cAAc,CAACwB,IAAI,cAAArC,oBAAA,uBAAnBA,oBAAA,CAAqBwB,GAAG,CAACc,GAAG,iBAC3BjG,OAAA,CAACtB,GAAG;wBAAWkE,KAAK,EAAC,MAAM;wBAAArB,QAAA,EAAE0E;sBAAG,GAAtBA,GAAG;wBAAA/D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAyB,CACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA,eACR;gBAEN,CAAC,EACD;kBACEiE,GAAG,EAAE,gBAAgB;kBACrB1E,KAAK,EAAE,MAAM;kBACbL,QAAQ,eACNvB,OAAA,CAACzB,GAAG;oBAACkD,MAAM,EAAE,EAAG;oBAAAF,QAAA,EACboD,qBAAqB,CAACQ,GAAG,CAACP,MAAM,iBAC/B5E,OAAA,CAACxB,GAAG;sBAACkD,IAAI,EAAE,EAAG;sBAAiBM,KAAK,EAAE;wBAAEoD,YAAY,EAAE;sBAAG,CAAE;sBAAA7D,QAAA,eACzDvB,OAAA,CAAC1B,IAAI;wBACHgD,IAAI,EAAC,OAAO;wBACZD,KAAK,EAAEuD,MAAM,CAACU,iBAAkB;wBAChCY,KAAK,eAAElG,OAAA,CAACI,IAAI;0BAAC4C,MAAM;0BAAAzB,QAAA,GAAC,MAAC,EAACqD,MAAM,CAACnE,SAAS;wBAAA;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAE;wBAAAd,QAAA,gBAE/CvB,OAAA,CAACK,SAAS;0BAACsG,QAAQ,EAAE;4BAAEC,IAAI,EAAE;0BAAE,CAAE;0BAAArF,QAAA,EAC9BqD,MAAM,CAACW;wBAAW;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eAEZrC,OAAA,CAACpB,KAAK;0BAAC8H,IAAI;0BAAAnF,QAAA,EACRqD,MAAM,CAACiC,cAAc,CAAC1B,GAAG,CAAC2B,GAAG,iBAC5B9G,OAAA,CAACtB,GAAG;4BAAA6C,QAAA,EAAYuF;0BAAG,GAATA,GAAG;4BAAA5E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC,eAERrC,OAAA,CAACnB,OAAO;0BAAAqD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAEXrC,OAAA,CAACjB,IAAI;0BACHuC,IAAI,EAAC,OAAO;0BACZ2B,UAAU,EAAE2B,MAAM,CAACmC,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;0BACxC9D,UAAU,EAAG+D,OAAO,iBAClBjH,OAAA,CAACjB,IAAI,CAAC4C,IAAI;4BAACK,KAAK,EAAE;8BAAEwD,OAAO,EAAE;4BAAQ,CAAE;4BAAAjE,QAAA,eACrCvB,OAAA,CAACpB,KAAK;8BAAA2C,QAAA,gBACJvB,OAAA,CAACN,mBAAmB;gCAACsC,KAAK,EAAE;kCAAEY,KAAK,EAAE;gCAAU;8BAAE;gCAAAV,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eACpDrC,OAAA,CAACI,IAAI;gCAAC4B,KAAK,EAAE;kCAAEkF,QAAQ,EAAE;gCAAO,CAAE;gCAAA3F,QAAA,EAAE0F;8BAAO;gCAAA/E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBACX;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAEDuC,MAAM,CAACmC,QAAQ,CAAChE,MAAM,GAAG,CAAC,iBACzB/C,OAAA,CAACI,IAAI;0BAACkD,IAAI,EAAC,WAAW;0BAACtB,KAAK,EAAE;4BAAEkF,QAAQ,EAAE;0BAAO,CAAE;0BAAA3F,QAAA,GAAC,eAC/C,EAACqD,MAAM,CAACmC,QAAQ,CAAChE,MAAM,GAAG,CAAC,EAAC,wBACjC;wBAAA;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG;oBAAC,GApCWuC,MAAM,CAACF,EAAE;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAqCxB,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAET,CAAC,EACD;kBACEiE,GAAG,EAAE,SAAS;kBACd1E,KAAK,EAAE,MAAM;kBACbL,QAAQ,eACNvB,OAAA,CAAAE,SAAA;oBAAAqB,QAAA,gBACEvB,OAAA,CAACZ,KAAK;sBACJ+H,OAAO,EAAC,sCAAQ;sBAChB5B,WAAW,EAAC,wJAA2B;sBACvCjC,IAAI,EAAC,MAAM;sBACX8D,QAAQ;sBACRpF,KAAK,EAAE;wBAAEoD,YAAY,EAAE;sBAAG;oBAAE;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC,eAEFrC,OAAA,CAACzB,GAAG;sBAACkD,MAAM,EAAE,EAAG;sBAAAF,QAAA,gBACdvB,OAAA,CAACxB,GAAG;wBAACkD,IAAI,EAAE,CAAE;wBAAAH,QAAA,eACXvB,OAAA,CAAC1B,IAAI;0BAACgD,IAAI,EAAC,OAAO;0BAACD,KAAK,EAAC,0BAAM;0BAAAE,QAAA,eAC7BvB,OAAA,CAACpB,KAAK;4BAACyI,SAAS,EAAC,UAAU;4BAAA9F,QAAA,gBACzBvB,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAgB;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAC7BrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAM;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA,CAACxB,GAAG;wBAACkD,IAAI,EAAE,CAAE;wBAAAH,QAAA,eACXvB,OAAA,CAAC1B,IAAI;0BAACgD,IAAI,EAAC,OAAO;0BAACD,KAAK,EAAC,0BAAM;0BAAAE,QAAA,eAC7BvB,OAAA,CAACpB,KAAK;4BAACyI,SAAS,EAAC,UAAU;4BAAA9F,QAAA,gBACzBvB,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAS;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACtBrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNrC,OAAA,CAACxB,GAAG;wBAACkD,IAAI,EAAE,CAAE;wBAAAH,QAAA,eACXvB,OAAA,CAAC1B,IAAI;0BAACgD,IAAI,EAAC,OAAO;0BAACD,KAAK,EAAC,0BAAM;0BAAAE,QAAA,eAC7BvB,OAAA,CAACpB,KAAK;4BAACyI,SAAS,EAAC,UAAU;4BAAA9F,QAAA,gBACzBvB,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACrBrC,OAAA,CAACI,IAAI;8BAAAmB,QAAA,EAAC;4BAAQ;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,eACN;gBAEN,CAAC;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrC,OAAA,CAACxB,GAAG;cAACkD,IAAI,EAAE,CAAE;cAAAH,QAAA,eACXvB,OAAA,CAACO,eAAe;gBACdC,UAAU,EAAEgE,cAAc,CAACF,GAAG,IAAI,EAAG;gBACrC7D,SAAS,EAAE+D,cAAc,CAAC/D;cAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrC,OAAA,CAAChB,KAAK;MACJqC,KAAK,EAAC,sCAAQ;MACdiG,OAAO,EAAExD,kBAAmB;MAC5ByD,QAAQ,EAAEA,CAAA,KAAMxD,qBAAqB,CAAC,KAAK,CAAE;MAC7C9B,KAAK,EAAE,GAAI;MACXuF,MAAM,EAAE,IAAK;MAAAjG,QAAA,gBAEbvB,OAAA,CAACZ,KAAK;QACJ+H,OAAO,EAAC,0BAAM;QACd5B,WAAW,EAAC,8GAAoB;QAChCjC,IAAI,EAAC,MAAM;QACX8D,QAAQ;QACRpF,KAAK,EAAE;UAAEoD,YAAY,EAAE;QAAG;MAAE;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFrC,OAAA,CAACI,IAAI;QAAAmB,QAAA,EAAC;MAAc;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACoB,GAAA,CA1QID,uBAAiC;AAAAiE,GAAA,GAAjCjE,uBAAiC;AA4QvC,eAAeA,uBAAuB;AAAC,IAAAD,EAAA,EAAAkE,GAAA;AAAAC,YAAA,CAAAnE,EAAA;AAAAmE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}