{"ast": null, "code": "export function matchesType(targetType, draggedItemType) {\n  if (draggedItemType === null) {\n    return targetType === null;\n  }\n  return Array.isArray(targetType) ? targetType.some(t => t === draggedItemType) : targetType === draggedItemType;\n}", "map": {"version": 3, "names": ["matchesType", "targetType", "draggedItemType", "Array", "isArray", "some", "t"], "sources": ["/Users/<USER>/Desktop/Link_CPQ/node_modules/dnd-core/src/utils/matchesType.ts"], "sourcesContent": ["import type { Identifier } from '../interfaces.js'\n\nexport function matchesType(\n\ttargetType: Identifier | Identifier[] | null,\n\tdraggedItemType: Identifier | null,\n): boolean {\n\tif (draggedItemType === null) {\n\t\treturn targetType === null\n\t}\n\treturn Array.isArray(targetType)\n\t\t? (targetType as Identifier[]).some((t) => t === draggedItemType)\n\t\t: targetType === draggedItemType\n}\n"], "mappings": "AAEA,OAAO,SAASA,WAAWA,CAC1BC,UAA4C,EAC5CC,eAAkC,EACxB;EACV,IAAIA,eAAe,KAAK,IAAI,EAAE;IAC7B,OAAOD,UAAU,KAAK,IAAI;;EAE3B,OAAOE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAC7BA,UAAW,CAAkBI,IAAI,CAAEC,CAAC,IAAKA,CAAC,KAAKJ,eAAe,CAAC,GAC/DD,UAAU,KAAKC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}