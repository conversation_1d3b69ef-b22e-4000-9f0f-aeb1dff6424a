{"version": 3, "file": "static/css/main.1b0c55ba.css", "mappings": "AACA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,6LAMF,CAEA,KACE,uEACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,YACE,gBACF,CAEA,YACE,eAAgB,CAChB,8BAAwC,CACxC,YACF,CAEA,WACE,eAAgB,CAChB,8BACF,CAEA,aACE,kBAAmB,CACnB,6BAA8B,CAC9B,YACF,CAGA,cACE,eAAgB,CAChB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,6BACE,YACF,CAGA,YACE,eAAgB,CAChB,iBACF,CAEA,mCACE,kBAAmB,CACnB,eACF,CAGA,gBACE,eAAgB,CAEhB,iBAAkB,CAClB,8BAAwC,CAFxC,YAGF,CAEA,cAIE,4BAA6B,CAF7B,eAAgB,CAChB,gBAAiB,CAFjB,gBAIF,CAEA,uBACE,eACF,CAGA,WACE,kDAA6D,CAE7D,iBAAkB,CADlB,UAAW,CAGX,kBAAmB,CADnB,YAEF,CAEA,uBACE,cAAe,CAEf,iBAAkB,CADlB,UAEF,CAEA,uBACE,cAAe,CACf,eAAiB,CACjB,iBACF,CAEA,wBACE,cAAe,CACf,UACF,CAGA,wBACE,YAAa,CAEb,QAAS,CADT,0BAEF,CAEA,sBAKE,eAAgB,CAJhB,WAKF,CAEA,2CANE,eAAgB,CAChB,iBAAkB,CAClB,8BAWF,CAPA,qBACE,QAAO,CAKP,eAAgB,CADhB,iBAEF,CAEA,yBAEE,eAAgB,CAChB,iBAAkB,CAClB,8BAAwC,CACxC,eAAgB,CAJhB,WAKF,CAGA,WAGE,iDAAoD,CADpD,WAAY,CADZ,UAGF,CAGA,gBACE,WAAY,CACZ,uBACF,CAEA,sBAEE,+BAA0C,CAD1C,0BAEF,CAEA,WACE,yBAA0B,CAC1B,iBAAkB,CAClB,YAAa,CACb,iBAAkB,CAClB,uBACF,CAEA,kBAEE,wBAAyB,CADzB,oBAEF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CAGb,YAAa,CAFb,sBAGF,CAGA,aAGE,UAAW,CADX,iBAAkB,CADlB,iBAGF,CAEA,yBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,0BACE,cAAe,CACf,iBACF,CAEA,gCACE,cAAe,CACf,kBACF,CAGA,yBACE,aACE,YACF,CAEA,wBACE,qBAAsB,CACtB,WACF,CAEA,+CAGE,kBAAmB,CADnB,UAEF,CAEA,qBACE,YACF,CACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,SACE,6BACF,CAEA,mBACE,GACE,2BACF,CACA,GACE,uBACF,CACF,CAEA,UACE,8BACF,CAGA,gDACE,cACF,CAEA,wBACE,kCACF,CAEA,8BACE,8BACF,CAGA,gBACE,eACF,CAGA,YACE,kBAAmB,CACnB,cAAe,CACf,eACF,CAEA,mBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,qBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,kBACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF", "sources": ["index.css"], "sourcesContent": ["/* 全局样式 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',\n    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',\n    'Segoe UI Emoji', 'Segoe UI Symbol';\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;\n}\n\n/* 自定义滚动条 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 布局样式 */\n.app-layout {\n  min-height: 100vh;\n}\n\n.app-header {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n}\n\n.app-sider {\n  background: #fff;\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\n}\n\n.app-content {\n  background: #f5f5f5;\n  min-height: calc(100vh - 64px);\n  padding: 24px;\n}\n\n/* 卡片样式 */\n.content-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-bottom: 24px;\n}\n\n.content-card .ant-card-body {\n  padding: 24px;\n}\n\n/* 表格样式 */\n.data-table {\n  background: #fff;\n  border-radius: 8px;\n}\n\n.data-table .ant-table-thead > tr > th {\n  background: #fafafa;\n  font-weight: 600;\n}\n\n/* 表单样式 */\n.form-container {\n  background: #fff;\n  padding: 24px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.form-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 24px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.form-actions .ant-btn {\n  margin-left: 8px;\n}\n\n/* 统计卡片 */\n.stat-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 16px;\n}\n\n.stat-card .stat-title {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-bottom: 8px;\n}\n\n.stat-card .stat-value {\n  font-size: 32px;\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n.stat-card .stat-change {\n  font-size: 12px;\n  opacity: 0.9;\n}\n\n/* 配置器样式 */\n.configurator-container {\n  display: flex;\n  height: calc(100vh - 120px);\n  gap: 16px;\n}\n\n.configurator-sidebar {\n  width: 300px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow-y: auto;\n}\n\n.configurator-canvas {\n  flex: 1;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.configurator-properties {\n  width: 320px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow-y: auto;\n}\n\n/* 3D查看器样式 */\n.viewer-3d {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(45deg, #f0f2f5, #e6f7ff);\n}\n\n/* 拖拽样式 */\n.draggable-item {\n  cursor: move;\n  transition: all 0.3s ease;\n}\n\n.draggable-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.drop-zone {\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  padding: 24px;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.drop-zone.active {\n  border-color: #1890ff;\n  background-color: #f0f8ff;\n}\n\n/* 加载样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n}\n\n/* 空状态样式 */\n.empty-state {\n  text-align: center;\n  padding: 48px 24px;\n  color: #999;\n}\n\n.empty-state .empty-icon {\n  font-size: 64px;\n  margin-bottom: 16px;\n  opacity: 0.3;\n}\n\n.empty-state .empty-title {\n  font-size: 16px;\n  margin-bottom: 8px;\n}\n\n.empty-state .empty-description {\n  font-size: 14px;\n  margin-bottom: 24px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .app-content {\n    padding: 16px;\n  }\n  \n  .configurator-container {\n    flex-direction: column;\n    height: auto;\n  }\n  \n  .configurator-sidebar,\n  .configurator-properties {\n    width: 100%;\n    margin-bottom: 16px;\n  }\n  \n  .configurator-canvas {\n    height: 400px;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n/* 自定义组件样式 */\n.ant-layout-sider-collapsed .ant-menu-item-icon {\n  font-size: 16px;\n}\n\n.ant-menu-item-selected {\n  background-color: #e6f7ff !important;\n}\n\n.ant-menu-item-selected::after {\n  border-right: 3px solid #1890ff;\n}\n\n/* 工具提示样式 */\n.custom-tooltip {\n  max-width: 300px;\n}\n\n/* 标签样式 */\n.status-tag {\n  border-radius: 12px;\n  font-size: 12px;\n  padding: 2px 8px;\n}\n\n.status-tag.active {\n  background-color: #f6ffed;\n  border-color: #b7eb8f;\n  color: #52c41a;\n}\n\n.status-tag.inactive {\n  background-color: #fff2e8;\n  border-color: #ffbb96;\n  color: #fa8c16;\n}\n\n.status-tag.draft {\n  background-color: #f0f0f0;\n  border-color: #d9d9d9;\n  color: #666;\n}\n"], "names": [], "sourceRoot": ""}