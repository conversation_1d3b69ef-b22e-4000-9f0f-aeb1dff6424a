{"name": "link-cpq-frontend", "version": "1.0.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@react-three/drei": "^9.88.0", "@react-three/fiber": "^8.15.0", "@reduxjs/toolkit": "^1.9.7", "@types/file-saver": "^2.0.6", "@types/lodash": "^4.14.199", "@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-beautiful-dnd": "^13.1.5", "@types/react-dom": "^18.2.11", "@types/react-syntax-highlighter": "^15.5.8", "@types/three": "^0.157.0", "@types/uuid": "^9.0.5", "antd": "^5.10.0", "axios": "^1.5.0", "dayjs": "^1.11.10", "fabric": "^5.3.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "konva": "^9.2.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-konva": "^18.2.10", "react-markdown": "^9.0.0", "react-redux": "^8.1.3", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.2", "three": "^0.157.0", "typescript": "^5.2.2", "uuid": "^9.0.1", "web-vitals": "^3.4.0"}, "scripts": {"start": "PORT=3340 craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@types/jest": "^29.5.5", "craco-alias": "^3.0.1", "prettier": "^3.0.3"}}